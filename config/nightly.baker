{
  name: 'ipaymentapp-nightly',
  working_directory: '..',

  metadata: {
    info_plist_path: 'ipaymentapp/ipaymentapp/Info.plist',
    build_metadata_plist_path: 'ipaymentapp/ipaymentapp/DefaultAppInfo.plist',
    bump_bundle_version: false,
    bundle_display_name: `date +%m%d%H%M | tr -d '\n'`,
    bundle_identifier: 'com.meituan.ipaymentapp'
  },

  archive: {
    workspace_path: 'ipaymentapp.xcworkspace',
    scheme: 'ipaymentapp',
    configuration: 'DailyBuild',
    archive_path: 'baker-tmp/archive.xcarchive',
    ipa_path: 'baker-tmp/package.ipa'
  },

  inhouse_distribution: {
    app_group: 'ipaymentapp-nightly',
    ipa_path: 'baker-tmp/package.ipa'
  }
}
