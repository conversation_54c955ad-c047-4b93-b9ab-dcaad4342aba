{
    name: 'iPayment-release',
    working_directory: '..',

    metadata: {
        info_plist_path: 'ipaymentapp/ipaymentapp/Info.plist',
        build_metadata_plist_path: 'ipaymentapp/ipaymentapp/DefaultAppInfo.plist',
        bump_bundle_version: true,
        bundle_display_name: '美团支付',
        bundle_identifier: 'com.meituan.ipayment'
    },

    archive: {
        workspace_path: 'ipaymentapp.xcworkspace',
        scheme: 'ipaymentapp',
        configuration: 'Release',
        archive_path: 'baker-tmp/archive.xcarchive',
        ipa_path: 'baker-tmp/package.ipa'
    },

    inhouse_distribution: {
        app_group: 'payment',
        released: true
    }
}
