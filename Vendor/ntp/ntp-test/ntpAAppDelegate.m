/*╔══════════════════════════════════════════════════════════════════════════════════════════════════╗
 ║ ntpAAppDelegate.m                                                                                ║
 ║                                                                                                  ║
 ║ Created by <PERSON> on Nov16/10                                                               ║
 ║ Copyright © 2010 Ramsay Consulting. All rights reserved.                                         ║
 ╚══════════════════════════════════════════════════════════════════════════════════════════════════╝*/

#import "ntpAAppDelegate.h"
#import "ios-ntp.h"

@implementation ntpAAppDelegate

@synthesize window;
@synthesize viewController;

- (BOOL)application:(UIApplication *) app didFinishLaunchingWithOptions:(NSDictionary *) options {
    
    [NetworkClock sharedNetworkClock];                      // gather up the ntp servers ...
    
    //[window addSubview:viewController.view];
    [window makeKeyAndVisible];
    /*┌──────────────────────────────────────────────────────────────────────────────────────────────────┐
     │  Create a timer that will fire in ten seconds and then every ten seconds thereafter to ask the   │
     │ network clock what time it is.                                                                   │
     └──────────────────────────────────────────────────────────────────────────────────────────────────┘*/
    NSTimer * repeatingTimer = [[NSTimer alloc]
                                initWithFireDate:[NSDate dateWithTimeIntervalSinceNow:1.0]
                                interval:1.0 target:self selector:@selector(repeatingMethod:)
                                userInfo:nil repeats:YES];
    
    [[NSRunLoop currentRunLoop] addTimer:repeatingTimer forMode:NSDefaultRunLoopMode];
    return YES;
}

- (void)repeatingMethod:(NSTimer *) theTimer {
    systemTime = [NSDate date];
    networkTime = [NSDate threadsafeNetworkDate];
    
    
    NSDateFormatter *formatter =[[NSDateFormatter alloc] init];
    [formatter setTimeZone:[NSTimeZone localTimeZone]];
    [formatter setDateFormat:@"MM月dd日/hh:mm:ss ms毫秒"];
    NSString* sysDate = [formatter stringFromDate:systemTime];
    NSString* webDate = [formatter stringFromDate:networkTime];
    
    sysClockLabel.text = sysDate;
    netClockLabel.text = webDate;
    differenceLabel.text = [NSString stringWithFormat:@"%5.3f",
                            [networkTime timeIntervalSinceDate:systemTime]];
    
}

- (void)applicationWillTerminate:(UIApplication *)application {
    
    [[NetworkClock sharedNetworkClock] finishAssociations];   // be nice and let all the servers go ...
}

@end