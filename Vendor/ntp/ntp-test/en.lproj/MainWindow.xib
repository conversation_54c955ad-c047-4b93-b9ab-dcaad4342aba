<?xml version="1.0" encoding="UTF-8"?>
<archive type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="7.10">
	<data>
		<int key="IBDocument.SystemTarget">1056</int>
		<string key="IBDocument.SystemVersion">10J567</string>
		<string key="IBDocument.InterfaceBuilderVersion">823</string>
		<string key="IBDocument.AppKitVersion">1038.35</string>
		<string key="IBDocument.HIToolboxVersion">462.00</string>
		<object class="NSMutableDictionary" key="IBDocument.PluginVersions">
			<string key="NS.key.0">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
			<string key="NS.object.0">132</string>
		</object>
		<object class="NSMutableArray" key="IBDocument.EditedObjectIDs">
			<bool key="EncodedWithXMLCoder">YES</bool>
			<integer value="12"/>
		</object>
		<object class="NSArray" key="IBDocument.PluginDependencies">
			<bool key="EncodedWithXMLCoder">YES</bool>
			<string>com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
		</object>
		<object class="NSMutableDictionary" key="IBDocument.Metadata">
			<bool key="EncodedWithXMLCoder">YES</bool>
			<object class="NSArray" key="dict.sortedKeys" id="0">
				<bool key="EncodedWithXMLCoder">YES</bool>
			</object>
			<object class="NSMutableArray" key="dict.values">
				<bool key="EncodedWithXMLCoder">YES</bool>
			</object>
		</object>
		<object class="NSMutableArray" key="IBDocument.RootObjects" id="1000">
			<bool key="EncodedWithXMLCoder">YES</bool>
			<object class="IBProxyObject" id="841351856">
				<string key="IBProxiedObjectIdentifier">IBFilesOwner</string>
				<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
			</object>
			<object class="IBProxyObject" id="427554174">
				<string key="IBProxiedObjectIdentifier">IBFirstResponder</string>
				<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
			</object>
			<object class="IBUICustomObject" id="664661524">
				<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
			</object>
			<object class="IBUIWindow" id="117978783">
				<reference key="NSNextResponder"/>
				<int key="NSvFlags">292</int>
				<object class="NSMutableArray" key="NSSubviews">
					<bool key="EncodedWithXMLCoder">YES</bool>
					<object class="IBUILabel" id="921954110">
						<reference key="NSNextResponder" ref="117978783"/>
						<int key="NSvFlags">292</int>
						<string key="NSFrame">{{20, 201}, {90, 21}}</string>
						<reference key="NSSuperview" ref="117978783"/>
						<bool key="IBUIOpaque">NO</bool>
						<bool key="IBUIClipsSubviews">YES</bool>
						<int key="IBUIContentMode">7</int>
						<bool key="IBUIUserInteractionEnabled">NO</bool>
						<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
						<string key="IBUIText">System Clock:</string>
						<object class="NSFont" key="IBUIFont" id="156605046">
							<string key="NSName">Helvetica</string>
							<double key="NSSize">13</double>
							<int key="NSfFlags">16</int>
						</object>
						<object class="NSColor" key="IBUITextColor" id="69250563">
							<int key="NSColorSpace">1</int>
							<bytes key="NSRGB">MCAwIDAAA</bytes>
						</object>
						<object class="NSColor" key="IBUIHighlightedColor" id="319256690">
							<int key="NSColorSpace">3</int>
							<bytes key="NSWhite">MQA</bytes>
						</object>
						<int key="IBUIBaselineAdjustment">1</int>
						<float key="IBUIMinimumFontSize">10</float>
						<int key="IBUITextAlignment">2</int>
					</object>
					<object class="IBUILabel" id="179607">
						<reference key="NSNextResponder" ref="117978783"/>
						<int key="NSvFlags">292</int>
						<string key="NSFrame">{{116, 201}, {185, 21}}</string>
						<reference key="NSSuperview" ref="117978783"/>
						<bool key="IBUIOpaque">NO</bool>
						<bool key="IBUIClipsSubviews">YES</bool>
						<int key="IBUIContentMode">7</int>
						<bool key="IBUIUserInteractionEnabled">NO</bool>
						<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
						<string key="IBUIText">sysClock</string>
						<reference key="IBUIFont" ref="156605046"/>
						<reference key="IBUITextColor" ref="69250563"/>
						<reference key="IBUIHighlightedColor" ref="319256690"/>
						<int key="IBUIBaselineAdjustment">1</int>
						<float key="IBUIMinimumFontSize">10</float>
					</object>
					<object class="IBUILabel" id="217747933">
						<reference key="NSNextResponder" ref="117978783"/>
						<int key="NSvFlags">292</int>
						<string key="NSFrame">{{20, 230}, {90, 21}}</string>
						<reference key="NSSuperview" ref="117978783"/>
						<bool key="IBUIOpaque">NO</bool>
						<bool key="IBUIClipsSubviews">YES</bool>
						<int key="IBUIContentMode">7</int>
						<bool key="IBUIUserInteractionEnabled">NO</bool>
						<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
						<string key="IBUIText">Network Clock:</string>
						<reference key="IBUIFont" ref="156605046"/>
						<reference key="IBUITextColor" ref="69250563"/>
						<reference key="IBUIHighlightedColor" ref="319256690"/>
						<int key="IBUIBaselineAdjustment">1</int>
						<float key="IBUIMinimumFontSize">10</float>
						<int key="IBUITextAlignment">2</int>
					</object>
					<object class="IBUILabel" id="838288147">
						<reference key="NSNextResponder" ref="117978783"/>
						<int key="NSvFlags">292</int>
						<string key="NSFrame">{{20, 259}, {172, 21}}</string>
						<reference key="NSSuperview" ref="117978783"/>
						<bool key="IBUIOpaque">NO</bool>
						<bool key="IBUIClipsSubviews">YES</bool>
						<int key="IBUIContentMode">7</int>
						<bool key="IBUIUserInteractionEnabled">NO</bool>
						<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
						<string key="IBUIText">Network ahead by (seconds):</string>
						<reference key="IBUIFont" ref="156605046"/>
						<reference key="IBUITextColor" ref="69250563"/>
						<reference key="IBUIHighlightedColor" ref="319256690"/>
						<int key="IBUIBaselineAdjustment">1</int>
						<float key="IBUIMinimumFontSize">10</float>
					</object>
					<object class="IBUILabel" id="113254413">
						<reference key="NSNextResponder" ref="117978783"/>
						<int key="NSvFlags">292</int>
						<string key="NSFrame">{{116, 230}, {185, 21}}</string>
						<reference key="NSSuperview" ref="117978783"/>
						<bool key="IBUIOpaque">NO</bool>
						<bool key="IBUIClipsSubviews">YES</bool>
						<int key="IBUIContentMode">7</int>
						<bool key="IBUIUserInteractionEnabled">NO</bool>
						<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
						<string key="IBUIText">netClock</string>
						<reference key="IBUIFont" ref="156605046"/>
						<reference key="IBUITextColor" ref="69250563"/>
						<reference key="IBUIHighlightedColor" ref="319256690"/>
						<int key="IBUIBaselineAdjustment">1</int>
						<float key="IBUIMinimumFontSize">10</float>
					</object>
					<object class="IBUILabel" id="494009492">
						<reference key="NSNextResponder" ref="117978783"/>
						<int key="NSvFlags">292</int>
						<string key="NSFrame">{{200, 259}, {73, 21}}</string>
						<reference key="NSSuperview" ref="117978783"/>
						<bool key="IBUIOpaque">NO</bool>
						<bool key="IBUIClipsSubviews">YES</bool>
						<int key="IBUIContentMode">7</int>
						<bool key="IBUIUserInteractionEnabled">NO</bool>
						<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
						<string key="IBUIText">diffMillis</string>
						<reference key="IBUIFont" ref="156605046"/>
						<reference key="IBUITextColor" ref="69250563"/>
						<reference key="IBUIHighlightedColor" ref="319256690"/>
						<int key="IBUIBaselineAdjustment">1</int>
						<float key="IBUIMinimumFontSize">10</float>
					</object>
				</object>
				<string key="NSFrameSize">{320, 480}</string>
				<reference key="NSSuperview"/>
				<object class="NSColor" key="IBUIBackgroundColor">
					<int key="NSColorSpace">1</int>
					<bytes key="NSRGB">MSAxIDEAA</bytes>
				</object>
				<bool key="IBUIOpaque">NO</bool>
				<bool key="IBUIClearsContextBeforeDrawing">NO</bool>
				<object class="IBUISimulatedStatusBarMetrics" key="IBUISimulatedStatusBarMetrics"/>
				<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
				<bool key="IBUIResizesToFullScreen">YES</bool>
			</object>
		</object>
		<object class="IBObjectContainer" key="IBDocument.Objects">
			<object class="NSMutableArray" key="connectionRecords">
				<bool key="EncodedWithXMLCoder">YES</bool>
				<object class="IBConnectionRecord">
					<object class="IBCocoaTouchOutletConnection" key="connection">
						<string key="label">delegate</string>
						<reference key="source" ref="841351856"/>
						<reference key="destination" ref="664661524"/>
					</object>
					<int key="connectionID">4</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBCocoaTouchOutletConnection" key="connection">
						<string key="label">window</string>
						<reference key="source" ref="664661524"/>
						<reference key="destination" ref="117978783"/>
					</object>
					<int key="connectionID">14</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBCocoaTouchOutletConnection" key="connection">
						<string key="label">sysClockLabel</string>
						<reference key="source" ref="664661524"/>
						<reference key="destination" ref="179607"/>
					</object>
					<int key="connectionID">29</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBCocoaTouchOutletConnection" key="connection">
						<string key="label">netClockLabel</string>
						<reference key="source" ref="664661524"/>
						<reference key="destination" ref="113254413"/>
					</object>
					<int key="connectionID">30</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBCocoaTouchOutletConnection" key="connection">
						<string key="label">differenceLabel</string>
						<reference key="source" ref="664661524"/>
						<reference key="destination" ref="494009492"/>
					</object>
					<int key="connectionID">31</int>
				</object>
			</object>
			<object class="IBMutableOrderedSet" key="objectRecords">
				<object class="NSArray" key="orderedObjects">
					<bool key="EncodedWithXMLCoder">YES</bool>
					<object class="IBObjectRecord">
						<int key="objectID">0</int>
						<reference key="object" ref="0"/>
						<reference key="children" ref="1000"/>
						<nil key="parent"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">-1</int>
						<reference key="object" ref="841351856"/>
						<reference key="parent" ref="0"/>
						<string key="objectName">File's Owner</string>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">3</int>
						<reference key="object" ref="664661524"/>
						<reference key="parent" ref="0"/>
						<string key="objectName">ntpA App Delegate</string>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">-2</int>
						<reference key="object" ref="427554174"/>
						<reference key="parent" ref="0"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">12</int>
						<reference key="object" ref="117978783"/>
						<object class="NSMutableArray" key="children">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<reference ref="838288147"/>
							<reference ref="494009492"/>
							<reference ref="179607"/>
							<reference ref="113254413"/>
							<reference ref="921954110"/>
							<reference ref="217747933"/>
						</object>
						<reference key="parent" ref="0"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">16</int>
						<reference key="object" ref="921954110"/>
						<reference key="parent" ref="117978783"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">18</int>
						<reference key="object" ref="217747933"/>
						<reference key="parent" ref="117978783"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">19</int>
						<reference key="object" ref="838288147"/>
						<reference key="parent" ref="117978783"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">21</int>
						<reference key="object" ref="494009492"/>
						<reference key="parent" ref="117978783"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">20</int>
						<reference key="object" ref="113254413"/>
						<reference key="parent" ref="117978783"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">17</int>
						<reference key="object" ref="179607"/>
						<reference key="parent" ref="117978783"/>
					</object>
				</object>
			</object>
			<object class="NSMutableDictionary" key="flattenedProperties">
				<bool key="EncodedWithXMLCoder">YES</bool>
				<object class="NSArray" key="dict.sortedKeys">
					<bool key="EncodedWithXMLCoder">YES</bool>
					<string>-1.CustomClassName</string>
					<string>-2.CustomClassName</string>
					<string>12.IBEditorWindowLastContentRect</string>
					<string>12.IBPluginDependency</string>
					<string>16.IBPluginDependency</string>
					<string>16.IBViewBoundsToFrameTransform</string>
					<string>17.IBPluginDependency</string>
					<string>17.IBViewBoundsToFrameTransform</string>
					<string>18.IBPluginDependency</string>
					<string>18.IBViewBoundsToFrameTransform</string>
					<string>19.IBPluginDependency</string>
					<string>19.IBViewBoundsToFrameTransform</string>
					<string>20.IBPluginDependency</string>
					<string>20.IBViewBoundsToFrameTransform</string>
					<string>21.IBPluginDependency</string>
					<string>21.IBViewBoundsToFrameTransform</string>
					<string>3.CustomClassName</string>
					<string>3.IBPluginDependency</string>
				</object>
				<object class="NSMutableArray" key="dict.values">
					<bool key="EncodedWithXMLCoder">YES</bool>
					<string>UIApplication</string>
					<string>UIResponder</string>
					<string>{{636, 341}, {320, 480}}</string>
					<string>com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
					<string>com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
					<object class="NSAffineTransform">
						<bytes key="NSTransformStruct">P4AAAL+AAABBqAAAw1wAAA</bytes>
					</object>
					<string>com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
					<object class="NSAffineTransform">
						<bytes key="NSTransformStruct">P4AAAL+AAABC6AAAw1QAAA</bytes>
					</object>
					<string>com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
					<object class="NSAffineTransform">
						<bytes key="NSTransformStruct">P4AAAL+AAABBoAAAw3kAAA</bytes>
					</object>
					<string>com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
					<object class="NSAffineTransform">
						<bytes key="NSTransformStruct">P4AAAL+AAABByAAAwpIAAA</bytes>
					</object>
					<string>com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
					<object class="NSAffineTransform">
						<bytes key="NSTransformStruct">P4AAAL+AAABC6AAAw3EAAA</bytes>
					</object>
					<string>com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
					<object class="NSAffineTransform">
						<bytes key="NSTransformStruct">P4AAAL+AAABBoAAAw6OAAA</bytes>
					</object>
					<string>ntpAAppDelegate</string>
					<string>com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
				</object>
			</object>
			<object class="NSMutableDictionary" key="unlocalizedProperties">
				<bool key="EncodedWithXMLCoder">YES</bool>
				<reference key="dict.sortedKeys" ref="0"/>
				<object class="NSMutableArray" key="dict.values">
					<bool key="EncodedWithXMLCoder">YES</bool>
				</object>
			</object>
			<nil key="activeLocalization"/>
			<object class="NSMutableDictionary" key="localizations">
				<bool key="EncodedWithXMLCoder">YES</bool>
				<reference key="dict.sortedKeys" ref="0"/>
				<object class="NSMutableArray" key="dict.values">
					<bool key="EncodedWithXMLCoder">YES</bool>
				</object>
			</object>
			<nil key="sourceID"/>
			<int key="maxID">31</int>
		</object>
		<object class="IBClassDescriber" key="IBDocument.Classes">
			<object class="NSMutableArray" key="referencedPartialClassDescriptions">
				<bool key="EncodedWithXMLCoder">YES</bool>
				<object class="IBPartialClassDescription">
					<string key="className">UIWindow</string>
					<string key="superclassName">UIView</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBUserSource</string>
						<string key="minorKey"/>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">ntpAAppDelegate</string>
					<string key="superclassName">NSObject</string>
					<object class="NSMutableDictionary" key="outlets">
						<bool key="EncodedWithXMLCoder">YES</bool>
						<object class="NSArray" key="dict.sortedKeys">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<string>differenceLabel</string>
							<string>netClockLabel</string>
							<string>sysClockLabel</string>
							<string>viewController</string>
							<string>window</string>
						</object>
						<object class="NSMutableArray" key="dict.values">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<string>UILabel</string>
							<string>UILabel</string>
							<string>UILabel</string>
							<string>ntpAViewController</string>
							<string>UIWindow</string>
						</object>
					</object>
					<object class="NSMutableDictionary" key="toOneOutletInfosByName">
						<bool key="EncodedWithXMLCoder">YES</bool>
						<object class="NSArray" key="dict.sortedKeys">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<string>differenceLabel</string>
							<string>netClockLabel</string>
							<string>sysClockLabel</string>
							<string>viewController</string>
							<string>window</string>
						</object>
						<object class="NSMutableArray" key="dict.values">
							<bool key="EncodedWithXMLCoder">YES</bool>
							<object class="IBToOneOutletInfo">
								<string key="name">differenceLabel</string>
								<string key="candidateClassName">UILabel</string>
							</object>
							<object class="IBToOneOutletInfo">
								<string key="name">netClockLabel</string>
								<string key="candidateClassName">UILabel</string>
							</object>
							<object class="IBToOneOutletInfo">
								<string key="name">sysClockLabel</string>
								<string key="candidateClassName">UILabel</string>
							</object>
							<object class="IBToOneOutletInfo">
								<string key="name">viewController</string>
								<string key="candidateClassName">ntpAViewController</string>
							</object>
							<object class="IBToOneOutletInfo">
								<string key="name">window</string>
								<string key="candidateClassName">UIWindow</string>
							</object>
						</object>
					</object>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBProjectSource</string>
						<string key="minorKey">Classes/ntpAAppDelegate.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">ntpAAppDelegate</string>
					<string key="superclassName">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBUserSource</string>
						<string key="minorKey"/>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">ntpAViewController</string>
					<string key="superclassName">UIViewController</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBProjectSource</string>
						<string key="minorKey">Classes/ntpAViewController.h</string>
					</object>
				</object>
			</object>
			<object class="NSMutableArray" key="referencedPartialClassDescriptionsV3.2+">
				<bool key="EncodedWithXMLCoder">YES</bool>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">Foundation.framework/Headers/NSError.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">Foundation.framework/Headers/NSFileManager.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">Foundation.framework/Headers/NSKeyValueCoding.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">Foundation.framework/Headers/NSKeyValueObserving.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">Foundation.framework/Headers/NSKeyedArchiver.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">Foundation.framework/Headers/NSObject.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">Foundation.framework/Headers/NSRunLoop.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">Foundation.framework/Headers/NSThread.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">Foundation.framework/Headers/NSURL.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">Foundation.framework/Headers/NSURLConnection.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">UIKit.framework/Headers/UIAccessibility.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">UIKit.framework/Headers/UINibLoading.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier" id="356479594">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">UIKit.framework/Headers/UIResponder.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">UIApplication</string>
					<string key="superclassName">UIResponder</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">UIKit.framework/Headers/UIApplication.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">UILabel</string>
					<string key="superclassName">UIView</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">UIKit.framework/Headers/UILabel.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">UIResponder</string>
					<string key="superclassName">NSObject</string>
					<reference key="sourceIdentifier" ref="356479594"/>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">UISearchBar</string>
					<string key="superclassName">UIView</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">UIKit.framework/Headers/UISearchBar.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">UISearchDisplayController</string>
					<string key="superclassName">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">UIKit.framework/Headers/UISearchDisplayController.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">UIView</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">UIKit.framework/Headers/UIPrintFormatter.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">UIView</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">UIKit.framework/Headers/UITextField.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">UIView</string>
					<string key="superclassName">UIResponder</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">UIKit.framework/Headers/UIView.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">UIViewController</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">UIKit.framework/Headers/UINavigationController.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">UIViewController</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">UIKit.framework/Headers/UIPopoverController.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">UIViewController</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">UIKit.framework/Headers/UISplitViewController.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">UIViewController</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">UIKit.framework/Headers/UITabBarController.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">UIViewController</string>
					<string key="superclassName">UIResponder</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">UIKit.framework/Headers/UIViewController.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">UIWindow</string>
					<string key="superclassName">UIView</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBFrameworkSource</string>
						<string key="minorKey">UIKit.framework/Headers/UIWindow.h</string>
					</object>
				</object>
			</object>
		</object>
		<int key="IBDocument.localizationMode">0</int>
		<string key="IBDocument.TargetRuntimeIdentifier">IBCocoaTouchFramework</string>
		<object class="NSMutableDictionary" key="IBDocument.PluginDeclaredDependencyDefaults">
			<string key="NS.key.0">com.apple.InterfaceBuilder.CocoaTouchPlugin.iPhoneOS</string>
			<integer value="1056" key="NS.object.0"/>
		</object>
		<object class="NSMutableDictionary" key="IBDocument.PluginDeclaredDevelopmentDependencies">
			<string key="NS.key.0">com.apple.InterfaceBuilder.CocoaTouchPlugin.InterfaceBuilder3</string>
			<integer value="3100" key="NS.object.0"/>
		</object>
		<bool key="IBDocument.PluginDeclaredDependenciesTrackSystemTargetVersion">YES</bool>
		<string key="IBDocument.LastKnownRelativeProjectPath">../ntpA.xcodeproj</string>
		<int key="IBDocument.defaultPropertyAccessControl">3</int>
		<string key="IBCocoaTouchPluginVersion">132</string>
	</data>
</archive>
