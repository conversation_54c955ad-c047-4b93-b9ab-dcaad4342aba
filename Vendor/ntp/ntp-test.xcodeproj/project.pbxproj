// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		701283131963D02B00758ABE /* ntp.hosts in Resources */ = {isa = PBXBuildFile; fileRef = 701283121963D02B00758ABE /* ntp.hosts */; };
		70BBD1E619629ED5005F1BD4 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 70BBD1E519629ED5005F1BD4 /* Foundation.framework */; };
		70BBD1E819629ED5005F1BD4 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 70BBD1E719629ED5005F1BD4 /* CoreGraphics.framework */; };
		70BBD1EA19629ED5005F1BD4 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 70BBD1E919629ED5005F1BD4 /* UIKit.framework */; };
		70BBD1F019629ED5005F1BD4 /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = 70BBD1EE19629ED5005F1BD4 /* InfoPlist.strings */; };
		70BBD1F219629ED5005F1BD4 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 70BBD1F119629ED5005F1BD4 /* main.m */; };
		70BBD1F819629ED5005F1BD4 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 70BBD1F719629ED5005F1BD4 /* Images.xcassets */; };
		70BBD1FF19629ED5005F1BD4 /* XCTest.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 70BBD1FE19629ED5005F1BD4 /* XCTest.framework */; };
		70BBD20019629ED5005F1BD4 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 70BBD1E519629ED5005F1BD4 /* Foundation.framework */; };
		70BBD20119629ED5005F1BD4 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 70BBD1E919629ED5005F1BD4 /* UIKit.framework */; };
		70BBD21619629F59005F1BD4 /* ntpAAppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 70BBD21519629F59005F1BD4 /* ntpAAppDelegate.m */; };
		70BBD22019629F6B005F1BD4 /* NetAssociation.m in Sources */ = {isa = PBXBuildFile; fileRef = 70BBD21A19629F6B005F1BD4 /* NetAssociation.m */; };
		70BBD22119629F6B005F1BD4 /* NetworkClock.m in Sources */ = {isa = PBXBuildFile; fileRef = 70BBD21C19629F6B005F1BD4 /* NetworkClock.m */; };
		70BBD22219629F6B005F1BD4 /* NSDate+NetworkClock.m in Sources */ = {isa = PBXBuildFile; fileRef = 70BBD21E19629F6B005F1BD4 /* NSDate+NetworkClock.m */; };
		70BBD22619629F7F005F1BD4 /* GCDAsyncUdpSocket.m in Sources */ = {isa = PBXBuildFile; fileRef = 70BBD22519629F7F005F1BD4 /* GCDAsyncUdpSocket.m */; };
		70BBD2291962A0BF005F1BD4 /* MainWindow.xib in Resources */ = {isa = PBXBuildFile; fileRef = 70BBD2271962A0BF005F1BD4 /* MainWindow.xib */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		70BBD20219629ED5005F1BD4 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 70BBD1DA19629ED5005F1BD4 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 70BBD1E119629ED5005F1BD4;
			remoteInfo = "ntp-test";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		701283121963D02B00758ABE /* ntp.hosts */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = ntp.hosts; sourceTree = "<group>"; };
		70BBD1E219629ED5005F1BD4 /* ntp-test.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "ntp-test.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		70BBD1E519629ED5005F1BD4 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		70BBD1E719629ED5005F1BD4 /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		70BBD1E919629ED5005F1BD4 /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = System/Library/Frameworks/UIKit.framework; sourceTree = SDKROOT; };
		70BBD1ED19629ED5005F1BD4 /* ntp-test-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "ntp-test-Info.plist"; sourceTree = "<group>"; };
		70BBD1EF19629ED5005F1BD4 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		70BBD1F119629ED5005F1BD4 /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		70BBD1F319629ED5005F1BD4 /* ntp-test-Prefix.pch */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "ntp-test-Prefix.pch"; sourceTree = "<group>"; };
		70BBD1F719629ED5005F1BD4 /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Images.xcassets; sourceTree = "<group>"; };
		70BBD1FD19629ED5005F1BD4 /* ntp-testTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "ntp-testTests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		70BBD1FE19629ED5005F1BD4 /* XCTest.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = XCTest.framework; path = Library/Frameworks/XCTest.framework; sourceTree = DEVELOPER_DIR; };
		70BBD21419629F59005F1BD4 /* ntpAAppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ntpAAppDelegate.h; sourceTree = "<group>"; };
		70BBD21519629F59005F1BD4 /* ntpAAppDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ntpAAppDelegate.m; sourceTree = "<group>"; };
		70BBD21819629F6B005F1BD4 /* ios-ntp.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "ios-ntp.h"; sourceTree = "<group>"; };
		70BBD21919629F6B005F1BD4 /* NetAssociation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = NetAssociation.h; sourceTree = "<group>"; };
		70BBD21A19629F6B005F1BD4 /* NetAssociation.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = NetAssociation.m; sourceTree = "<group>"; };
		70BBD21B19629F6B005F1BD4 /* NetworkClock.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = NetworkClock.h; sourceTree = "<group>"; };
		70BBD21C19629F6B005F1BD4 /* NetworkClock.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = NetworkClock.m; sourceTree = "<group>"; };
		70BBD21D19629F6B005F1BD4 /* NSDate+NetworkClock.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSDate+NetworkClock.h"; sourceTree = "<group>"; };
		70BBD21E19629F6B005F1BD4 /* NSDate+NetworkClock.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSDate+NetworkClock.m"; sourceTree = "<group>"; };
		70BBD22419629F7F005F1BD4 /* GCDAsyncUdpSocket.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = GCDAsyncUdpSocket.h; path = ../../Pods/CocoaAsyncSocket/GCD/GCDAsyncUdpSocket.h; sourceTree = "<group>"; };
		70BBD22519629F7F005F1BD4 /* GCDAsyncUdpSocket.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = GCDAsyncUdpSocket.m; path = ../../Pods/CocoaAsyncSocket/GCD/GCDAsyncUdpSocket.m; sourceTree = "<group>"; };
		70BBD2281962A0BF005F1BD4 /* en */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = en; path = en.lproj/MainWindow.xib; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		70BBD1DF19629ED5005F1BD4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				70BBD1E819629ED5005F1BD4 /* CoreGraphics.framework in Frameworks */,
				70BBD1EA19629ED5005F1BD4 /* UIKit.framework in Frameworks */,
				70BBD1E619629ED5005F1BD4 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		70BBD1FA19629ED5005F1BD4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				70BBD1FF19629ED5005F1BD4 /* XCTest.framework in Frameworks */,
				70BBD20119629ED5005F1BD4 /* UIKit.framework in Frameworks */,
				70BBD20019629ED5005F1BD4 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		701283111963D02B00758ABE /* Resources */ = {
			isa = PBXGroup;
			children = (
				701283121963D02B00758ABE /* ntp.hosts */,
			);
			path = Resources;
			sourceTree = SOURCE_ROOT;
		};
		70BBD1D919629ED5005F1BD4 = {
			isa = PBXGroup;
			children = (
				70BBD22419629F7F005F1BD4 /* GCDAsyncUdpSocket.h */,
				70BBD22519629F7F005F1BD4 /* GCDAsyncUdpSocket.m */,
				70BBD1EB19629ED5005F1BD4 /* ntp-test */,
				70BBD1E419629ED5005F1BD4 /* Frameworks */,
				70BBD1E319629ED5005F1BD4 /* Products */,
			);
			sourceTree = "<group>";
		};
		70BBD1E319629ED5005F1BD4 /* Products */ = {
			isa = PBXGroup;
			children = (
				70BBD1E219629ED5005F1BD4 /* ntp-test.app */,
				70BBD1FD19629ED5005F1BD4 /* ntp-testTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		70BBD1E419629ED5005F1BD4 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				70BBD1E519629ED5005F1BD4 /* Foundation.framework */,
				70BBD1E719629ED5005F1BD4 /* CoreGraphics.framework */,
				70BBD1E919629ED5005F1BD4 /* UIKit.framework */,
				70BBD1FE19629ED5005F1BD4 /* XCTest.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		70BBD1EB19629ED5005F1BD4 /* ntp-test */ = {
			isa = PBXGroup;
			children = (
				701283111963D02B00758ABE /* Resources */,
				70BBD21719629F6B005F1BD4 /* Classes */,
				70BBD21419629F59005F1BD4 /* ntpAAppDelegate.h */,
				70BBD21519629F59005F1BD4 /* ntpAAppDelegate.m */,
				70BBD1F719629ED5005F1BD4 /* Images.xcassets */,
				70BBD1EC19629ED5005F1BD4 /* Supporting Files */,
			);
			path = "ntp-test";
			sourceTree = "<group>";
		};
		70BBD1EC19629ED5005F1BD4 /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				70BBD2271962A0BF005F1BD4 /* MainWindow.xib */,
				70BBD1ED19629ED5005F1BD4 /* ntp-test-Info.plist */,
				70BBD1EE19629ED5005F1BD4 /* InfoPlist.strings */,
				70BBD1F119629ED5005F1BD4 /* main.m */,
				70BBD1F319629ED5005F1BD4 /* ntp-test-Prefix.pch */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		70BBD21719629F6B005F1BD4 /* Classes */ = {
			isa = PBXGroup;
			children = (
				70BBD21819629F6B005F1BD4 /* ios-ntp.h */,
				70BBD21919629F6B005F1BD4 /* NetAssociation.h */,
				70BBD21A19629F6B005F1BD4 /* NetAssociation.m */,
				70BBD21B19629F6B005F1BD4 /* NetworkClock.h */,
				70BBD21C19629F6B005F1BD4 /* NetworkClock.m */,
				70BBD21D19629F6B005F1BD4 /* NSDate+NetworkClock.h */,
				70BBD21E19629F6B005F1BD4 /* NSDate+NetworkClock.m */,
			);
			path = Classes;
			sourceTree = SOURCE_ROOT;
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		70BBD1E119629ED5005F1BD4 /* ntp-test */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 70BBD20E19629ED5005F1BD4 /* Build configuration list for PBXNativeTarget "ntp-test" */;
			buildPhases = (
				70BBD1DE19629ED5005F1BD4 /* Sources */,
				70BBD1DF19629ED5005F1BD4 /* Frameworks */,
				70BBD1E019629ED5005F1BD4 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "ntp-test";
			productName = "ntp-test";
			productReference = 70BBD1E219629ED5005F1BD4 /* ntp-test.app */;
			productType = "com.apple.product-type.application";
		};
		70BBD1FC19629ED5005F1BD4 /* ntp-testTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 70BBD21119629ED5005F1BD4 /* Build configuration list for PBXNativeTarget "ntp-testTests" */;
			buildPhases = (
				70BBD1F919629ED5005F1BD4 /* Sources */,
				70BBD1FA19629ED5005F1BD4 /* Frameworks */,
				70BBD1FB19629ED5005F1BD4 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				70BBD20319629ED5005F1BD4 /* PBXTargetDependency */,
			);
			name = "ntp-testTests";
			productName = "ntp-testTests";
			productReference = 70BBD1FD19629ED5005F1BD4 /* ntp-testTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		70BBD1DA19629ED5005F1BD4 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0510;
				ORGANIZATIONNAME = zjs;
				TargetAttributes = {
					70BBD1FC19629ED5005F1BD4 = {
						TestTargetID = 70BBD1E119629ED5005F1BD4;
					};
				};
			};
			buildConfigurationList = 70BBD1DD19629ED5005F1BD4 /* Build configuration list for PBXProject "ntp-test" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
			);
			mainGroup = 70BBD1D919629ED5005F1BD4;
			productRefGroup = 70BBD1E319629ED5005F1BD4 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				70BBD1E119629ED5005F1BD4 /* ntp-test */,
				70BBD1FC19629ED5005F1BD4 /* ntp-testTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		70BBD1E019629ED5005F1BD4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				70BBD2291962A0BF005F1BD4 /* MainWindow.xib in Resources */,
				70BBD1F019629ED5005F1BD4 /* InfoPlist.strings in Resources */,
				701283131963D02B00758ABE /* ntp.hosts in Resources */,
				70BBD1F819629ED5005F1BD4 /* Images.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		70BBD1FB19629ED5005F1BD4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		70BBD1DE19629ED5005F1BD4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				70BBD1F219629ED5005F1BD4 /* main.m in Sources */,
				70BBD21619629F59005F1BD4 /* ntpAAppDelegate.m in Sources */,
				70BBD22219629F6B005F1BD4 /* NSDate+NetworkClock.m in Sources */,
				70BBD22019629F6B005F1BD4 /* NetAssociation.m in Sources */,
				70BBD22619629F7F005F1BD4 /* GCDAsyncUdpSocket.m in Sources */,
				70BBD22119629F6B005F1BD4 /* NetworkClock.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		70BBD1F919629ED5005F1BD4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		70BBD20319629ED5005F1BD4 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 70BBD1E119629ED5005F1BD4 /* ntp-test */;
			targetProxy = 70BBD20219629ED5005F1BD4 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		70BBD1EE19629ED5005F1BD4 /* InfoPlist.strings */ = {
			isa = PBXVariantGroup;
			children = (
				70BBD1EF19629ED5005F1BD4 /* en */,
			);
			name = InfoPlist.strings;
			sourceTree = "<group>";
		};
		70BBD2271962A0BF005F1BD4 /* MainWindow.xib */ = {
			isa = PBXVariantGroup;
			children = (
				70BBD2281962A0BF005F1BD4 /* en */,
			);
			name = MainWindow.xib;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		70BBD20C19629ED5005F1BD4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 7.1;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		70BBD20D19629ED5005F1BD4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 7.1;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		70BBD20F19629ED5005F1BD4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "ntp-test/ntp-test-Prefix.pch";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
					IOS_NTP_TEST,
				);
				INFOPLIST_FILE = "ntp-test/ntp-test-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 6.0;
				PRODUCT_NAME = "$(TARGET_NAME)";
				WRAPPER_EXTENSION = app;
			};
			name = Debug;
		};
		70BBD21019629ED5005F1BD4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "ntp-test/ntp-test-Prefix.pch";
				INFOPLIST_FILE = "ntp-test/ntp-test-Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 6.0;
				PRODUCT_NAME = "$(TARGET_NAME)";
				WRAPPER_EXTENSION = app;
			};
			name = Release;
		};
		70BBD21219629ED5005F1BD4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(BUILT_PRODUCTS_DIR)/ntp-test.app/ntp-test";
				FRAMEWORK_SEARCH_PATHS = (
					"$(SDKROOT)/Developer/Library/Frameworks",
					"$(inherited)",
					"$(DEVELOPER_FRAMEWORKS_DIR)",
				);
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "ntp-test/ntp-test-Prefix.pch";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = "ntp-testTests/ntp-testTests-Info.plist";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUNDLE_LOADER)";
				WRAPPER_EXTENSION = xctest;
			};
			name = Debug;
		};
		70BBD21319629ED5005F1BD4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(BUILT_PRODUCTS_DIR)/ntp-test.app/ntp-test";
				FRAMEWORK_SEARCH_PATHS = (
					"$(SDKROOT)/Developer/Library/Frameworks",
					"$(inherited)",
					"$(DEVELOPER_FRAMEWORKS_DIR)",
				);
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "ntp-test/ntp-test-Prefix.pch";
				INFOPLIST_FILE = "ntp-testTests/ntp-testTests-Info.plist";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUNDLE_LOADER)";
				WRAPPER_EXTENSION = xctest;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		70BBD1DD19629ED5005F1BD4 /* Build configuration list for PBXProject "ntp-test" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				70BBD20C19629ED5005F1BD4 /* Debug */,
				70BBD20D19629ED5005F1BD4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		70BBD20E19629ED5005F1BD4 /* Build configuration list for PBXNativeTarget "ntp-test" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				70BBD20F19629ED5005F1BD4 /* Debug */,
				70BBD21019629ED5005F1BD4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		70BBD21119629ED5005F1BD4 /* Build configuration list for PBXNativeTarget "ntp-testTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				70BBD21219629ED5005F1BD4 /* Debug */,
				70BBD21319629ED5005F1BD4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 70BBD1DA19629ED5005F1BD4 /* Project object */;
}
