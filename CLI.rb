require 'git'
require 'cocoapods'
require 'Thor'

module Payment

	class CLI < Thor
		desc "autoRelease ./config/autorelease.baker", "run script to autoRlease 6 SDKs of payment"
		option :config, :type => :string, :required => true, :aliases => '-c'
		def autoRelease
			spec = self.read_config(options[:config])
      payment_auto_release = PaymentAutoRelease.new spec
      payment_auto_release.release
		end

		no_commands do
      def read_config(config_path)
        return unless config_path

        code = File.read config_path
        begin
          spec = eval(code, TOPLEVEL_BINDING, options[:config])
        rescue SyntaxError, Exception => e
          warn "Invalid build configuration in [#{config_path}]: #{e}"
        end

        # Use environment variable to override config inside spec
        spec[:account] ||= {}
        spec[:account][:username] = ENV['MEITUAN_ACCOUNT'] if ENV['MEITUAN_ACCOUNT']
        spec[:account][:password] = ENV['MEITUAN_PASSWORD'] if ENV['MEITUAN_PASSWORD']

        Dir::chdir(File.dirname(config_path))
        if spec[:working_directory]
          Dir::chdir(spec[:working_directory])
        end

        puts "Working directory: #{File.absolute_path '.'}"

        return spec
      end
    end
	end

	class PaymentAutoRelease
		def initialize(spec = nil)
			if spec
				@auto_release_configuration = spec[:auto_release]
				@task_ID = spec[:task_ID]
				@is_merge_dev_branch = spec[:merge_dev]
				@only_update_changelog = spec[:only_update_changelog]
				current_time = Time.now.to_i
				@repo_path_prefix = "/tmp/#{current_time}/"
				@main_branch = (spec[:main_branch] or 'master' )
				@dev_branch = (spec[:dev_branch] or 'development')
			end
		end

		def release
			release_process('SAKPaymentKit', 'ssh://*******************/ios/sakpaymentkit.git')
			release_process('SAKMeituanPay', 'ssh://*******************/ios/sakmeituanpay.git')
			release_process('SAKWalletService', 'ssh://*******************/payios/sakwalletservice.git')
            release_process('SAKPaymentAccount', 'ssh://*******************/payios/sakpaymentaccount.git')
			release_process('SAKPaymentChannel', 'ssh://*******************/ios/sakpaymentchannel.git')
			release_process('SAKBarcodeCashier', 'ssh://*******************/ios/sakbarcodecashier.git')
			release_process('SAKCashier', 'ssh://*******************/ios/sakcashier.git')
			release_process('SAKWallet', 'ssh://*******************/ios/sakwallet.git')
            release_process('SAKPaymentGuard', 'ssh://*******************/payios/sakpaymentguard.git')
		end

		def release_process(repo_name, repo_address)
			currentRepoInfo = @auto_release_configuration.select { |k,v| k == repo_name.to_sym } [repo_name.to_sym]
			if !currentRepoInfo
				return
			end

			is_release = currentRepoInfo[:release]
			if !is_release && !is_release.nil?
				return
			end

			release_branch = currentRepoInfo[:branch]
			tag_name = currentRepoInfo[:tag]
			dependence_info = currentRepoInfo[:dependence]
			repo_path = @repo_path_prefix + repo_name
			puts "####     ####     ####     ####     ####     #### ####     ####     ####     ####     ####     ####"
			puts "#{repo_name}(#{repo_address}) start release_process"
			puts "release_branch is #{release_branch}                                       <<<"
			puts "tag_name is #{tag_name}                                                   <<<"
			puts "dependence_info is #{dependence_info}                                     <<<"
			puts "workspace_path is #{repo_path}                                            <<<"
			puts "####     ####     ####     ####     ####     #### ####     ####     ####     ####     ####     ####"

			clone_branch(repo_address, repo_path)

			if @only_update_changelog
				updateChangeLog(repo_path, release_branch, tag_name, dependence_info)
				return
			end

			add_branch(repo_path, @dev_branch, release_branch)
			check_podfile_config(repo_path, release_branch, repo_name)
			update_version(repo_path, repo_name, release_branch, tag_name)

			if dependence_info
				update_dependence(repo_path, repo_name, release_branch, dependence_info)
			end
			updateChangeLog(repo_path, release_branch, tag_name, dependence_info)
			merge_branch(repo_path, @main_branch, release_branch)
			if @is_merge_dev_branch
				merge_branch(repo_path, @dev_branch, release_branch)
			end
			add_tag(repo_path, @main_branch, tag_name, repo_name, repo_address)
		end

		def clone_branch(repo_address, repo_path)
			puts "clone #{repo_address} to #{repo_path}                               <<<"
			Git.clone(repo_address, repo_path)
		end

		def add_branch(repo_path, origin_branch, new_branch)
			if exist_branch(repo_path, new_branch)
				return
			end
			g = Git.open(repo_path)
			if origin_branch
				g.checkout(origin_branch)
			end
			puts "from #{g.current_branch} checkout new branch  #{new_branch}                                   <<<"
			g.branch(new_branch).checkout
			g.push('origin', g.current_branch)
		end

		def exist_branch(repo_path, branch)
			g = Git.open(repo_path)
			has_branch = false
			g.branches.map do |b|
				if b.to_s.include?(branch)
					has_branch = true
					break
				end
			end
			return has_branch
		end

		def update_version(repo_path, repo_name, release_branch, tag_name)
			g = Git.open(repo_path)
			g.checkout(release_branch)
			run("sed -i \"\" \"s/s.version          = \\\"*.*.*\\\"/s.version          = \\\"#{tag_name}\\\"/g\" #{repo_path}/#{repo_name}.podspec")
			g.add
			commit_message = "#{@task_ID},update #{repo_name} version to #{tag_name}"
			if g.status.changed.empty?
				puts"update_version nothing changed"
				return
			end
			g.commit(commit_message)
			g.push('origin', g.current_branch)
		end

		def update_dependence(repo_path, repo_name, release_branch, dependence_info)
			g = Git.open(repo_path)
			g.checkout(release_branch)
			commit_message = "#{@task_ID},update dependency repository -- "
			dependence_info.map do |k, v|
				from_restrain = '~>'
				to_restrain = '~>'
				repo_dependency = v
				repo_info = v
				if repo_info.index("_")
					from_restrain = repo_info.split(/_/).at(0)
					to_restrain = repo_info.split(/_/).at(1)
					repo_dependency = repo_info.split(/_/).at(2)
				end
				run("sed -i \"\" \"s/'#{k.to_s}',\ '#{from_restrain} *.*.*'/'#{k.to_s}',\ '#{to_restrain} #{repo_dependency}'/g\" #{repo_path}/#{repo_name}.podspec")
				commit_message = commit_message + "#{k.to_s} version to #{v}, "
			end
			if g.status.changed.empty?
				puts "commit_message: #{commit_message}"
				puts "update_dependence nothing changed"
				return
			end

			g.add
			g.commit(commit_message)
			g.push('origin', g.current_branch)
		end

		def merge_branch(repo_path, origin_branch, merged_branch)
			g = Git.open(repo_path)
			g.checkout(origin_branch)
			g.merge(merged_branch)
			g.push('origin', g.current_branch)
			puts "merge #{merged_branch} to #{origin_branch}                  <<<"
		end

		def add_tag(repo_path, target_branch, tag_name, repo_name, repo_address)
			g = Git.open(repo_path)
			g.checkout(target_branch)
			begin
				g.add_tag(tag_name)
			rescue
				puts "#{tag_name} 已经存在，不重复打 tag"
			end
			g.push('origin', "refs/tags/#{tag_name}")
			puts "#{target_branch} add a tag - #{tag_name}                    <<<"

			# 发布新的版本信息到美团的specs
			release_spec_for_MT(repo_path, target_branch, tag_name, repo_address)
		end

		def release_spec_for_MT(repo_path, target_branch, tag_name, repo_address)
			repo_name = repo_path.split('/').last
			spces_repos_name = pod_repo_push_spec('ssh://*******************/ios/specs.git')
			puts "remote slaveX spec's name is #{repo_name}                                 <<< "

			puts "=============================== binary buid and push spec, begins ================= "
			run("gem cleanup sakpodsharper")
			run("sakpodsharper --version")
			run("rm -rf sandbox.SAKPodSharper* || true")
			run("rm -rf ~/Library/Caches/CocoaPods/Pods/Specs || true")
			run("bundle exec pod repo update sankuai-specs || bundle exec pod repo update sankuai-ios-specs || true")
			run("bundle exec pod repo update sankuai-binaryspecs || bundle exec pod repo update sankuai-ios-binaryspecs || true")
			run("sakpodsharper -p #{repo_name} -g #{repo_address} -o #{tag_name}")
			puts "=============================== binary buid and push spec, ends ================= "

			if spces_repos_name
				puts "=============================== start release #{repo_name} specs ==============================="
				run("bundle exec pod repo push #{spces_repos_name} #{repo_path}/#{repo_name}.podspec --allow-warnings --use-libraries --sources=ssh://*******************/ios/binaryspecs.git,ssh://*******************/ios/specs.git --verbose")
				puts "=============================== end release #{repo_name} specs ==============================="
			end
		end

		def pod_repo_push_spec(repo_url)
			repo = get_specs_repos.select {|r| r[:url]==repo_url} [0]
			unless repo
				puts "Error repo URL: #{repo_url}"
				exit false
			end
			repo_name = repo[:name]
		end

		def get_specs_repos()
			::Pod::SourcesManager.all.map do |s|
				Hash[:name => s.name, :path => s.repo.to_s, :url => s.url]
			end
		end

		def check_podfile_config(repo_path, release_branch, repo_name)
			g = Git.open(repo_path)
			g.checkout(release_branch)
			podfilePath = "#{repo_path}/Podfile"
			temporaryPodfilePath = "#{repo_path}/TemporaryPodfile"
			if check_podfile(podfilePath, temporaryPodfilePath)
				puts "====== check podfile finish ======="
				IO.foreach(podfilePath) {
					|sentence|
					puts sentence
				}
			end
			g.add
			if g.status.changed.empty?
				puts"#{repo_name}'s check_podfile_config nothing changed"
				return
			else
				puts"#{repo_name}'s check_podfile_config done"
			end
			commit_message = "#{@task_ID},remove #{repo_name}'s podfile payment repo config"
			g.commit(commit_message)
			g.push('origin', g.current_branch)
		end

		def check_podfile(podfilePath, temporaryPodfilePath)
			projectConfigSection = false
			lineNo = 0
			f = File.new(podfilePath)
			f.each {
				|sentence|

				if sentence.index("target") && !sentence.index("Tests")
					projectConfigSection = true
				end
				if projectConfigSection && sentence.index("end")
					projectConfigSection = false
				end

				if projectConfigSection
					[":git =>", "'~>", "'>", "'>=", "'="].map {
						|pattern|

						if sentence.index(pattern) && !sentence.index("OHHTTPStubs")
							lineNo = f.lineno
							break
						end
					}
				end

				if lineNo != 0
					break
				end
			}
			f.close

			if lineNo != 0
				run("sed '#{lineNo}d' #{podfilePath} > #{temporaryPodfilePath}")
				run("mv #{temporaryPodfilePath} #{podfilePath}")
				check_podfile(podfilePath, temporaryPodfilePath)
			else
				return true
			end
		end

		def updateChangeLog(repo_path, target_branch, tag_name, dependence_info)
			g = Git.open(repo_path)
			g.checkout(target_branch)

			changelog_path = repo_path + "/CHANGELOG.md"
			temp_changelog_path = repo_path + "/tempchangelog.md"
			temp_bugfix_path = repo_path + "/tempbugfix.md"
			temp_feature_path = repo_path + "/tempfeature.md"

			temp_bugfix_file = File.open("#{temp_bugfix_path}", 'w')
			temp_feature_file = File.open("#{temp_feature_path}", 'w')
			g.log(1000).between("origin/#{@main_branch}", "origin/#{target_branch}").each {
				|commit|
				puts "<<<< commit and commit message: #{commit} #{commit.message}"
				if commit.message.index("[bugfix]") && !commit.message.index("Merge pull request")
					puts ">>>>> bugfix message is: #{commit.message.split("[bugfix]")[1]}"
					temp_bugfix_file << "#{commit.message.split("[bugfix]")[1]}\n"
				elsif commit.message.index("[feature]") && !commit.message.index("Merge pull request")
					puts ">>>>> feature message is: #{commit.message.split("[feature]")[1]}"
					temp_feature_file << "#{commit.message.split("[feature]")[1]}\n"
				end
			}

			puts "++++++ commit message [feature] size: #{temp_feature_file.size}"
			puts "++++++ commit message [bugfix] size: #{temp_bugfix_file.size}"

			temp_bugfix_file.close
			temp_feature_file.close


			temp_changelog_file = File.open("#{temp_changelog_path}", 'w')
			changelog_file = File.new("#{changelog_path}")
			changelog_file.each do |message|
				temp_changelog_file << message
				if message=~/^=== #{tag_name}/
					temp_changelog_file.close
					run("rm #{temp_changelog_path}")
					run("rm #{temp_bugfix_path}")
					run("rm #{temp_feature_path}")
					return
				end
				if message=~/^## Bugfix/
					temp_feature_file = File.new("#{temp_feature_path}")
					temp_bugfix_file = File.new("#{temp_bugfix_path}")
					temp_changelog_file << "\n"
					temp_changelog_file << "=== #{tag_name} #{Time.now.strftime("%Y-%m-%d")}\n"
					if temp_feature_file.size > 0
						temp_changelog_file << "\n"
						temp_changelog_file << "* Feature:\n"
						temp_feature_file.each do |feature|
							temp_changelog_file << "\t* #{feature}"
						end
					elsif temp_bugfix_file.size <= 0
						temp_changelog_file << "\n"
						temp_changelog_file << "* Feature:\n"
						change_log_message = "\t* #{@task_ID}: update dependency"
						if dependence_info
							dependence_info.map do |k, v|
								change_log_message = change_log_message + " #{k.to_s} version to #{v}"
							end
						end
						temp_changelog_file << change_log_message
					end
					if temp_bugfix_file.size > 0
						temp_changelog_file << "\n"
						temp_changelog_file << "* Bugfix:\n"
						temp_bugfix_file.each do |bugfix|
							temp_changelog_file << "\t* #{bugfix}"
						end
					end
					temp_changelog_file << "\n"
					temp_bugfix_file.close
					temp_feature_file.close
				end
			end
			changelog_file.close
			temp_changelog_file.close
			run("mv #{temp_changelog_path} #{changelog_path}")

			run("rm #{temp_bugfix_path}")
			run("rm  #{temp_feature_path}")

			run("cat #{changelog_path}")

			g.add
			g.commit("#{@task_ID}, update changelog")
			g.push('origin', g.current_branch)
		end

		def run_without_exit(command)
			if command.kind_of?(Array)
				command = command.join(" ")
			end

			puts "> #{command}"
			output = ''
			IO.popen(command) do |io|
				while line = io.gets do
					puts line
					output << line
				end
				io.close
			end

			return output
		end

		def run(command)
			output = run_without_exit(command)
			if $?.exitstatus != 0
				puts "Child process exit with non-zero status code, #{$?}"
				exit!
			end

			return output
		end
	end

	CLI.start(ARGV)
end
