#!/bin/bash

function runTest () {
    start=$(date +%s)
    if [ "$1" = "device" ] ; then
      if [ "$2" = "" ] ; then
        echo "use default device"
        xcodebuild test -destination-timeout 900 -workspace ipaymentapp.xcworkspace -scheme ipaymentappTests -sdk iphoneos -configuration Debug
      else
        echo "use $2"
        xcodebuild test -destination-timeout 900 -workspace ipaymentapp.xcworkspace -scheme ipaymentappTests -destination platform='iOS',name="$2" -configuration Debug
      fi
    elif [ "$1" = "" ] ; then
        echo "use default simulator"
        xcodebuild test -destination-timeout 900 -workspace ipaymentapp.xcworkspace -scheme ipaymentappTests -sdk iphonesimulator -configuration Debug -destination platform='iOS Simulator',name='iPhone 6 Plus'
    else
        echo "use $1"
        xcodebuild test -destination-timeout 900 -workspace ipaymentapp.xcworkspace -scheme ipaymentappTests -sdk iphonesimulator -configuration Debug -destination platform='iOS Simulator',"$1",name='iPhone 5s'
    fi
    result=$?
    end=$(date +%s)
    time=$(( $end-$start ))
    echo "运行时间:${time}秒"
    return $result
}

#while [ 1 ]; do
    runTest "$1" "$2"
    exit $?
#done
