//
//  imeituan_monkey.m
//  imeituan-monkey
//
//  Created by zjs on 15/6/24.
//  Copyright (c) 2015年 meituan.com. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <XCTest/XCTest.h>
#import "KIF/KIF.h"
#import "UIApplication-KIFAdditions.h"
#import "UIResponder+SPKFirstResponder.h"

@interface UIApplication (Monkey)

- (BOOL)openURL:(NSURL*)url;

@end

@implementation UIApplication (Monkey)

- (BOOL)openURL:(NSURL*)url
{
    return NO; // 防止跳出程序。
}

@end

@interface imeituan_monkey : XCTestCase

@end

@implementation imeituan_monkey

- (void)setUp {
    [super setUp];
    // Put setup code here. This method is called before the invocation of each test method in the class.
}

- (void)tearDown {
    // Put teardown code here. This method is called after the invocation of each test method in the class.
    [super tearDown];
}


- (CALayer *)flashLayer
{
    static CALayer *layer = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        layer = [[CALayer alloc] init];
        layer.frame = CGRectMake(0, 0, 30, 30);
        NSBundle *bundle = [NSBundle bundleForClass:[self class]];
        NSString *imagePath = [bundle pathForResource:@"touch" ofType:@"png"];
        UIImage *image = [UIImage imageWithContentsOfFile:imagePath];
        layer.contents = (id)image.CGImage;
    });
    return layer;
}

- (UIView *)getTopViewWithPoint:(CGPoint)point
{
    UIWindow *currentWindow = nil;
    UIWindowLevel maxLevel = -999;
    for (UIWindow *window in [[[UIApplication sharedApplication] windowsWithKeyWindow] reverseObjectEnumerator]) {
        CGPoint windowPoint = [window convertPoint:point fromView:nil];
        UIView *view = [window hitTest:windowPoint withEvent:nil];
        if (view == window || !view) {
            continue;
        }
        
        if (window.windowLevel > maxLevel) {
            currentWindow = window;
            maxLevel = window.windowLevel;
        }
    }
    return currentWindow;
}

- (void)tapLeftArea
{
    [tester runBlock:^KIFTestStepResult(NSError **error) {
        CGFloat width = 44;
        CGFloat height = 44;
        
        CGFloat x = 12 + arc4random_uniform(width + 1);
        CGFloat y = 20 + arc4random_uniform(height+ 1);
        CGPoint randomPoint = CGPointMake(x, y);
        
        UIView *currentWindow = [self getTopViewWithPoint:randomPoint];
        CGPoint windowPoint = [currentWindow convertPoint:randomPoint fromView:nil];
        UIView *view = [currentWindow hitTest:windowPoint withEvent:nil];
        
        if (!view) {
            return KIFTestStepResultSuccess;
        }
        
        [currentWindow.layer addSublayer:[self flashLayer]];
        [[self flashLayer] setPosition:randomPoint];
        CGPoint viewPoint = [view convertPoint:randomPoint fromView:nil];
        [view tapAtPoint:viewPoint];
        return KIFTestStepResultSuccess;
    }];
}

- (void)tap
{
    [tester runBlock:^KIFTestStepResult(NSError **error) {
        CGRect bounds = [[UIScreen mainScreen] bounds];
        CGFloat x = bounds.size.width * arc4random_uniform(bounds.size.width + 1) / bounds.size.width;
        CGFloat y = bounds.size.height * arc4random_uniform(bounds.size.height + 1) / bounds.size.height;
        CGPoint randomPoint = CGPointMake(x, y);
        
        UIView *currentWindow = [self getTopViewWithPoint:randomPoint];
        CGPoint windowPoint = [currentWindow convertPoint:randomPoint fromView:nil];
        UIView *view = [currentWindow hitTest:windowPoint withEvent:nil];
        
        if (!view) {
            return KIFTestStepResultSuccess;
        }
        
        [currentWindow.layer addSublayer:[self flashLayer]];
        [[self flashLayer] setPosition:randomPoint];
        CGPoint viewPoint = [view convertPoint:randomPoint fromView:nil];
        [view tapAtPoint:viewPoint];
        return KIFTestStepResultSuccess;
    }];
}

- (void)drag
{
    [tester runBlock:^KIFTestStepResult(NSError **error) {
        CGRect bounds = [[UIScreen mainScreen] bounds];
        CGFloat x = bounds.size.width * arc4random_uniform(bounds.size.width + 1) / bounds.size.width;
        CGFloat y = bounds.size.height * arc4random_uniform(bounds.size.height + 1) / bounds.size.height;
        CGPoint randomPoint = CGPointMake(x, y);
        
        CGFloat x2 = bounds.size.width * arc4random_uniform(bounds.size.width+1) / bounds.size.width;
        CGFloat y2 = bounds.size.height * arc4random_uniform(bounds.size.height+1) / bounds.size.height;
        CGPoint randomPoint2 = CGPointMake(x2, y2);
        
        UIView *currentWindow = [self getTopViewWithPoint:randomPoint];
        CGPoint windowPoint = [currentWindow convertPoint:randomPoint fromView:nil];
        UIView *view = [currentWindow hitTest:windowPoint withEvent:nil];
        if (!view) {
            return KIFTestStepResultSuccess;
        }
        
        [currentWindow.layer addSublayer:[self flashLayer]];
        [[self flashLayer] setPosition:randomPoint];
        
        CGPoint viewPoint = [view convertPoint:randomPoint fromView:nil];
        CGPoint viewPoint2 = [view convertPoint:randomPoint2 fromView:nil];
        
        
        [view dragFromPoint:viewPoint toPoint:viewPoint2];
        return KIFTestStepResultSuccess;
    }];
}

- (void)longPress
{
    [tester runBlock:^KIFTestStepResult(NSError **error) {
        CGRect bounds = [[UIScreen mainScreen] bounds];
        CGFloat x = bounds.size.width * arc4random_uniform(bounds.size.width + 1) / bounds.size.width;
        CGFloat y = bounds.size.height * arc4random_uniform(bounds.size.height + 1) / bounds.size.height;
        CGPoint randomPoint = CGPointMake(x, y);
        
        UIView *currentWindow = [self getTopViewWithPoint:randomPoint];
        CGPoint windowPoint = [currentWindow convertPoint:randomPoint fromView:nil];
        UIView *view = [currentWindow hitTest:windowPoint withEvent:nil];
        
        if (!view) {
            return KIFTestStepResultSuccess;
        }
        
        [currentWindow.layer addSublayer:[self flashLayer]];
        [[self flashLayer] setPosition:randomPoint];
        
        CGPoint viewPoint = [view convertPoint:randomPoint fromView:nil];
        
        [view longPressAtPoint:viewPoint duration:1.0];
        return KIFTestStepResultSuccess;
    }];
}

- (void)runRandomAction
{
    int randNumber = arc4random_uniform(60);
    if (randNumber >= 0 && randNumber <45) {
        [self tap];
    } else if (randNumber >= 45 && randNumber < 50) {
        [self tapLeftArea];
    } else if (randNumber >= 50 && randNumber < 55) {
        [self drag];
    } else {
        [self longPress];
    }
}

- (BOOL)processAlertView
{
    id firstResponder = [UIResponder spk_currentFirstResponder];
    if (![firstResponder isKindOfClass:[UIAlertController class]]) {
        // 非 UIAlertView ，非 UIAlertController
        return NO;
    }
    NSBundle *bundle = [NSBundle bundleForClass:[self class]];
    NSString *plistPath = [bundle pathForResource:@"alertViewButtonTitles" ofType:@"plist"];
    NSMutableArray *data = [[NSMutableArray alloc] initWithContentsOfFile:plistPath];
    NSEnumerationOptions enumerationOption;
    __block BOOL isAlertTitle = NO;
    int randNumber = arc4random_uniform(10);
    if (randNumber >= 5) {
        enumerationOption = 0;
    } else {
        enumerationOption = NSEnumerationReverse;
    }
    [data enumerateObjectsWithOptions:enumerationOption usingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        NSString *buttonTitle = (NSString *)obj;
        if ([tester tryFindingViewWithAccessibilityLabel:buttonTitle error:nil]) {
            [tester tapViewWithAccessibilityLabel:buttonTitle];
            *stop = YES;
            isAlertTitle = YES;
        }
    }];
    return isAlertTitle;
}

- (void)testCloseMemoryCheck
{
    [tester tapViewWithAccessibilityLabel:@"设置"];
    [tester waitForViewWithAccessibilityLabel:@"设置"];
    
    [tester tapViewWithAccessibilityLabel:@"调试信息"];
    [tester waitForViewWithAccessibilityLabel:@"想要点啥？"];
    
    [tester tapViewWithAccessibilityLabel:@"关闭内存泄露检测"];
    
    [tester setOn:YES forSwitchWithAccessibilityLabel:@"memoryCheck"];
    
    [tester tapViewWithAccessibilityLabel:@"Close"];
    
    [tester tapViewWithAccessibilityLabel:@"下单"];
    
}

- (void)testMonkey
{
    while (1) {
        if (![self processAlertView]) {
            [self runRandomAction];
        }
        
        [tester waitForTimeInterval:0.5];
    }
}

@end;
