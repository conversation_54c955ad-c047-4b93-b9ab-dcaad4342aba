// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 51;
	objects = {

/* Begin PBXBuildFile section */
		07686C042058F39900046188 /* PPConfigViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 07686C032058F39900046188 /* PPConfigViewController.swift */; };
		07686C0620590A8A00046188 /* PPGlobalConstant.swift in Sources */ = {isa = PBXBuildFile; fileRef = 07686C0520590A8A00046188 /* PPGlobalConstant.swift */; };
		07686C082059107C00046188 /* NSObject+Addtion.swift in Sources */ = {isa = PBXBuildFile; fileRef = 07686C072059107C00046188 /* NSObject+Addtion.swift */; };
		07686C0A20592FB100046188 /* PPSelectMockViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 07686C0920592FB100046188 /* PPSelectMockViewController.swift */; };
		0784C21721A69B5B00624997 /* PPPicassoVCDebugViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0784C21621A69B5B00624997 /* PPPicassoVCDebugViewController.m */; };
		0784C21921A6B1F100624997 /* JSPathData.plist in Resources */ = {isa = PBXBuildFile; fileRef = 0784C21821A6B1F100624997 /* JSPathData.plist */; };
		0790B07E205FADFB004C8833 /* ReactiveCocoaBindings.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0790B07D205FADFB004C8833 /* ReactiveCocoaBindings.swift */; };
		0790B080205FAFE0004C8833 /* PPMineViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0790B07F205FAFE0004C8833 /* PPMineViewController.swift */; };
		07DACD01205B97EB00FB01F0 /* PPUtil.swift in Sources */ = {isa = PBXBuildFile; fileRef = 07DACD00205B97EB00FB01F0 /* PPUtil.swift */; };
		07E744CE2226644B00896C60 /* PPCacheManagerViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 07E744CD2226644B00896C60 /* PPCacheManagerViewController.m */; };
		0A0CE28F1B58E76E00DE7304 /* PPOrderStatus.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A0CE28E1B58E76E00DE7304 /* PPOrderStatus.m */; };
		0A1DAB951B4CE05A00C34A65 /* PortalSettings.plist in Resources */ = {isa = PBXBuildFile; fileRef = 0A1DAB941B4CE05A00C34A65 /* PortalSettings.plist */; };
		0A5DEB9B1B54D943002FCF3F /* PPAccountController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A5DEB9A1B54D943002FCF3F /* PPAccountController.m */; };
		0A6F020D1B3AC46800F64F7B /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A6F020C1B3AC46800F64F7B /* main.m */; };
		0A6F02101B3AC46800F64F7B /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A6F020F1B3AC46800F64F7B /* AppDelegate.m */; };
		0A6F021E1B3AC46900F64F7B /* LaunchScreen.xib in Resources */ = {isa = PBXBuildFile; fileRef = 0A6F021C1B3AC46900F64F7B /* LaunchScreen.xib */; };
		0A6F02681B3AC65F00F64F7B /* NSDate+Time.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A6F02351B3AC65F00F64F7B /* NSDate+Time.m */; };
		0A6F02691B3AC65F00F64F7B /* PPContentInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A6F02371B3AC65F00F64F7B /* PPContentInfo.m */; };
		0A6F026A1B3AC65F00F64F7B /* PPModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A6F02391B3AC65F00F64F7B /* PPModel.m */; };
		0A6F026C1B3AC65F00F64F7B /* PPOrder.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A6F023D1B3AC65F00F64F7B /* PPOrder.m */; };
		0A6F026D1B3AC65F00F64F7B /* PPService.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A6F023F1B3AC65F00F64F7B /* PPService.m */; };
		0A6F026E1B3AC65F00F64F7B /* SAKBaseModel+CashierApp.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A6F02411B3AC65F00F64F7B /* SAKBaseModel+CashierApp.m */; };
		0A6F026F1B3AC65F00F64F7B /* PPConfigTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A6F02441B3AC65F00F64F7B /* PPConfigTableViewCell.m */; };
		0A6F02711B3AC65F00F64F7B /* PPContentTextFieldCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A6F02481B3AC65F00F64F7B /* PPContentTextFieldCell.m */; };
		0A6F02741B3AC65F00F64F7B /* PPMyAccountCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A6F024E1B3AC65F00F64F7B /* PPMyAccountCell.m */; };
		0A6F02751B3AC65F00F64F7B /* PPMyAccountView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A6F02501B3AC65F00F64F7B /* PPMyAccountView.m */; };
		0A6F02761B3AC65F00F64F7B /* PPOrderDetailViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A6F02521B3AC65F00F64F7B /* PPOrderDetailViewController.m */; };
		0A6F02771B3AC65F00F64F7B /* PPOrderlistViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A6F02541B3AC65F00F64F7B /* PPOrderlistViewController.m */; };
		0A6F02781B3AC65F00F64F7B /* PPOrderTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 0A6F02561B3AC65F00F64F7B /* PPOrderTableViewCell.m */; };
		0A6F027C1B3AC65F00F64F7B /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 0A6F025D1B3AC65F00F64F7B /* <EMAIL> */; };
		0A6F027D1B3AC65F00F64F7B /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 0A6F025E1B3AC65F00F64F7B /* <EMAIL> */; };
		0A6F027F1B3AC65F00F64F7B /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 0A6F02601B3AC65F00F64F7B /* <EMAIL> */; };
		0A6F02801B3AC65F00F64F7B /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 0A6F02611B3AC65F00F64F7B /* <EMAIL> */; };
		0A6F02811B3AC65F00F64F7B /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 0A6F02621B3AC65F00F64F7B /* <EMAIL> */; };
		0A6F02821B3AC65F00F64F7B /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 0A6F02631B3AC65F00F64F7B /* <EMAIL> */; };
		0A6F02831B3AC65F00F64F7B /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 0A6F02641B3AC65F00F64F7B /* <EMAIL> */; };
		0A6F02841B3AC65F00F64F7B /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 0A6F02651B3AC65F00F64F7B /* <EMAIL> */; };
		0A6F02851B3AC65F00F64F7B /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 0A6F02661B3AC65F00F64F7B /* <EMAIL> */; };
		0A6F02861B3AC65F00F64F7B /* DefaultAppInfo.plist in Resources */ = {isa = PBXBuildFile; fileRef = 0A6F02671B3AC65F00F64F7B /* DefaultAppInfo.plist */; };
		0AAAAA641B42C95B0099BC4A /* MTNavigationController+IPayment.m in Sources */ = {isa = PBXBuildFile; fileRef = 0AAAAA631B42C95B0099BC4A /* MTNavigationController+IPayment.m */; };
		0E35CC3B2136E4DB000CAA36 /* PPQRCodeScanViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0E35CC3A2136E4DB000CAA36 /* PPQRCodeScanViewController.m */; };
		0E35CC3D2136E4FE000CAA36 /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0E35CC3C2136E4FE000CAA36 /* AVFoundation.framework */; };
		0E35CC402136E84F000CAA36 /* PPQRScanChildViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 0E35CC3F2136E84F000CAA36 /* PPQRScanChildViewController.m */; };
		0E35CC432136F4A1000CAA36 /* PPBarCoverView.m in Sources */ = {isa = PBXBuildFile; fileRef = 0E35CC422136F4A1000CAA36 /* PPBarCoverView.m */; };
		0E89D5502180BE5E0066135A /* mt_security.png in Resources */ = {isa = PBXBuildFile; fileRef = 0E89D54F2180BE5E0066135A /* mt_security.png */; };
		2348A478211871CE00AA1AA7 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 2348A471211871CB00AA1AA7 /* <EMAIL> */; };
		2348A479211871CE00AA1AA7 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 2348A472211871CC00AA1AA7 /* <EMAIL> */; };
		2348A47A211871CE00AA1AA7 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 2348A473211871CD00AA1AA7 /* <EMAIL> */; };
		2348A47B211871CE00AA1AA7 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 2348A474211871CD00AA1AA7 /* <EMAIL> */; };
		2348A47C211871CE00AA1AA7 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 2348A475211871CD00AA1AA7 /* <EMAIL> */; };
		2348A47D211871CE00AA1AA7 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 2348A476211871CD00AA1AA7 /* <EMAIL> */; };
		2348A47E211871CE00AA1AA7 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 2348A477211871CE00AA1AA7 /* <EMAIL> */; };
		239EEE4C21D0D8F500CF0556 /* PPBarcodeCashierHasParametersViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 239EEE4B21D0D8F500CF0556 /* PPBarcodeCashierHasParametersViewController.m */; };
		2CF69FD2235839DF00093F0C /* MerchantWalletEntranceViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 2CF69FD1235839DF00093F0C /* MerchantWalletEntranceViewController.m */; };
		3498D9E72D75A3E3001490BC /* CoreML.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 3498D9E62D75A3E3001490BC /* CoreML.framework */; };
		35148B4D2553DE3300EF3E38 /* PPOrderDetailViewCustomCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 35148B4C2553DE3300EF3E38 /* PPOrderDetailViewCustomCell.m */; };
		356E0BAA255299AF001F94E0 /* PPRefundedMoneyInputModalView.m in Sources */ = {isa = PBXBuildFile; fileRef = 356E0BA9255299AF001F94E0 /* PPRefundedMoneyInputModalView.m */; };
		359B724524B71BD400B74517 /* UIColor+Addition.m in Sources */ = {isa = PBXBuildFile; fileRef = 359B724424B71BD400B74517 /* UIColor+Addition.m */; };
		35FCDEC824AF4B0A0090CE72 /* MTBaseViewController+DarkModeNavBarStyle.m in Sources */ = {isa = PBXBuildFile; fileRef = 35FCDEC724AF4B0A0090CE72 /* MTBaseViewController+DarkModeNavBarStyle.m */; };
		4112DA3C26C6981000E6A58A /* PPHalfPageDebugViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 4112DA3B26C6981000E6A58A /* PPHalfPageDebugViewController.m */; };
		412C5C98229D94ED0062F553 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 412C5C96229D94EC0062F553 /* <EMAIL> */; };
		412C5C99229D94ED0062F553 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 412C5C97229D94ED0062F553 /* <EMAIL> */; };
		41452BC9226DDD070018567A /* PPHostSwitcher.m in Sources */ = {isa = PBXBuildFile; fileRef = 41452BC8226DDD070018567A /* PPHostSwitcher.m */; };
		41A09A7421182CAC0020B8B4 /* PPRiskErrorProcessController.m in Sources */ = {isa = PBXBuildFile; fileRef = 41A09A7221182CAC0020B8B4 /* PPRiskErrorProcessController.m */; };
		41C7670220B6D3A2006A911A /* PPOpenPayDebugViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 41C7670120B6D3A2006A911A /* PPOpenPayDebugViewController.m */; };
		41C7670720B71CEF006A911A /* MeituanPaySDK.m in Sources */ = {isa = PBXBuildFile; fileRef = 41C7670620B71CEF006A911A /* MeituanPaySDK.m */; };
		421F14E821BA77A30045599C /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 421F14E621BA77A10045599C /* <EMAIL> */; };
		421F14E921BA77A30045599C /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 421F14E721BA77A10045599C /* <EMAIL> */; };
		42BAFDDC1FCC10A700106D04 /* PPFinancialKitViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 42BAFDDB1FCC10A700106D04 /* PPFinancialKitViewController.m */; };
		45185B91E876C2452225FAA6 /* libPods-ipaymentappTests.a in Frameworks */ = {isa = PBXBuildFile; fileRef = F0152918C8AC14CF369ED16E /* libPods-ipaymentappTests.a */; };
		561A89691F380B8E000F4336 /* SAKDomainObject+Debug.m in Sources */ = {isa = PBXBuildFile; fileRef = 561A89681F380B8E000F4336 /* SAKDomainObject+Debug.m */; };
		564A63942048FD7500AA03FC /* PPHelloPayController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 564A63932048FD7500AA03FC /* PPHelloPayController.swift */; };
		564A63962049332700AA03FC /* PPPaymentSettingView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 564A63952049332700AA03FC /* PPPaymentSettingView.swift */; };
		56C953B71E6009360012B8AF /* PPRefundedOrderListViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 56C953B61E6009360012B8AF /* PPRefundedOrderListViewController.m */; };
		56C953BA1E600D980012B8AF /* PPRefundedOrderCellTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 56C953B91E600D980012B8AF /* PPRefundedOrderCellTableViewCell.m */; };
		56C953C01E600F7E0012B8AF /* PPRefundedOrderUIObject.m in Sources */ = {isa = PBXBuildFile; fileRef = 56C953BF1E600F7E0012B8AF /* PPRefundedOrderUIObject.m */; };
		56C953C31E600F9C0012B8AF /* PPRefundedOrder.m in Sources */ = {isa = PBXBuildFile; fileRef = 56C953C21E600F9C0012B8AF /* PPRefundedOrder.m */; };
		56C953C61E6024F00012B8AF /* PPRefundedOrderViewModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 56C953C51E6024F00012B8AF /* PPRefundedOrderViewModel.m */; };
		56F7DEC32046824200E4C1BC /* PPSubmitOrderViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56F7DEC22046824200E4C1BC /* PPSubmitOrderViewController.swift */; };
		56F7DEC72046AAF600E4C1BC /* UIColor+Addtion.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56F7DEC52046AAF600E4C1BC /* UIColor+Addtion.swift */; };
		56F7DEC82046AAF600E4C1BC /* UIFont+Addition.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56F7DEC62046AAF600E4C1BC /* UIFont+Addition.swift */; };
		56F7DECB2046C3F600E4C1BC /* NSObject+MLeaksFinderSwitch.m in Sources */ = {isa = PBXBuildFile; fileRef = 56F7DECA2046C3F600E4C1BC /* NSObject+MLeaksFinderSwitch.m */; };
		5A0F380F1E4B1465003D98CA /* SAKHostSwitcherURLProtocol+APPMock.m in Sources */ = {isa = PBXBuildFile; fileRef = 5A0F380E1E4B1465003D98CA /* SAKHostSwitcherURLProtocol+APPMock.m */; };
		5A8ED17A1E6D3DBD00D2F558 /* PPRegisterAPPMock.m in Sources */ = {isa = PBXBuildFile; fileRef = 5A8ED1791E6D3DBD00D2F558 /* PPRegisterAPPMock.m */; };
		5A8ED17D1E6D3EC000D2F558 /* SAKBaseModel+PPHook.m in Sources */ = {isa = PBXBuildFile; fileRef = 5A8ED17C1E6D3EC000D2F558 /* SAKBaseModel+PPHook.m */; };
		5AB9786A1E52ABC80089C6C2 /* PPTitansAdapter.m in Sources */ = {isa = PBXBuildFile; fileRef = 5AB978691E52ABC80089C6C2 /* PPTitansAdapter.m */; };
		5AB9786D1E52AC250089C6C2 /* PPTitansNamespace.m in Sources */ = {isa = PBXBuildFile; fileRef = 5AB9786C1E52AC250089C6C2 /* PPTitansNamespace.m */; };
		672D372D1E600308001DFB54 /* PPDataStorageViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 672D372C1E600308001DFB54 /* PPDataStorageViewController.m */; };
		6745287921D390BC0018BB05 /* PPDebugPanel.m in Sources */ = {isa = PBXBuildFile; fileRef = 6745287821D390BC0018BB05 /* PPDebugPanel.m */; };
		67754C72248140EA0000590B /* SPKTitleSwitchTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 67754C71248140EA0000590B /* SPKTitleSwitchTableViewCell.m */; };
		67754C75248141420000590B /* SPKCommonTableView.m in Sources */ = {isa = PBXBuildFile; fileRef = 67754C73248141420000590B /* SPKCommonTableView.m */; };
		67754C782481418F0000590B /* SPKScrollViewRefreshControl.m in Sources */ = {isa = PBXBuildFile; fileRef = 67754C772481418F0000590B /* SPKScrollViewRefreshControl.m */; };
		67754C7B248141C30000590B /* SPKScrollViewDragControl.m in Sources */ = {isa = PBXBuildFile; fileRef = 67754C7A248141C30000590B /* SPKScrollViewDragControl.m */; };
		67754C7E248142680000590B /* SPKScrollViewLoadMoreControl.m in Sources */ = {isa = PBXBuildFile; fileRef = 67754C7C248142670000590B /* SPKScrollViewLoadMoreControl.m */; };
		67754C81248142870000590B /* SPKNetworkErrorView.m in Sources */ = {isa = PBXBuildFile; fileRef = 67754C80248142870000590B /* SPKNetworkErrorView.m */; };
		67754C86248142BA0000590B /* SPKRefreshNormalHeader.m in Sources */ = {isa = PBXBuildFile; fileRef = 67754C84248142BA0000590B /* SPKRefreshNormalHeader.m */; };
		67754C87248142BA0000590B /* SPKRefreshNormalFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 67754C85248142BA0000590B /* SPKRefreshNormalFooter.m */; };
		67754C8A248142DB0000590B /* SPKRefreshHeader.m in Sources */ = {isa = PBXBuildFile; fileRef = 67754C88248142DB0000590B /* SPKRefreshHeader.m */; };
		67754C8D248142FD0000590B /* SPKRefreshComponent.m in Sources */ = {isa = PBXBuildFile; fileRef = 67754C8B248142FC0000590B /* SPKRefreshComponent.m */; };
		67754C902481431D0000590B /* UIScrollView+SPKRefresh.m in Sources */ = {isa = PBXBuildFile; fileRef = 67754C8F2481431D0000590B /* UIScrollView+SPKRefresh.m */; };
		67754C93248143350000590B /* SPKRefreshConst.m in Sources */ = {isa = PBXBuildFile; fileRef = 67754C91248143350000590B /* SPKRefreshConst.m */; };
		67754C962481434F0000590B /* SPKRefreshFooter.m in Sources */ = {isa = PBXBuildFile; fileRef = 67754C952481434F0000590B /* SPKRefreshFooter.m */; };
		67754C99248144320000590B /* SPKScrollPageView.m in Sources */ = {isa = PBXBuildFile; fileRef = 67754C98248144310000590B /* SPKScrollPageView.m */; };
		67754C9C248144680000590B /* SPKTitleSwitchCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 67754C9A248144680000590B /* SPKTitleSwitchCell.m */; };
		67754C9F248145640000590B /* SPKCommonCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 67754C9D248145640000590B /* SPKCommonCell.m */; };
		67754CA2248145990000590B /* SPKTitleTipCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 67754CA0248145990000590B /* SPKTitleTipCell.m */; };
		67754CA5248145B20000590B /* SPKTitleImageCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 67754CA3248145B20000590B /* SPKTitleImageCell.m */; };
		67754CA8248145CB0000590B /* SPKTitleInputCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 67754CA6248145CA0000590B /* SPKTitleInputCell.m */; };
		67754CAB2481460A0000590B /* SPKTitleTipTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 67754CA92481460A0000590B /* SPKTitleTipTableViewCell.m */; };
		67754CAE248146310000590B /* SPKTitleImageTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 67754CAC248146300000590B /* SPKTitleImageTableViewCell.m */; };
		67754CB12481465A0000590B /* SPKTitleInputTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 67754CAF248146590000590B /* SPKTitleInputTableViewCell.m */; };
		6A6F1EFC21B015EC00F48300 /* PPUIKit2ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 6A6F1EFB21B015EC00F48300 /* PPUIKit2ViewController.m */; };
		6A6F1EFF21B01BB400F48300 /* PPUIKit2TabSwitchViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 6A6F1EFE21B01BB400F48300 /* PPUIKit2TabSwitchViewController.m */; };
		6A6F1F0221B01BD000F48300 /* PPUIKit2ButtonViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 6A6F1F0121B01BD000F48300 /* PPUIKit2ButtonViewController.m */; };
		6A6F1F0521B01C0900F48300 /* PPUIKit2AgreementViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 6A6F1F0421B01C0900F48300 /* PPUIKit2AgreementViewController.m */; };
		6A6F1F0821B01C2F00F48300 /* PPUIKit2PickerViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 6A6F1F0721B01C2F00F48300 /* PPUIKit2PickerViewController.m */; };
		6A6F1F0B21B01C4B00F48300 /* PPUIKit2NoticeViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 6A6F1F0A21B01C4B00F48300 /* PPUIKit2NoticeViewController.m */; };
		6A6F1F0E21B01C6600F48300 /* PPUIKit2BannerViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 6A6F1F0D21B01C6600F48300 /* PPUIKit2BannerViewController.m */; };
		6A6F1F1121B01C7900F48300 /* PPUIKit2CardViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 6A6F1F1021B01C7900F48300 /* PPUIKit2CardViewController.m */; };
		6A6F1F1421B01C9D00F48300 /* PPUIKit2ListItemViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 6A6F1F1321B01C9D00F48300 /* PPUIKit2ListItemViewController.m */; };
		6A6F1F1721B01CDA00F48300 /* PPUIKit2ActivityViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 6A6F1F1621B01CDA00F48300 /* PPUIKit2ActivityViewController.m */; };
		6A6F1F1A21B01CF700F48300 /* PPUIKit2ToastViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 6A6F1F1921B01CF700F48300 /* PPUIKit2ToastViewController.m */; };
		6A6F1F1D21B01D1100F48300 /* PPUIKit2AlertViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 6A6F1F1C21B01D1100F48300 /* PPUIKit2AlertViewController.m */; };
		6A6F1F2021B01D3A00F48300 /* PPUIKitActionSheetViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 6A6F1F1F21B01D3A00F48300 /* PPUIKitActionSheetViewController.m */; };
		6A7418A122CB890C00337AEB /* PPTestCertificateCameraViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 6A74189E22CB890C00337AEB /* PPTestCertificateCameraViewController.m */; };
		6A7418A222CB890C00337AEB /* PPCertificateCameraViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 6A74189F22CB890C00337AEB /* PPCertificateCameraViewController.m */; };
		6AE9D3F421B7D8A100113E50 /* MTFUTableViewCellAccessoryImage.png in Resources */ = {isa = PBXBuildFile; fileRef = 6AE9D3F321B7D8A100113E50 /* MTFUTableViewCellAccessoryImage.png */; };
		6AE9D3FA21B8C04B00113E50 /* bankicon.png in Resources */ = {isa = PBXBuildFile; fileRef = 6AE9D3F921B8C04B00113E50 /* bankicon.png */; };
		6AE9D3FC21B8CDEF00113E50 /* bankcheck.png in Resources */ = {isa = PBXBuildFile; fileRef = 6AE9D3FB21B8CDEF00113E50 /* bankcheck.png */; };
		6E07D2F61D3614FB007F0EE1 /* alertViewButtonTitles.plist in Resources */ = {isa = PBXBuildFile; fileRef = 6E07D2F51D3614FB007F0EE1 /* alertViewButtonTitles.plist */; };
		6E82B2321F96EDE800CBECA9 /* PPNFCManagementViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 6E82B2311F96EDE800CBECA9 /* PPNFCManagementViewController.m */; };
		6EA4DC6D1DCAF3E300C316D3 /* PPDebugWindow.m in Sources */ = {isa = PBXBuildFile; fileRef = 6EA4DC6C1DCAF3E300C316D3 /* PPDebugWindow.m */; };
		6EC7EF2C1CD0C358006921EE /* paymentapp_monkey.m in Sources */ = {isa = PBXBuildFile; fileRef = 6EC7EF2A1CD0C358006921EE /* paymentapp_monkey.m */; };
		6EC7EF2D1CD0C358006921EE /* monkey.sh in Resources */ = {isa = PBXBuildFile; fileRef = 6EC7EF2B1CD0C358006921EE /* monkey.sh */; };
		6EC7EF2F1CD0C443006921EE /* touch.png in Resources */ = {isa = PBXBuildFile; fileRef = 6EC7EF2E1CD0C443006921EE /* touch.png */; };
		7AB2EAEB1D45C59000F24AEA /* PPWalletEntranceInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 7AB2EAEA1D45C59000F24AEA /* PPWalletEntranceInfo.m */; };
		7AB2EAF21D45E14700F24AEA /* PPMineModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 7AB2EAF11D45E14700F24AEA /* PPMineModel.m */; };
		7AB2EAF51D45ECCC00F24AEA /* PPMineViewModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 7AB2EAF41D45ECCC00F24AEA /* PPMineViewModel.m */; };
		7AB625FF1D52F96700804FCE /* WalletEntranceInfo.plist in Resources */ = {isa = PBXBuildFile; fileRef = 7AB625FE1D52F96700804FCE /* WalletEntranceInfo.plist */; };
		AD7CEEF3264CC8D40066A10F /* libbz2.1.0.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = AD7CEEF2264CC8B10066A10F /* libbz2.1.0.tbd */; };
		B43BDFF31D4F71BC00F797F8 /* Default-568.png in Resources */ = {isa = PBXBuildFile; fileRef = B43BDFF21D4F71BC00F797F8 /* Default-568.png */; };
		B4A8E7801BA6ABFD008C89D0 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = B4A8E77E1BA6ABFD008C89D0 /* <EMAIL> */; };
		B4A8E7811BA6ABFD008C89D0 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = B4A8E77F1BA6ABFD008C89D0 /* <EMAIL> */; };
		B4A8E7861BA6ADCF008C89D0 /* PPAppSelectViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = B4A8E7851BA6ADCF008C89D0 /* PPAppSelectViewController.m */; };
		B4A8E7891BA6B2B1008C89D0 /* PPAppSelectTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = B4A8E7881BA6B2B1008C89D0 /* PPAppSelectTableViewCell.m */; };
		BCB55198200C9D6900711A42 /* MTBaseViewController+IPayment.m in Sources */ = {isa = PBXBuildFile; fileRef = BCB55197200C9D6800711A42 /* MTBaseViewController+IPayment.m */; };
		BCB5519C200DDCA000711A42 /* PPPicassoDebugViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = BCB5519B200DDCA000711A42 /* PPPicassoDebugViewController.m */; };
		BCDEA8C4200E4FDD0084517C /* UITextField+MemoryLeak.m in Sources */ = {isa = PBXBuildFile; fileRef = BCDEA8C3200E4FDD0084517C /* UITextField+MemoryLeak.m */; };
		D30606271FCEAAEA00D5D849 /* PPVerifyPasswordViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = D30606261FCEAAEA00D5D849 /* PPVerifyPasswordViewController.m */; };
		D306062A1FD584AA00D5D849 /* PPTestViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = D30606291FD584AA00D5D849 /* PPTestViewController.m */; };
		D31B5EE9203021FA00FC3482 /* PPScrollPageViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = D31B5EDD203021F900FC3482 /* PPScrollPageViewController.m */; };
		D31B5EEA203021FA00FC3482 /* PPSPKRefreshTestViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = D31B5EDF203021F900FC3482 /* PPSPKRefreshTestViewController.m */; };
		D31B5EEB203021FA00FC3482 /* PPNormalizedCellViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = D31B5EE0203021F900FC3482 /* PPNormalizedCellViewController.m */; };
		D31B5EEC203021FA00FC3482 /* PPAgreementViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = D31B5EE3203021F900FC3482 /* PPAgreementViewController.m */; };
		D31B5EED203021FA00FC3482 /* PPAlertViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = D31B5EE4203021F900FC3482 /* PPAlertViewController.m */; };
		D31B5EEE203021FA00FC3482 /* PPUIKitViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = D31B5EE7203021F900FC3482 /* PPUIKitViewController.m */; };
		D31B5EF120303EFF00FC3482 /* PPBannerViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = D31B5EF020303EFF00FC3482 /* PPBannerViewController.m */; };
		D31B5EF420306DB500FC3482 /* PPToastViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = D31B5EF320306DB500FC3482 /* PPToastViewController.m */; };
		D31B5EF72030774C00FC3482 /* PPLoadingViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = D31B5EF62030774C00FC3482 /* PPLoadingViewController.m */; };
		D31B5EFA20307E7000FC3482 /* PPNetworkErrorViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = D31B5EF920307E7000FC3482 /* PPNetworkErrorViewController.m */; };
		D31B5EFE203E97DD00FC3482 /* PPScanCardViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = D31B5EFD203E97DD00FC3482 /* PPScanCardViewController.m */; };
		D31B5F11203EBE6500FC3482 /* PPFinacialUtilityViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = D31B5F10203EBE6500FC3482 /* PPFinacialUtilityViewController.m */; };
		D31B5F14203EBEA800FC3482 /* PPFingerPrintPayViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = D31B5F13203EBEA800FC3482 /* PPFingerPrintPayViewController.m */; };
		D33FB0A7200CF30600E60F1D /* MessageUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = D33FB0A6200CF30600E60F1D /* MessageUI.framework */; };
		D33FB0AB200CF32B00E60F1D /* PPCrashReporter.m in Sources */ = {isa = PBXBuildFile; fileRef = D33FB0AA200CF32B00E60F1D /* PPCrashReporter.m */; };
		D3DEA93A1FC3D65B0005E26B /* PPPaymentKitViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = D3DEA9391FC3D65B0005E26B /* PPPaymentKitViewController.m */; };
		E746831722A12FE400C49A17 /* MESHDebugViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = E746830E22A12FE400C49A17 /* MESHDebugViewController.m */; };
		E746831822A12FE400C49A17 /* MESHH5DebugViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = E746831022A12FE400C49A17 /* MESHH5DebugViewController.m */; };
		E746831922A12FE400C49A17 /* MESHTest1ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = E746831222A12FE400C49A17 /* MESHTest1ViewController.m */; };
		E746831A22A12FE400C49A17 /* SAKPayMeshService.m in Sources */ = {isa = PBXBuildFile; fileRef = E746831422A12FE400C49A17 /* SAKPayMeshService.m */; };
		E9CA8E40AE99B5C9C8222087 /* libPods-ipaymentapp.a in Frameworks */ = {isa = PBXBuildFile; fileRef = ************************ /* libPods-ipaymentapp.a */; };
		FF9B00A62823CEBB00EF96A9 /* libSAKAccount-Core.a in Frameworks */ = {isa = PBXBuildFile; fileRef = FF9B00A52823CEBB00EF96A9 /* libSAKAccount-Core.a */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		0A6F02241B3AC46900F64F7B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 0A6F01FF1B3AC46800F64F7B /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 0A6F02061B3AC46800F64F7B;
			remoteInfo = ipaymentapp;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		07686C032058F39900046188 /* PPConfigViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PPConfigViewController.swift; sourceTree = "<group>"; };
		07686C0520590A8A00046188 /* PPGlobalConstant.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PPGlobalConstant.swift; sourceTree = "<group>"; };
		07686C072059107C00046188 /* NSObject+Addtion.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "NSObject+Addtion.swift"; sourceTree = "<group>"; };
		07686C0920592FB100046188 /* PPSelectMockViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PPSelectMockViewController.swift; sourceTree = "<group>"; };
		0784C21521A69B5B00624997 /* PPPicassoVCDebugViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPPicassoVCDebugViewController.h; sourceTree = "<group>"; };
		0784C21621A69B5B00624997 /* PPPicassoVCDebugViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PPPicassoVCDebugViewController.m; sourceTree = "<group>"; };
		0784C21821A6B1F100624997 /* JSPathData.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = JSPathData.plist; path = ipaymentapp/JSPathData.plist; sourceTree = SOURCE_ROOT; };
		0790B07D205FADFB004C8833 /* ReactiveCocoaBindings.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ReactiveCocoaBindings.swift; sourceTree = "<group>"; };
		0790B07F205FAFE0004C8833 /* PPMineViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PPMineViewController.swift; sourceTree = "<group>"; };
		07DACD00205B97EB00FB01F0 /* PPUtil.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PPUtil.swift; sourceTree = "<group>"; };
		07E744CC2226644B00896C60 /* PPCacheManagerViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPCacheManagerViewController.h; sourceTree = "<group>"; };
		07E744CD2226644B00896C60 /* PPCacheManagerViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PPCacheManagerViewController.m; sourceTree = "<group>"; };
		0A0CE28E1B58E76E00DE7304 /* PPOrderStatus.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPOrderStatus.m; sourceTree = "<group>"; };
		0A1DAB941B4CE05A00C34A65 /* PortalSettings.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = PortalSettings.plist; sourceTree = "<group>"; };
		0A5DEB991B54D943002FCF3F /* PPAccountController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPAccountController.h; sourceTree = "<group>"; };
		0A5DEB9A1B54D943002FCF3F /* PPAccountController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPAccountController.m; sourceTree = "<group>"; };
		0A6908BF1B5790D500936191 /* PPOrderStatus.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPOrderStatus.h; sourceTree = "<group>"; };
		0A6F02071B3AC46800F64F7B /* ipaymentapp.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = ipaymentapp.app; sourceTree = BUILT_PRODUCTS_DIR; };
		0A6F020B1B3AC46800F64F7B /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		0A6F020C1B3AC46800F64F7B /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		0A6F020E1B3AC46800F64F7B /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		0A6F020F1B3AC46800F64F7B /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		0A6F021D1B3AC46900F64F7B /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = Base; path = Base.lproj/LaunchScreen.xib; sourceTree = "<group>"; };
		0A6F02231B3AC46900F64F7B /* ipaymentappTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = ipaymentappTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		0A6F02281B3AC46900F64F7B /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		0A6F02341B3AC65F00F64F7B /* NSDate+Time.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSDate+Time.h"; sourceTree = "<group>"; };
		0A6F02351B3AC65F00F64F7B /* NSDate+Time.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSDate+Time.m"; sourceTree = "<group>"; };
		0A6F02361B3AC65F00F64F7B /* PPContentInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPContentInfo.h; sourceTree = "<group>"; };
		0A6F02371B3AC65F00F64F7B /* PPContentInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPContentInfo.m; sourceTree = "<group>"; };
		0A6F02381B3AC65F00F64F7B /* PPModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPModel.h; sourceTree = "<group>"; };
		0A6F02391B3AC65F00F64F7B /* PPModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPModel.m; sourceTree = "<group>"; };
		0A6F023C1B3AC65F00F64F7B /* PPOrder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPOrder.h; sourceTree = "<group>"; };
		0A6F023D1B3AC65F00F64F7B /* PPOrder.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPOrder.m; sourceTree = "<group>"; };
		0A6F023E1B3AC65F00F64F7B /* PPService.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPService.h; sourceTree = "<group>"; };
		0A6F023F1B3AC65F00F64F7B /* PPService.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPService.m; sourceTree = "<group>"; };
		0A6F02401B3AC65F00F64F7B /* SAKBaseModel+CashierApp.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SAKBaseModel+CashierApp.h"; sourceTree = "<group>"; };
		0A6F02411B3AC65F00F64F7B /* SAKBaseModel+CashierApp.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "SAKBaseModel+CashierApp.m"; sourceTree = "<group>"; };
		0A6F02431B3AC65F00F64F7B /* PPConfigTableViewCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPConfigTableViewCell.h; sourceTree = "<group>"; };
		0A6F02441B3AC65F00F64F7B /* PPConfigTableViewCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPConfigTableViewCell.m; sourceTree = "<group>"; };
		0A6F02471B3AC65F00F64F7B /* PPContentTextFieldCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPContentTextFieldCell.h; sourceTree = "<group>"; };
		0A6F02481B3AC65F00F64F7B /* PPContentTextFieldCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPContentTextFieldCell.m; sourceTree = "<group>"; };
		0A6F024D1B3AC65F00F64F7B /* PPMyAccountCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPMyAccountCell.h; sourceTree = "<group>"; };
		0A6F024E1B3AC65F00F64F7B /* PPMyAccountCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPMyAccountCell.m; sourceTree = "<group>"; };
		0A6F024F1B3AC65F00F64F7B /* PPMyAccountView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPMyAccountView.h; sourceTree = "<group>"; };
		0A6F02501B3AC65F00F64F7B /* PPMyAccountView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPMyAccountView.m; sourceTree = "<group>"; };
		0A6F02511B3AC65F00F64F7B /* PPOrderDetailViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPOrderDetailViewController.h; sourceTree = "<group>"; };
		0A6F02521B3AC65F00F64F7B /* PPOrderDetailViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPOrderDetailViewController.m; sourceTree = "<group>"; };
		0A6F02531B3AC65F00F64F7B /* PPOrderlistViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPOrderlistViewController.h; sourceTree = "<group>"; };
		0A6F02541B3AC65F00F64F7B /* PPOrderlistViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPOrderlistViewController.m; sourceTree = "<group>"; };
		0A6F02551B3AC65F00F64F7B /* PPOrderTableViewCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPOrderTableViewCell.h; sourceTree = "<group>"; };
		0A6F02561B3AC65F00F64F7B /* PPOrderTableViewCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPOrderTableViewCell.m; sourceTree = "<group>"; };
		0A6F025C1B3AC65F00F64F7B /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		0A6F025D1B3AC65F00F64F7B /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		0A6F025E1B3AC65F00F64F7B /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		0A6F025F1B3AC65F00F64F7B /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		0A6F02601B3AC65F00F64F7B /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		0A6F02611B3AC65F00F64F7B /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		0A6F02621B3AC65F00F64F7B /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		0A6F02631B3AC65F00F64F7B /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		0A6F02641B3AC65F00F64F7B /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		0A6F02651B3AC65F00F64F7B /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		0A6F02661B3AC65F00F64F7B /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		0A6F02671B3AC65F00F64F7B /* DefaultAppInfo.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = DefaultAppInfo.plist; sourceTree = "<group>"; };
		0AAAAA621B42C95B0099BC4A /* MTNavigationController+IPayment.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "MTNavigationController+IPayment.h"; sourceTree = "<group>"; };
		0AAAAA631B42C95B0099BC4A /* MTNavigationController+IPayment.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "MTNavigationController+IPayment.m"; sourceTree = "<group>"; };
		0AAAAA651B42CA7B0099BC4A /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		0AAAAA661B42CA7B0099BC4A /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		0AAAAA671B42CA7B0099BC4A /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		0AAAAA681B42CA7B0099BC4A /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		0AD377831B3BA06A00F67750 /* libPods-ipaymentapp-AutoCoding.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = "libPods-ipaymentapp-AutoCoding.a"; path = "/Users/<USER>/dev/ipaymentapp/Pods/../build/Debug-iphoneos/libPods-ipaymentapp-AutoCoding.a"; sourceTree = "<absolute>"; };
		0AD377851B3BA08E00F67750 /* libPods-ipaymentapp.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = "libPods-ipaymentapp.a"; path = "/Users/<USER>/dev/ipaymentapp/Pods/../build/Debug-iphoneos/libPods-ipaymentapp.a"; sourceTree = "<absolute>"; };
		0D10C2B115D21214C28EA2FC /* Pods-ipaymentapp.releaseinhouse.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ipaymentapp.releaseinhouse.xcconfig"; path = "../Pods/Target Support Files/Pods-ipaymentapp/Pods-ipaymentapp.releaseinhouse.xcconfig"; sourceTree = "<group>"; };
		0E35CC392136E4DB000CAA36 /* PPQRCodeScanViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPQRCodeScanViewController.h; sourceTree = "<group>"; };
		0E35CC3A2136E4DB000CAA36 /* PPQRCodeScanViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PPQRCodeScanViewController.m; sourceTree = "<group>"; };
		0E35CC3C2136E4FE000CAA36 /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = System/Library/Frameworks/AVFoundation.framework; sourceTree = SDKROOT; };
		0E35CC3E2136E84F000CAA36 /* PPQRScanChildViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPQRScanChildViewController.h; sourceTree = "<group>"; };
		0E35CC3F2136E84F000CAA36 /* PPQRScanChildViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PPQRScanChildViewController.m; sourceTree = "<group>"; };
		0E35CC412136F4A1000CAA36 /* PPBarCoverView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPBarCoverView.h; sourceTree = "<group>"; };
		0E35CC422136F4A1000CAA36 /* PPBarCoverView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PPBarCoverView.m; sourceTree = "<group>"; };
		0E89D54F2180BE5E0066135A /* mt_security.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = mt_security.png; sourceTree = "<group>"; };
		23282186CDA6A31079E3CAF0 /* Pods-ipaymentapp.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ipaymentapp.debug.xcconfig"; path = "../Pods/Target Support Files/Pods-ipaymentapp/Pods-ipaymentapp.debug.xcconfig"; sourceTree = "<group>"; };
		2348A471211871CB00AA1AA7 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		2348A472211871CC00AA1AA7 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		2348A473211871CD00AA1AA7 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		2348A474211871CD00AA1AA7 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		2348A475211871CD00AA1AA7 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		2348A476211871CD00AA1AA7 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		2348A477211871CE00AA1AA7 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		239EEE4A21D0D8F500CF0556 /* PPBarcodeCashierHasParametersViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPBarcodeCashierHasParametersViewController.h; sourceTree = "<group>"; };
		239EEE4B21D0D8F500CF0556 /* PPBarcodeCashierHasParametersViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PPBarcodeCashierHasParametersViewController.m; sourceTree = "<group>"; };
		2CF69FD0235839DF00093F0C /* MerchantWalletEntranceViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MerchantWalletEntranceViewController.h; sourceTree = "<group>"; };
		2CF69FD1235839DF00093F0C /* MerchantWalletEntranceViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MerchantWalletEntranceViewController.m; sourceTree = "<group>"; };
		3498D9E62D75A3E3001490BC /* CoreML.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreML.framework; path = System/Library/Frameworks/CoreML.framework; sourceTree = SDKROOT; };
		35148B4C2553DE3300EF3E38 /* PPOrderDetailViewCustomCell.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PPOrderDetailViewCustomCell.m; sourceTree = "<group>"; };
		35148B4E2553DE4400EF3E38 /* PPOrderDetailViewCustomCell.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPOrderDetailViewCustomCell.h; sourceTree = "<group>"; };
		354E323A88BC676126FFF525 /* Pods-ipaymentappTests.dailybuild.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ipaymentappTests.dailybuild.xcconfig"; path = "../Pods/Target Support Files/Pods-ipaymentappTests/Pods-ipaymentappTests.dailybuild.xcconfig"; sourceTree = "<group>"; };
		356E0BA825529994001F94E0 /* PPRefundedMoneyInputModalView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPRefundedMoneyInputModalView.h; sourceTree = "<group>"; };
		356E0BA9255299AF001F94E0 /* PPRefundedMoneyInputModalView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PPRefundedMoneyInputModalView.m; sourceTree = "<group>"; };
		359B724324B71BAF00B74517 /* UIColor+Addition.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIColor+Addition.h"; sourceTree = "<group>"; };
		359B724424B71BD400B74517 /* UIColor+Addition.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIColor+Addition.m"; sourceTree = "<group>"; };
		35FCDEC624AF4B0A0090CE72 /* MTBaseViewController+DarkModeNavBarStyle.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "MTBaseViewController+DarkModeNavBarStyle.h"; sourceTree = "<group>"; };
		35FCDEC724AF4B0A0090CE72 /* MTBaseViewController+DarkModeNavBarStyle.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "MTBaseViewController+DarkModeNavBarStyle.m"; sourceTree = "<group>"; };
		4112DA3A26C6981000E6A58A /* PPHalfPageDebugViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPHalfPageDebugViewController.h; sourceTree = "<group>"; };
		4112DA3B26C6981000E6A58A /* PPHalfPageDebugViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PPHalfPageDebugViewController.m; sourceTree = "<group>"; };
		412C5C96229D94EC0062F553 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		412C5C97229D94ED0062F553 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		41452BC7226DDD070018567A /* PPHostSwitcher.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPHostSwitcher.h; sourceTree = "<group>"; };
		41452BC8226DDD070018567A /* PPHostSwitcher.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPHostSwitcher.m; sourceTree = "<group>"; };
		41A09A7221182CAC0020B8B4 /* PPRiskErrorProcessController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPRiskErrorProcessController.m; sourceTree = "<group>"; };
		41A09A7321182CAC0020B8B4 /* PPRiskErrorProcessController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPRiskErrorProcessController.h; sourceTree = "<group>"; };
		41C7670020B6D3A2006A911A /* PPOpenPayDebugViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPOpenPayDebugViewController.h; sourceTree = "<group>"; };
		41C7670120B6D3A2006A911A /* PPOpenPayDebugViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PPOpenPayDebugViewController.m; sourceTree = "<group>"; };
		41C7670420B71CEE006A911A /* MeituanPaySDK.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MeituanPaySDK.h; sourceTree = "<group>"; };
		41C7670520B71CEE006A911A /* MeituanPaySDKDefine.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MeituanPaySDKDefine.h; sourceTree = "<group>"; };
		41C7670620B71CEF006A911A /* MeituanPaySDK.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MeituanPaySDK.m; sourceTree = "<group>"; };
		421F14E621BA77A10045599C /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		421F14E721BA77A10045599C /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		42B4E0CBE48E46E9D7FE139F /* Pods-ipaymentappTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ipaymentappTests.debug.xcconfig"; path = "../Pods/Target Support Files/Pods-ipaymentappTests/Pods-ipaymentappTests.debug.xcconfig"; sourceTree = "<group>"; };
		42BAFDDA1FCC10A700106D04 /* PPFinancialKitViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPFinancialKitViewController.h; sourceTree = "<group>"; };
		42BAFDDB1FCC10A700106D04 /* PPFinancialKitViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PPFinancialKitViewController.m; sourceTree = "<group>"; };
		561A89671F380B8E000F4336 /* SAKDomainObject+Debug.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SAKDomainObject+Debug.h"; sourceTree = "<group>"; };
		561A89681F380B8E000F4336 /* SAKDomainObject+Debug.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "SAKDomainObject+Debug.m"; sourceTree = "<group>"; };
		564A63932048FD7500AA03FC /* PPHelloPayController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PPHelloPayController.swift; sourceTree = "<group>"; };
		564A63952049332700AA03FC /* PPPaymentSettingView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PPPaymentSettingView.swift; sourceTree = "<group>"; };
		56B03D4F2074E3FB004D48FB /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Images.xcassets; sourceTree = "<group>"; };
		56C953B51E6009360012B8AF /* PPRefundedOrderListViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPRefundedOrderListViewController.h; sourceTree = "<group>"; };
		56C953B61E6009360012B8AF /* PPRefundedOrderListViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPRefundedOrderListViewController.m; sourceTree = "<group>"; };
		56C953B81E600D980012B8AF /* PPRefundedOrderCellTableViewCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPRefundedOrderCellTableViewCell.h; sourceTree = "<group>"; };
		56C953B91E600D980012B8AF /* PPRefundedOrderCellTableViewCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPRefundedOrderCellTableViewCell.m; sourceTree = "<group>"; };
		56C953BE1E600F7E0012B8AF /* PPRefundedOrderUIObject.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPRefundedOrderUIObject.h; sourceTree = "<group>"; };
		56C953BF1E600F7E0012B8AF /* PPRefundedOrderUIObject.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPRefundedOrderUIObject.m; sourceTree = "<group>"; };
		56C953C11E600F9C0012B8AF /* PPRefundedOrder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPRefundedOrder.h; sourceTree = "<group>"; };
		56C953C21E600F9C0012B8AF /* PPRefundedOrder.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPRefundedOrder.m; sourceTree = "<group>"; };
		56C953C41E6024F00012B8AF /* PPRefundedOrderViewModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPRefundedOrderViewModel.h; sourceTree = "<group>"; };
		56C953C51E6024F00012B8AF /* PPRefundedOrderViewModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPRefundedOrderViewModel.m; sourceTree = "<group>"; };
		56F7DEC12046824200E4C1BC /* ipaymentapp-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "ipaymentapp-Bridging-Header.h"; sourceTree = "<group>"; };
		56F7DEC22046824200E4C1BC /* PPSubmitOrderViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PPSubmitOrderViewController.swift; sourceTree = "<group>"; };
		56F7DEC52046AAF600E4C1BC /* UIColor+Addtion.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "UIColor+Addtion.swift"; sourceTree = "<group>"; };
		56F7DEC62046AAF600E4C1BC /* UIFont+Addition.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = "UIFont+Addition.swift"; sourceTree = "<group>"; };
		56F7DEC92046C3F600E4C1BC /* NSObject+MLeaksFinderSwitch.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "NSObject+MLeaksFinderSwitch.h"; sourceTree = "<group>"; };
		56F7DECA2046C3F600E4C1BC /* NSObject+MLeaksFinderSwitch.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "NSObject+MLeaksFinderSwitch.m"; sourceTree = "<group>"; };
		5A0F380D1E4B1465003D98CA /* SAKHostSwitcherURLProtocol+APPMock.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SAKHostSwitcherURLProtocol+APPMock.h"; sourceTree = "<group>"; };
		5A0F380E1E4B1465003D98CA /* SAKHostSwitcherURLProtocol+APPMock.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "SAKHostSwitcherURLProtocol+APPMock.m"; sourceTree = "<group>"; };
		5A8ED1781E6D3DBD00D2F558 /* PPRegisterAPPMock.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPRegisterAPPMock.h; sourceTree = "<group>"; };
		5A8ED1791E6D3DBD00D2F558 /* PPRegisterAPPMock.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPRegisterAPPMock.m; sourceTree = "<group>"; };
		5A8ED17B1E6D3EC000D2F558 /* SAKBaseModel+PPHook.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SAKBaseModel+PPHook.h"; sourceTree = "<group>"; };
		5A8ED17C1E6D3EC000D2F558 /* SAKBaseModel+PPHook.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "SAKBaseModel+PPHook.m"; sourceTree = "<group>"; };
		5AB978681E52ABC80089C6C2 /* PPTitansAdapter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = PPTitansAdapter.h; path = Titans/PPTitansAdapter.h; sourceTree = "<group>"; };
		5AB978691E52ABC80089C6C2 /* PPTitansAdapter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = PPTitansAdapter.m; path = Titans/PPTitansAdapter.m; sourceTree = "<group>"; };
		5AB9786B1E52AC250089C6C2 /* PPTitansNamespace.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = PPTitansNamespace.h; path = Titans/PPTitansNamespace.h; sourceTree = "<group>"; };
		5AB9786C1E52AC250089C6C2 /* PPTitansNamespace.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = PPTitansNamespace.m; path = Titans/PPTitansNamespace.m; sourceTree = "<group>"; };
		624FECA4E4E8D6B4A61E3D44 /* Pods-ipaymentappTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ipaymentappTests.release.xcconfig"; path = "../Pods/Target Support Files/Pods-ipaymentappTests/Pods-ipaymentappTests.release.xcconfig"; sourceTree = "<group>"; };
		672D372B1E600308001DFB54 /* PPDataStorageViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPDataStorageViewController.h; sourceTree = "<group>"; };
		672D372C1E600308001DFB54 /* PPDataStorageViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPDataStorageViewController.m; sourceTree = "<group>"; };
		6745287721D390BC0018BB05 /* PPDebugPanel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPDebugPanel.h; sourceTree = "<group>"; };
		6745287821D390BC0018BB05 /* PPDebugPanel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPDebugPanel.m; sourceTree = "<group>"; };
		67754C70248140EA0000590B /* SPKTitleSwitchTableViewCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPKTitleSwitchTableViewCell.h; sourceTree = "<group>"; };
		67754C71248140EA0000590B /* SPKTitleSwitchTableViewCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPKTitleSwitchTableViewCell.m; sourceTree = "<group>"; };
		67754C73248141420000590B /* SPKCommonTableView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPKCommonTableView.m; sourceTree = "<group>"; };
		67754C74248141420000590B /* SPKCommonTableView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPKCommonTableView.h; sourceTree = "<group>"; };
		67754C762481418F0000590B /* SPKScrollViewRefreshControl.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPKScrollViewRefreshControl.h; sourceTree = "<group>"; };
		67754C772481418F0000590B /* SPKScrollViewRefreshControl.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPKScrollViewRefreshControl.m; sourceTree = "<group>"; };
		67754C79248141C30000590B /* SPKScrollViewDragControl.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPKScrollViewDragControl.h; sourceTree = "<group>"; };
		67754C7A248141C30000590B /* SPKScrollViewDragControl.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPKScrollViewDragControl.m; sourceTree = "<group>"; };
		67754C7C248142670000590B /* SPKScrollViewLoadMoreControl.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPKScrollViewLoadMoreControl.m; sourceTree = "<group>"; };
		67754C7D248142670000590B /* SPKScrollViewLoadMoreControl.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPKScrollViewLoadMoreControl.h; sourceTree = "<group>"; };
		67754C7F248142870000590B /* SPKNetworkErrorView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPKNetworkErrorView.h; sourceTree = "<group>"; };
		67754C80248142870000590B /* SPKNetworkErrorView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPKNetworkErrorView.m; sourceTree = "<group>"; };
		67754C82248142BA0000590B /* SPKRefreshNormalFooter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPKRefreshNormalFooter.h; sourceTree = "<group>"; };
		67754C83248142BA0000590B /* SPKRefreshNormalHeader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPKRefreshNormalHeader.h; sourceTree = "<group>"; };
		67754C84248142BA0000590B /* SPKRefreshNormalHeader.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPKRefreshNormalHeader.m; sourceTree = "<group>"; };
		67754C85248142BA0000590B /* SPKRefreshNormalFooter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPKRefreshNormalFooter.m; sourceTree = "<group>"; };
		67754C88248142DB0000590B /* SPKRefreshHeader.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPKRefreshHeader.m; sourceTree = "<group>"; };
		67754C89248142DB0000590B /* SPKRefreshHeader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPKRefreshHeader.h; sourceTree = "<group>"; };
		67754C8B248142FC0000590B /* SPKRefreshComponent.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPKRefreshComponent.m; sourceTree = "<group>"; };
		67754C8C248142FC0000590B /* SPKRefreshComponent.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPKRefreshComponent.h; sourceTree = "<group>"; };
		67754C8E2481431C0000590B /* UIScrollView+SPKRefresh.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIScrollView+SPKRefresh.h"; sourceTree = "<group>"; };
		67754C8F2481431D0000590B /* UIScrollView+SPKRefresh.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIScrollView+SPKRefresh.m"; sourceTree = "<group>"; };
		67754C91248143350000590B /* SPKRefreshConst.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPKRefreshConst.m; sourceTree = "<group>"; };
		67754C92248143350000590B /* SPKRefreshConst.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPKRefreshConst.h; sourceTree = "<group>"; };
		67754C942481434E0000590B /* SPKRefreshFooter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPKRefreshFooter.h; sourceTree = "<group>"; };
		67754C952481434F0000590B /* SPKRefreshFooter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPKRefreshFooter.m; sourceTree = "<group>"; };
		67754C97248144310000590B /* SPKScrollPageView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPKScrollPageView.h; sourceTree = "<group>"; };
		67754C98248144310000590B /* SPKScrollPageView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPKScrollPageView.m; sourceTree = "<group>"; };
		67754C9A248144680000590B /* SPKTitleSwitchCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPKTitleSwitchCell.m; sourceTree = "<group>"; };
		67754C9B248144680000590B /* SPKTitleSwitchCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPKTitleSwitchCell.h; sourceTree = "<group>"; };
		67754C9D248145640000590B /* SPKCommonCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPKCommonCell.m; sourceTree = "<group>"; };
		67754C9E248145640000590B /* SPKCommonCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPKCommonCell.h; sourceTree = "<group>"; };
		67754CA0248145990000590B /* SPKTitleTipCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPKTitleTipCell.m; sourceTree = "<group>"; };
		67754CA1248145990000590B /* SPKTitleTipCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPKTitleTipCell.h; sourceTree = "<group>"; };
		67754CA3248145B20000590B /* SPKTitleImageCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPKTitleImageCell.m; sourceTree = "<group>"; };
		67754CA4248145B20000590B /* SPKTitleImageCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPKTitleImageCell.h; sourceTree = "<group>"; };
		67754CA6248145CA0000590B /* SPKTitleInputCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPKTitleInputCell.m; sourceTree = "<group>"; };
		67754CA7248145CB0000590B /* SPKTitleInputCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPKTitleInputCell.h; sourceTree = "<group>"; };
		67754CA92481460A0000590B /* SPKTitleTipTableViewCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPKTitleTipTableViewCell.m; sourceTree = "<group>"; };
		67754CAA2481460A0000590B /* SPKTitleTipTableViewCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPKTitleTipTableViewCell.h; sourceTree = "<group>"; };
		67754CAC248146300000590B /* SPKTitleImageTableViewCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPKTitleImageTableViewCell.m; sourceTree = "<group>"; };
		67754CAD248146310000590B /* SPKTitleImageTableViewCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPKTitleImageTableViewCell.h; sourceTree = "<group>"; };
		67754CAF248146590000590B /* SPKTitleInputTableViewCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPKTitleInputTableViewCell.m; sourceTree = "<group>"; };
		67754CB02481465A0000590B /* SPKTitleInputTableViewCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPKTitleInputTableViewCell.h; sourceTree = "<group>"; };
		6A6F1EFA21B015EC00F48300 /* PPUIKit2ViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPUIKit2ViewController.h; sourceTree = "<group>"; };
		6A6F1EFB21B015EC00F48300 /* PPUIKit2ViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PPUIKit2ViewController.m; sourceTree = "<group>"; };
		6A6F1EFD21B01BB400F48300 /* PPUIKit2TabSwitchViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPUIKit2TabSwitchViewController.h; sourceTree = "<group>"; };
		6A6F1EFE21B01BB400F48300 /* PPUIKit2TabSwitchViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PPUIKit2TabSwitchViewController.m; sourceTree = "<group>"; };
		6A6F1F0021B01BD000F48300 /* PPUIKit2ButtonViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPUIKit2ButtonViewController.h; sourceTree = "<group>"; };
		6A6F1F0121B01BD000F48300 /* PPUIKit2ButtonViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PPUIKit2ButtonViewController.m; sourceTree = "<group>"; };
		6A6F1F0321B01C0900F48300 /* PPUIKit2AgreementViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPUIKit2AgreementViewController.h; sourceTree = "<group>"; };
		6A6F1F0421B01C0900F48300 /* PPUIKit2AgreementViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PPUIKit2AgreementViewController.m; sourceTree = "<group>"; };
		6A6F1F0621B01C2F00F48300 /* PPUIKit2PickerViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPUIKit2PickerViewController.h; sourceTree = "<group>"; };
		6A6F1F0721B01C2F00F48300 /* PPUIKit2PickerViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PPUIKit2PickerViewController.m; sourceTree = "<group>"; };
		6A6F1F0921B01C4B00F48300 /* PPUIKit2NoticeViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPUIKit2NoticeViewController.h; sourceTree = "<group>"; };
		6A6F1F0A21B01C4B00F48300 /* PPUIKit2NoticeViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PPUIKit2NoticeViewController.m; sourceTree = "<group>"; };
		6A6F1F0C21B01C6600F48300 /* PPUIKit2BannerViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPUIKit2BannerViewController.h; sourceTree = "<group>"; };
		6A6F1F0D21B01C6600F48300 /* PPUIKit2BannerViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PPUIKit2BannerViewController.m; sourceTree = "<group>"; };
		6A6F1F0F21B01C7900F48300 /* PPUIKit2CardViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPUIKit2CardViewController.h; sourceTree = "<group>"; };
		6A6F1F1021B01C7900F48300 /* PPUIKit2CardViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PPUIKit2CardViewController.m; sourceTree = "<group>"; };
		6A6F1F1221B01C9D00F48300 /* PPUIKit2ListItemViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPUIKit2ListItemViewController.h; sourceTree = "<group>"; };
		6A6F1F1321B01C9D00F48300 /* PPUIKit2ListItemViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PPUIKit2ListItemViewController.m; sourceTree = "<group>"; };
		6A6F1F1521B01CDA00F48300 /* PPUIKit2ActivityViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPUIKit2ActivityViewController.h; sourceTree = "<group>"; };
		6A6F1F1621B01CDA00F48300 /* PPUIKit2ActivityViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PPUIKit2ActivityViewController.m; sourceTree = "<group>"; };
		6A6F1F1821B01CF700F48300 /* PPUIKit2ToastViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPUIKit2ToastViewController.h; sourceTree = "<group>"; };
		6A6F1F1921B01CF700F48300 /* PPUIKit2ToastViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PPUIKit2ToastViewController.m; sourceTree = "<group>"; };
		6A6F1F1B21B01D1100F48300 /* PPUIKit2AlertViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPUIKit2AlertViewController.h; sourceTree = "<group>"; };
		6A6F1F1C21B01D1100F48300 /* PPUIKit2AlertViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PPUIKit2AlertViewController.m; sourceTree = "<group>"; };
		6A6F1F1E21B01D3A00F48300 /* PPUIKitActionSheetViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPUIKitActionSheetViewController.h; sourceTree = "<group>"; };
		6A6F1F1F21B01D3A00F48300 /* PPUIKitActionSheetViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PPUIKitActionSheetViewController.m; sourceTree = "<group>"; };
		6A74189D22CB890C00337AEB /* PPCertificateCameraViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPCertificateCameraViewController.h; sourceTree = "<group>"; };
		6A74189E22CB890C00337AEB /* PPTestCertificateCameraViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPTestCertificateCameraViewController.m; sourceTree = "<group>"; };
		6A74189F22CB890C00337AEB /* PPCertificateCameraViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPCertificateCameraViewController.m; sourceTree = "<group>"; };
		6A7418A022CB890C00337AEB /* PPTestCertificateCameraViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPTestCertificateCameraViewController.h; sourceTree = "<group>"; };
		6AE9D3F321B7D8A100113E50 /* MTFUTableViewCellAccessoryImage.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = MTFUTableViewCellAccessoryImage.png; sourceTree = "<group>"; };
		6AE9D3F921B8C04B00113E50 /* bankicon.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = bankicon.png; sourceTree = "<group>"; };
		6AE9D3FB21B8CDEF00113E50 /* bankcheck.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = bankcheck.png; sourceTree = "<group>"; };
		6E07D2F51D3614FB007F0EE1 /* alertViewButtonTitles.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = alertViewButtonTitles.plist; sourceTree = "<group>"; };
		6E1A9C6E1DE7115700BB472B /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		6E1A9C6F1DE7115700BB472B /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		6E1A9C701DE7115700BB472B /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		6E1A9C711DE7115700BB472B /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		6E1A9C721DE7115700BB472B /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		6E1A9C731DE7115700BB472B /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		6E1A9C741DE7115700BB472B /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		6E1A9C751DE7115700BB472B /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		6E1A9C761DE7115700BB472B /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		6E1A9C771DE7115700BB472B /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		6E1A9C781DE7115700BB472B /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		6E1A9C791DE7115700BB472B /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		6E1A9C7A1DE7115700BB472B /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		6E1A9C7B1DE7115700BB472B /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		6E1A9C7C1DE7115700BB472B /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		6E51795D1F9889DA007C8EF7 /* ipaymentapp.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = ipaymentapp.entitlements; sourceTree = "<group>"; };
		6E82B2301F96EDE800CBECA9 /* PPNFCManagementViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPNFCManagementViewController.h; sourceTree = "<group>"; };
		6E82B2311F96EDE800CBECA9 /* PPNFCManagementViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PPNFCManagementViewController.m; sourceTree = "<group>"; };
		6EA4DC6B1DCAF3E300C316D3 /* PPDebugWindow.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPDebugWindow.h; sourceTree = "<group>"; };
		6EA4DC6C1DCAF3E300C316D3 /* PPDebugWindow.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPDebugWindow.m; sourceTree = "<group>"; };
		6EA4DC6E1DCAF42400C316D3 /* SAKBaseModel_Private.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SAKBaseModel_Private.h; sourceTree = "<group>"; };
		6EB5D7981DE31FA7007D7807 /* libSAKAccount.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libSAKAccount.a; path = "../../../Library/Developer/Xcode/DerivedData/ipaymentapp-gcqvgjkgiccnzxebuyidbfebifbg/Build/Products/ReleaseInhouse-iphonesimulator/libSAKAccount.a"; sourceTree = "<group>"; };
		6EC7EF2A1CD0C358006921EE /* paymentapp_monkey.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = paymentapp_monkey.m; sourceTree = "<group>"; };
		6EC7EF2B1CD0C358006921EE /* monkey.sh */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.script.sh; path = monkey.sh; sourceTree = "<group>"; };
		6EC7EF2E1CD0C443006921EE /* touch.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = touch.png; sourceTree = "<group>"; };
		7A75A24BF69A1E060F1D0E33 /* Pods-ipaymentapp.dailybuild.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ipaymentapp.dailybuild.xcconfig"; path = "../Pods/Target Support Files/Pods-ipaymentapp/Pods-ipaymentapp.dailybuild.xcconfig"; sourceTree = "<group>"; };
		7AB2EAE91D45C59000F24AEA /* PPWalletEntranceInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPWalletEntranceInfo.h; sourceTree = "<group>"; };
		7AB2EAEA1D45C59000F24AEA /* PPWalletEntranceInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPWalletEntranceInfo.m; sourceTree = "<group>"; };
		7AB2EAEC1D45D50400F24AEA /* ipaymentapp-Prefix.pch */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "ipaymentapp-Prefix.pch"; sourceTree = "<group>"; };
		7AB2EAF01D45E14700F24AEA /* PPMineModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPMineModel.h; sourceTree = "<group>"; };
		7AB2EAF11D45E14700F24AEA /* PPMineModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPMineModel.m; sourceTree = "<group>"; };
		7AB2EAF31D45ECCC00F24AEA /* PPMineViewModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPMineViewModel.h; sourceTree = "<group>"; };
		7AB2EAF41D45ECCC00F24AEA /* PPMineViewModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPMineViewModel.m; sourceTree = "<group>"; };
		7AB625FE1D52F96700804FCE /* WalletEntranceInfo.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = WalletEntranceInfo.plist; sourceTree = "<group>"; };
		9D7AC605843107E2C9604338 /* Pods-ipaymentappTests.releaseinhouse.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ipaymentappTests.releaseinhouse.xcconfig"; path = "../Pods/Target Support Files/Pods-ipaymentappTests/Pods-ipaymentappTests.releaseinhouse.xcconfig"; sourceTree = "<group>"; };
		AD7CEEF2264CC8B10066A10F /* libbz2.1.0.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libbz2.1.0.tbd; path = usr/lib/libbz2.1.0.tbd; sourceTree = SDKROOT; };
		B43BDFF21D4F71BC00F797F8 /* Default-568.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "Default-568.png"; sourceTree = "<group>"; };
		B4A8E77E1BA6ABFD008C89D0 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		B4A8E77F1BA6ABFD008C89D0 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		B4A8E7841BA6ADCF008C89D0 /* PPAppSelectViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPAppSelectViewController.h; sourceTree = "<group>"; };
		B4A8E7851BA6ADCF008C89D0 /* PPAppSelectViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPAppSelectViewController.m; sourceTree = "<group>"; };
		B4A8E7871BA6B2B1008C89D0 /* PPAppSelectTableViewCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPAppSelectTableViewCell.h; sourceTree = "<group>"; };
		B4A8E7881BA6B2B1008C89D0 /* PPAppSelectTableViewCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPAppSelectTableViewCell.m; sourceTree = "<group>"; };
		BCB55196200C9D6800711A42 /* MTBaseViewController+IPayment.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "MTBaseViewController+IPayment.h"; sourceTree = "<group>"; };
		BCB55197200C9D6800711A42 /* MTBaseViewController+IPayment.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "MTBaseViewController+IPayment.m"; sourceTree = "<group>"; };
		BCB5519A200DDCA000711A42 /* PPPicassoDebugViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPPicassoDebugViewController.h; sourceTree = "<group>"; };
		BCB5519B200DDCA000711A42 /* PPPicassoDebugViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PPPicassoDebugViewController.m; sourceTree = "<group>"; };
		BCDEA8C2200E4FDD0084517C /* UITextField+MemoryLeak.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UITextField+MemoryLeak.h"; sourceTree = "<group>"; };
		BCDEA8C3200E4FDD0084517C /* UITextField+MemoryLeak.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UITextField+MemoryLeak.m"; sourceTree = "<group>"; };
		D30606251FCEAAEA00D5D849 /* PPVerifyPasswordViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPVerifyPasswordViewController.h; sourceTree = "<group>"; };
		D30606261FCEAAEA00D5D849 /* PPVerifyPasswordViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PPVerifyPasswordViewController.m; sourceTree = "<group>"; };
		D30606281FD584AA00D5D849 /* PPTestViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPTestViewController.h; sourceTree = "<group>"; };
		D30606291FD584AA00D5D849 /* PPTestViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PPTestViewController.m; sourceTree = "<group>"; };
		D31B5EDD203021F900FC3482 /* PPScrollPageViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPScrollPageViewController.m; sourceTree = "<group>"; };
		D31B5EDE203021F900FC3482 /* PPUIKitViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPUIKitViewController.h; sourceTree = "<group>"; };
		D31B5EDF203021F900FC3482 /* PPSPKRefreshTestViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPSPKRefreshTestViewController.m; sourceTree = "<group>"; };
		D31B5EE0203021F900FC3482 /* PPNormalizedCellViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPNormalizedCellViewController.m; sourceTree = "<group>"; };
		D31B5EE1203021F900FC3482 /* PPAlertViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPAlertViewController.h; sourceTree = "<group>"; };
		D31B5EE2203021F900FC3482 /* PPAgreementViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPAgreementViewController.h; sourceTree = "<group>"; };
		D31B5EE3203021F900FC3482 /* PPAgreementViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPAgreementViewController.m; sourceTree = "<group>"; };
		D31B5EE4203021F900FC3482 /* PPAlertViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPAlertViewController.m; sourceTree = "<group>"; };
		D31B5EE5203021F900FC3482 /* PPNormalizedCellViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPNormalizedCellViewController.h; sourceTree = "<group>"; };
		D31B5EE6203021F900FC3482 /* PPScrollPageViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPScrollPageViewController.h; sourceTree = "<group>"; };
		D31B5EE7203021F900FC3482 /* PPUIKitViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPUIKitViewController.m; sourceTree = "<group>"; };
		D31B5EE8203021FA00FC3482 /* PPSPKRefreshTestViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPSPKRefreshTestViewController.h; sourceTree = "<group>"; };
		D31B5EEF20303EFF00FC3482 /* PPBannerViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPBannerViewController.h; sourceTree = "<group>"; };
		D31B5EF020303EFF00FC3482 /* PPBannerViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PPBannerViewController.m; sourceTree = "<group>"; };
		D31B5EF220306DB500FC3482 /* PPToastViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPToastViewController.h; sourceTree = "<group>"; };
		D31B5EF320306DB500FC3482 /* PPToastViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PPToastViewController.m; sourceTree = "<group>"; };
		D31B5EF52030774C00FC3482 /* PPLoadingViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPLoadingViewController.h; sourceTree = "<group>"; };
		D31B5EF62030774C00FC3482 /* PPLoadingViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PPLoadingViewController.m; sourceTree = "<group>"; };
		D31B5EF820307E7000FC3482 /* PPNetworkErrorViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPNetworkErrorViewController.h; sourceTree = "<group>"; };
		D31B5EF920307E7000FC3482 /* PPNetworkErrorViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PPNetworkErrorViewController.m; sourceTree = "<group>"; };
		D31B5EFC203E97DD00FC3482 /* PPScanCardViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPScanCardViewController.h; sourceTree = "<group>"; };
		D31B5EFD203E97DD00FC3482 /* PPScanCardViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PPScanCardViewController.m; sourceTree = "<group>"; };
		D31B5F0F203EBE6500FC3482 /* PPFinacialUtilityViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPFinacialUtilityViewController.h; sourceTree = "<group>"; };
		D31B5F10203EBE6500FC3482 /* PPFinacialUtilityViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PPFinacialUtilityViewController.m; sourceTree = "<group>"; };
		D31B5F12203EBEA800FC3482 /* PPFingerPrintPayViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPFingerPrintPayViewController.h; sourceTree = "<group>"; };
		D31B5F13203EBEA800FC3482 /* PPFingerPrintPayViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PPFingerPrintPayViewController.m; sourceTree = "<group>"; };
		D33FB0A6200CF30600E60F1D /* MessageUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MessageUI.framework; path = System/Library/Frameworks/MessageUI.framework; sourceTree = SDKROOT; };
		D33FB0A9200CF32B00E60F1D /* PPCrashReporter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PPCrashReporter.h; sourceTree = "<group>"; };
		D33FB0AA200CF32B00E60F1D /* PPCrashReporter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PPCrashReporter.m; sourceTree = "<group>"; };
		D3DEA9381FC3D65B0005E26B /* PPPaymentKitViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PPPaymentKitViewController.h; sourceTree = "<group>"; };
		D3DEA9391FC3D65B0005E26B /* PPPaymentKitViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PPPaymentKitViewController.m; sourceTree = "<group>"; };
		DC452069872A18B906441813 /* Pods-ipaymentapp.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ipaymentapp.release.xcconfig"; path = "../Pods/Target Support Files/Pods-ipaymentapp/Pods-ipaymentapp.release.xcconfig"; sourceTree = "<group>"; };
		************************ /* libPods-ipaymentapp.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-ipaymentapp.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		E746830D22A12FE400C49A17 /* MESHDebugViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MESHDebugViewController.h; sourceTree = "<group>"; };
		E746830E22A12FE400C49A17 /* MESHDebugViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MESHDebugViewController.m; sourceTree = "<group>"; };
		E746830F22A12FE400C49A17 /* MESHH5DebugViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MESHH5DebugViewController.h; sourceTree = "<group>"; };
		E746831022A12FE400C49A17 /* MESHH5DebugViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MESHH5DebugViewController.m; sourceTree = "<group>"; };
		E746831122A12FE400C49A17 /* MESHTest1ViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MESHTest1ViewController.h; sourceTree = "<group>"; };
		E746831222A12FE400C49A17 /* MESHTest1ViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MESHTest1ViewController.m; sourceTree = "<group>"; };
		E746831322A12FE400C49A17 /* SAKPayMeshService.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SAKPayMeshService.h; sourceTree = "<group>"; };
		E746831422A12FE400C49A17 /* SAKPayMeshService.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SAKPayMeshService.m; sourceTree = "<group>"; };
		F0152918C8AC14CF369ED16E /* libPods-ipaymentappTests.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-ipaymentappTests.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		FF9B00A52823CEBB00EF96A9 /* libSAKAccount-Core.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = "libSAKAccount-Core.a"; path = "../Pods/SAKAccount/bin/libSAKAccount-Core.a"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		0A6F02041B3AC46800F64F7B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				3498D9E72D75A3E3001490BC /* CoreML.framework in Frameworks */,
				FF9B00A62823CEBB00EF96A9 /* libSAKAccount-Core.a in Frameworks */,
				AD7CEEF3264CC8D40066A10F /* libbz2.1.0.tbd in Frameworks */,
				0E35CC3D2136E4FE000CAA36 /* AVFoundation.framework in Frameworks */,
				D33FB0A7200CF30600E60F1D /* MessageUI.framework in Frameworks */,
				E9CA8E40AE99B5C9C8222087 /* libPods-ipaymentapp.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0A6F02201B3AC46900F64F7B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				45185B91E876C2452225FAA6 /* libPods-ipaymentappTests.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		07E744CF2226646500896C60 /* CacheManager */ = {
			isa = PBXGroup;
			children = (
				07E744CC2226644B00896C60 /* PPCacheManagerViewController.h */,
				07E744CD2226644B00896C60 /* PPCacheManagerViewController.m */,
			);
			path = CacheManager;
			sourceTree = "<group>";
		};
		0A6F01FE1B3AC46800F64F7B = {
			isa = PBXGroup;
			children = (
				0A6F02091B3AC46800F64F7B /* ipaymentapp */,
				0A6F02261B3AC46900F64F7B /* ipaymentappTests */,
				0A6F02081B3AC46800F64F7B /* Products */,
				************************ /* Frameworks */,
				C68A6398F89FF3E7C77DA8CC /* Pods */,
			);
			sourceTree = "<group>";
		};
		0A6F02081B3AC46800F64F7B /* Products */ = {
			isa = PBXGroup;
			children = (
				0A6F02071B3AC46800F64F7B /* ipaymentapp.app */,
				0A6F02231B3AC46900F64F7B /* ipaymentappTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		0A6F02091B3AC46800F64F7B /* ipaymentapp */ = {
			isa = PBXGroup;
			children = (
				56F7DEC42046AAB400E4C1BC /* SwiftFoundation */,
				6E51795D1F9889DA007C8EF7 /* ipaymentapp.entitlements */,
				7AB625FE1D52F96700804FCE /* WalletEntranceInfo.plist */,
				B43BDFF21D4F71BC00F797F8 /* Default-568.png */,
				B4A8E7821BA6AD9D008C89D0 /* Module */,
				0A6F02331B3AC65F00F64F7B /* Model */,
				BCDEA8C1200E4FBE0084517C /* MemoryLeak */,
				0A6F02421B3AC65F00F64F7B /* viewController */,
				0A6F025B1B3AC65F00F64F7B /* Resource */,
				0A6F02671B3AC65F00F64F7B /* DefaultAppInfo.plist */,
				0A6F020E1B3AC46800F64F7B /* AppDelegate.h */,
				0A6F020F1B3AC46800F64F7B /* AppDelegate.m */,
				56B03D4F2074E3FB004D48FB /* Images.xcassets */,
				0A6F021C1B3AC46900F64F7B /* LaunchScreen.xib */,
				0A6F020A1B3AC46800F64F7B /* Supporting Files */,
			);
			path = ipaymentapp;
			sourceTree = "<group>";
		};
		0A6F020A1B3AC46800F64F7B /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				0E89D54F2180BE5E0066135A /* mt_security.png */,
				0A6F020B1B3AC46800F64F7B /* Info.plist */,
				0A1DAB941B4CE05A00C34A65 /* PortalSettings.plist */,
				0A6F020C1B3AC46800F64F7B /* main.m */,
				7AB2EAEC1D45D50400F24AEA /* ipaymentapp-Prefix.pch */,
				56F7DEC12046824200E4C1BC /* ipaymentapp-Bridging-Header.h */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		0A6F02261B3AC46900F64F7B /* ipaymentappTests */ = {
			isa = PBXGroup;
			children = (
				6EC7EF2A1CD0C358006921EE /* paymentapp_monkey.m */,
				6EC7EF2B1CD0C358006921EE /* monkey.sh */,
				0A6F02271B3AC46900F64F7B /* Supporting Files */,
			);
			path = ipaymentappTests;
			sourceTree = "<group>";
		};
		0A6F02271B3AC46900F64F7B /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				6EC7EF2E1CD0C443006921EE /* touch.png */,
				0A6F02281B3AC46900F64F7B /* Info.plist */,
				6E07D2F51D3614FB007F0EE1 /* alertViewButtonTitles.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		0A6F02331B3AC65F00F64F7B /* Model */ = {
			isa = PBXGroup;
			children = (
				0AAAAA621B42C95B0099BC4A /* MTNavigationController+IPayment.h */,
				0AAAAA631B42C95B0099BC4A /* MTNavigationController+IPayment.m */,
				0A6F02341B3AC65F00F64F7B /* NSDate+Time.h */,
				0A6F02351B3AC65F00F64F7B /* NSDate+Time.m */,
				0A6F02361B3AC65F00F64F7B /* PPContentInfo.h */,
				0A6F02371B3AC65F00F64F7B /* PPContentInfo.m */,
				0A6F02381B3AC65F00F64F7B /* PPModel.h */,
				0A6F02391B3AC65F00F64F7B /* PPModel.m */,
				0A6F023C1B3AC65F00F64F7B /* PPOrder.h */,
				0A6F023D1B3AC65F00F64F7B /* PPOrder.m */,
				0A6F023E1B3AC65F00F64F7B /* PPService.h */,
				0A6F023F1B3AC65F00F64F7B /* PPService.m */,
				0A6F02401B3AC65F00F64F7B /* SAKBaseModel+CashierApp.h */,
				0A6F02411B3AC65F00F64F7B /* SAKBaseModel+CashierApp.m */,
				0A5DEB991B54D943002FCF3F /* PPAccountController.h */,
				0A5DEB9A1B54D943002FCF3F /* PPAccountController.m */,
				0A6908BF1B5790D500936191 /* PPOrderStatus.h */,
				0A0CE28E1B58E76E00DE7304 /* PPOrderStatus.m */,
				7AB2EAE91D45C59000F24AEA /* PPWalletEntranceInfo.h */,
				7AB2EAEA1D45C59000F24AEA /* PPWalletEntranceInfo.m */,
				7AB2EAF01D45E14700F24AEA /* PPMineModel.h */,
				7AB2EAF11D45E14700F24AEA /* PPMineModel.m */,
				7AB2EAF31D45ECCC00F24AEA /* PPMineViewModel.h */,
				7AB2EAF41D45ECCC00F24AEA /* PPMineViewModel.m */,
				6EA4DC6E1DCAF42400C316D3 /* SAKBaseModel_Private.h */,
				5A0F380D1E4B1465003D98CA /* SAKHostSwitcherURLProtocol+APPMock.h */,
				5A0F380E1E4B1465003D98CA /* SAKHostSwitcherURLProtocol+APPMock.m */,
				5A8ED1781E6D3DBD00D2F558 /* PPRegisterAPPMock.h */,
				5A8ED1791E6D3DBD00D2F558 /* PPRegisterAPPMock.m */,
				5A8ED17B1E6D3EC000D2F558 /* SAKBaseModel+PPHook.h */,
				5A8ED17C1E6D3EC000D2F558 /* SAKBaseModel+PPHook.m */,
				35FCDEC624AF4B0A0090CE72 /* MTBaseViewController+DarkModeNavBarStyle.h */,
				35FCDEC724AF4B0A0090CE72 /* MTBaseViewController+DarkModeNavBarStyle.m */,
				359B724324B71BAF00B74517 /* UIColor+Addition.h */,
				359B724424B71BD400B74517 /* UIColor+Addition.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		0A6F02421B3AC65F00F64F7B /* viewController */ = {
			isa = PBXGroup;
			children = (
				6A74189D22CB890C00337AEB /* PPCertificateCameraViewController.h */,
				6A74189F22CB890C00337AEB /* PPCertificateCameraViewController.m */,
				6A7418A022CB890C00337AEB /* PPTestCertificateCameraViewController.h */,
				6A74189E22CB890C00337AEB /* PPTestCertificateCameraViewController.m */,
				E746830622A12FBF00C49A17 /* mesh */,
				6745287721D390BC0018BB05 /* PPDebugPanel.h */,
				6745287821D390BC0018BB05 /* PPDebugPanel.m */,
				2CF69FD0235839DF00093F0C /* MerchantWalletEntranceViewController.h */,
				2CF69FD1235839DF00093F0C /* MerchantWalletEntranceViewController.m */,
				0790B07F205FAFE0004C8833 /* PPMineViewController.swift */,
				BCB55196200C9D6800711A42 /* MTBaseViewController+IPayment.h */,
				BCB55197200C9D6800711A42 /* MTBaseViewController+IPayment.m */,
				0A6F02431B3AC65F00F64F7B /* PPConfigTableViewCell.h */,
				0A6F02441B3AC65F00F64F7B /* PPConfigTableViewCell.m */,
				07686C0920592FB100046188 /* PPSelectMockViewController.swift */,
				07686C032058F39900046188 /* PPConfigViewController.swift */,
				0A6F02471B3AC65F00F64F7B /* PPContentTextFieldCell.h */,
				0A6F02481B3AC65F00F64F7B /* PPContentTextFieldCell.m */,
				0A6F024D1B3AC65F00F64F7B /* PPMyAccountCell.h */,
				0A6F024E1B3AC65F00F64F7B /* PPMyAccountCell.m */,
				0A6F024F1B3AC65F00F64F7B /* PPMyAccountView.h */,
				0A6F02501B3AC65F00F64F7B /* PPMyAccountView.m */,
				0A6F02511B3AC65F00F64F7B /* PPOrderDetailViewController.h */,
				0A6F02521B3AC65F00F64F7B /* PPOrderDetailViewController.m */,
				35148B4C2553DE3300EF3E38 /* PPOrderDetailViewCustomCell.m */,
				35148B4E2553DE4400EF3E38 /* PPOrderDetailViewCustomCell.h */,
				0A6F02531B3AC65F00F64F7B /* PPOrderlistViewController.h */,
				0A6F02541B3AC65F00F64F7B /* PPOrderlistViewController.m */,
				0A6F02551B3AC65F00F64F7B /* PPOrderTableViewCell.h */,
				0A6F02561B3AC65F00F64F7B /* PPOrderTableViewCell.m */,
				56C953B51E6009360012B8AF /* PPRefundedOrderListViewController.h */,
				56C953B61E6009360012B8AF /* PPRefundedOrderListViewController.m */,
				56C953B81E600D980012B8AF /* PPRefundedOrderCellTableViewCell.h */,
				56C953B91E600D980012B8AF /* PPRefundedOrderCellTableViewCell.m */,
				56C953BE1E600F7E0012B8AF /* PPRefundedOrderUIObject.h */,
				56C953BF1E600F7E0012B8AF /* PPRefundedOrderUIObject.m */,
				56C953C11E600F9C0012B8AF /* PPRefundedOrder.h */,
				56C953C21E600F9C0012B8AF /* PPRefundedOrder.m */,
				56C953C41E6024F00012B8AF /* PPRefundedOrderViewModel.h */,
				56C953C51E6024F00012B8AF /* PPRefundedOrderViewModel.m */,
				56F7DEC22046824200E4C1BC /* PPSubmitOrderViewController.swift */,
				564A63932048FD7500AA03FC /* PPHelloPayController.swift */,
				564A63952049332700AA03FC /* PPPaymentSettingView.swift */,
				42BAFDDA1FCC10A700106D04 /* PPFinancialKitViewController.h */,
				42BAFDDB1FCC10A700106D04 /* PPFinancialKitViewController.m */,
				239EEE4A21D0D8F500CF0556 /* PPBarcodeCashierHasParametersViewController.h */,
				239EEE4B21D0D8F500CF0556 /* PPBarcodeCashierHasParametersViewController.m */,
				D31B5EFB203E977200FC3482 /* PayBase */,
				D31B5EDC20301F1800FC3482 /* UIKit */,
				6A6F1EF921B0155400F48300 /* UIKit2 */,
				07E744CF2226646500896C60 /* CacheManager */,
				356E0BA825529994001F94E0 /* PPRefundedMoneyInputModalView.h */,
				356E0BA9255299AF001F94E0 /* PPRefundedMoneyInputModalView.m */,
				4112DA3A26C6981000E6A58A /* PPHalfPageDebugViewController.h */,
				4112DA3B26C6981000E6A58A /* PPHalfPageDebugViewController.m */,
			);
			path = viewController;
			sourceTree = "<group>";
		};
		0A6F025B1B3AC65F00F64F7B /* Resource */ = {
			isa = PBXGroup;
			children = (
				412C5C96229D94EC0062F553 /* <EMAIL> */,
				412C5C97229D94ED0062F553 /* <EMAIL> */,
				421F14E721BA77A10045599C /* <EMAIL> */,
				421F14E621BA77A10045599C /* <EMAIL> */,
				6AE9D3FB21B8CDEF00113E50 /* bankcheck.png */,
				6AE9D3F921B8C04B00113E50 /* bankicon.png */,
				6AE9D3F321B7D8A100113E50 /* MTFUTableViewCellAccessoryImage.png */,
				2348A473211871CD00AA1AA7 /* <EMAIL> */,
				2348A476211871CD00AA1AA7 /* <EMAIL> */,
				2348A471211871CB00AA1AA7 /* <EMAIL> */,
				2348A477211871CE00AA1AA7 /* <EMAIL> */,
				2348A475211871CD00AA1AA7 /* <EMAIL> */,
				2348A474211871CD00AA1AA7 /* <EMAIL> */,
				2348A472211871CC00AA1AA7 /* <EMAIL> */,
				6E1A9C6D1DE7113200BB472B /* H5_ICON */,
				B4A8E77E1BA6ABFD008C89D0 /* <EMAIL> */,
				B4A8E77F1BA6ABFD008C89D0 /* <EMAIL> */,
				0AAAAA651B42CA7B0099BC4A /* <EMAIL> */,
				0AAAAA661B42CA7B0099BC4A /* <EMAIL> */,
				0AAAAA671B42CA7B0099BC4A /* <EMAIL> */,
				0AAAAA681B42CA7B0099BC4A /* <EMAIL> */,
				0A6F025C1B3AC65F00F64F7B /* <EMAIL> */,
				0A6F025D1B3AC65F00F64F7B /* <EMAIL> */,
				0A6F025E1B3AC65F00F64F7B /* <EMAIL> */,
				0A6F025F1B3AC65F00F64F7B /* <EMAIL> */,
				0A6F02601B3AC65F00F64F7B /* <EMAIL> */,
				0A6F02611B3AC65F00F64F7B /* <EMAIL> */,
				0A6F02621B3AC65F00F64F7B /* <EMAIL> */,
				0A6F02631B3AC65F00F64F7B /* <EMAIL> */,
				0A6F02641B3AC65F00F64F7B /* <EMAIL> */,
				0A6F02651B3AC65F00F64F7B /* <EMAIL> */,
				0A6F02661B3AC65F00F64F7B /* <EMAIL> */,
			);
			path = Resource;
			sourceTree = "<group>";
		};
		0E35CC382136E48B000CAA36 /* QRCodeScan */ = {
			isa = PBXGroup;
			children = (
				0E35CC392136E4DB000CAA36 /* PPQRCodeScanViewController.h */,
				0E35CC3A2136E4DB000CAA36 /* PPQRCodeScanViewController.m */,
				0E35CC3E2136E84F000CAA36 /* PPQRScanChildViewController.h */,
				0E35CC3F2136E84F000CAA36 /* PPQRScanChildViewController.m */,
				0E35CC412136F4A1000CAA36 /* PPBarCoverView.h */,
				0E35CC422136F4A1000CAA36 /* PPBarCoverView.m */,
			);
			path = QRCodeScan;
			sourceTree = "<group>";
		};
		41452BC6226DDD070018567A /* HostSwitch */ = {
			isa = PBXGroup;
			children = (
				41452BC7226DDD070018567A /* PPHostSwitcher.h */,
				41452BC8226DDD070018567A /* PPHostSwitcher.m */,
			);
			path = HostSwitch;
			sourceTree = "<group>";
		};
		41A09A7121182CAC0020B8B4 /* RiskController */ = {
			isa = PBXGroup;
			children = (
				41A09A7221182CAC0020B8B4 /* PPRiskErrorProcessController.m */,
				41A09A7321182CAC0020B8B4 /* PPRiskErrorProcessController.h */,
			);
			path = RiskController;
			sourceTree = "<group>";
		};
		41C7670320B6D66D006A911A /* OpenPayDebug */ = {
			isa = PBXGroup;
			children = (
				41C7670420B71CEE006A911A /* MeituanPaySDK.h */,
				41C7670620B71CEF006A911A /* MeituanPaySDK.m */,
				41C7670520B71CEE006A911A /* MeituanPaySDKDefine.h */,
				41C7670020B6D3A2006A911A /* PPOpenPayDebugViewController.h */,
				41C7670120B6D3A2006A911A /* PPOpenPayDebugViewController.m */,
			);
			path = OpenPayDebug;
			sourceTree = "<group>";
		};
		561A89661F380B8E000F4336 /* Debug */ = {
			isa = PBXGroup;
			children = (
				561A89671F380B8E000F4336 /* SAKDomainObject+Debug.h */,
				561A89681F380B8E000F4336 /* SAKDomainObject+Debug.m */,
			);
			path = Debug;
			sourceTree = "<group>";
		};
		56F7DEC42046AAB400E4C1BC /* SwiftFoundation */ = {
			isa = PBXGroup;
			children = (
				0790B07D205FADFB004C8833 /* ReactiveCocoaBindings.swift */,
				56F7DEC52046AAF600E4C1BC /* UIColor+Addtion.swift */,
				56F7DEC62046AAF600E4C1BC /* UIFont+Addition.swift */,
				07686C072059107C00046188 /* NSObject+Addtion.swift */,
				07686C0520590A8A00046188 /* PPGlobalConstant.swift */,
				07DACD00205B97EB00FB01F0 /* PPUtil.swift */,
			);
			path = SwiftFoundation;
			sourceTree = "<group>";
		};
		5AB978671E52AB210089C6C2 /* Titants */ = {
			isa = PBXGroup;
			children = (
				5AB978681E52ABC80089C6C2 /* PPTitansAdapter.h */,
				5AB978691E52ABC80089C6C2 /* PPTitansAdapter.m */,
				5AB9786B1E52AC250089C6C2 /* PPTitansNamespace.h */,
				5AB9786C1E52AC250089C6C2 /* PPTitansNamespace.m */,
			);
			name = Titants;
			sourceTree = "<group>";
		};
		67754C6F248140D50000590B /* PaymentKit */ = {
			isa = PBXGroup;
			children = (
				67754CB02481465A0000590B /* SPKTitleInputTableViewCell.h */,
				67754CAF248146590000590B /* SPKTitleInputTableViewCell.m */,
				67754CAD248146310000590B /* SPKTitleImageTableViewCell.h */,
				67754CAC248146300000590B /* SPKTitleImageTableViewCell.m */,
				67754CAA2481460A0000590B /* SPKTitleTipTableViewCell.h */,
				67754CA92481460A0000590B /* SPKTitleTipTableViewCell.m */,
				67754CA7248145CB0000590B /* SPKTitleInputCell.h */,
				67754CA6248145CA0000590B /* SPKTitleInputCell.m */,
				67754CA4248145B20000590B /* SPKTitleImageCell.h */,
				67754CA3248145B20000590B /* SPKTitleImageCell.m */,
				67754CA1248145990000590B /* SPKTitleTipCell.h */,
				67754CA0248145990000590B /* SPKTitleTipCell.m */,
				67754C9E248145640000590B /* SPKCommonCell.h */,
				67754C9D248145640000590B /* SPKCommonCell.m */,
				67754C9B248144680000590B /* SPKTitleSwitchCell.h */,
				67754C9A248144680000590B /* SPKTitleSwitchCell.m */,
				67754C97248144310000590B /* SPKScrollPageView.h */,
				67754C98248144310000590B /* SPKScrollPageView.m */,
				67754C942481434E0000590B /* SPKRefreshFooter.h */,
				67754C952481434F0000590B /* SPKRefreshFooter.m */,
				67754C92248143350000590B /* SPKRefreshConst.h */,
				67754C91248143350000590B /* SPKRefreshConst.m */,
				67754C8E2481431C0000590B /* UIScrollView+SPKRefresh.h */,
				67754C8F2481431D0000590B /* UIScrollView+SPKRefresh.m */,
				67754C8C248142FC0000590B /* SPKRefreshComponent.h */,
				67754C8B248142FC0000590B /* SPKRefreshComponent.m */,
				67754C89248142DB0000590B /* SPKRefreshHeader.h */,
				67754C88248142DB0000590B /* SPKRefreshHeader.m */,
				67754C82248142BA0000590B /* SPKRefreshNormalFooter.h */,
				67754C85248142BA0000590B /* SPKRefreshNormalFooter.m */,
				67754C83248142BA0000590B /* SPKRefreshNormalHeader.h */,
				67754C84248142BA0000590B /* SPKRefreshNormalHeader.m */,
				67754C7F248142870000590B /* SPKNetworkErrorView.h */,
				67754C80248142870000590B /* SPKNetworkErrorView.m */,
				67754C7D248142670000590B /* SPKScrollViewLoadMoreControl.h */,
				67754C7C248142670000590B /* SPKScrollViewLoadMoreControl.m */,
				67754C79248141C30000590B /* SPKScrollViewDragControl.h */,
				67754C7A248141C30000590B /* SPKScrollViewDragControl.m */,
				67754C762481418F0000590B /* SPKScrollViewRefreshControl.h */,
				67754C772481418F0000590B /* SPKScrollViewRefreshControl.m */,
				67754C74248141420000590B /* SPKCommonTableView.h */,
				67754C73248141420000590B /* SPKCommonTableView.m */,
				67754C70248140EA0000590B /* SPKTitleSwitchTableViewCell.h */,
				67754C71248140EA0000590B /* SPKTitleSwitchTableViewCell.m */,
			);
			path = PaymentKit;
			sourceTree = "<group>";
		};
		6A6F1EF921B0155400F48300 /* UIKit2 */ = {
			isa = PBXGroup;
			children = (
				6A6F1EFA21B015EC00F48300 /* PPUIKit2ViewController.h */,
				6A6F1EFB21B015EC00F48300 /* PPUIKit2ViewController.m */,
				6A6F1EFD21B01BB400F48300 /* PPUIKit2TabSwitchViewController.h */,
				6A6F1EFE21B01BB400F48300 /* PPUIKit2TabSwitchViewController.m */,
				6A6F1F0021B01BD000F48300 /* PPUIKit2ButtonViewController.h */,
				6A6F1F0121B01BD000F48300 /* PPUIKit2ButtonViewController.m */,
				6A6F1F0321B01C0900F48300 /* PPUIKit2AgreementViewController.h */,
				6A6F1F0421B01C0900F48300 /* PPUIKit2AgreementViewController.m */,
				6A6F1F0621B01C2F00F48300 /* PPUIKit2PickerViewController.h */,
				6A6F1F0721B01C2F00F48300 /* PPUIKit2PickerViewController.m */,
				6A6F1F0921B01C4B00F48300 /* PPUIKit2NoticeViewController.h */,
				6A6F1F0A21B01C4B00F48300 /* PPUIKit2NoticeViewController.m */,
				6A6F1F0C21B01C6600F48300 /* PPUIKit2BannerViewController.h */,
				6A6F1F0D21B01C6600F48300 /* PPUIKit2BannerViewController.m */,
				6A6F1F0F21B01C7900F48300 /* PPUIKit2CardViewController.h */,
				6A6F1F1021B01C7900F48300 /* PPUIKit2CardViewController.m */,
				6A6F1F1221B01C9D00F48300 /* PPUIKit2ListItemViewController.h */,
				6A6F1F1321B01C9D00F48300 /* PPUIKit2ListItemViewController.m */,
				6A6F1F1521B01CDA00F48300 /* PPUIKit2ActivityViewController.h */,
				6A6F1F1621B01CDA00F48300 /* PPUIKit2ActivityViewController.m */,
				6A6F1F1821B01CF700F48300 /* PPUIKit2ToastViewController.h */,
				6A6F1F1921B01CF700F48300 /* PPUIKit2ToastViewController.m */,
				6A6F1F1B21B01D1100F48300 /* PPUIKit2AlertViewController.h */,
				6A6F1F1C21B01D1100F48300 /* PPUIKit2AlertViewController.m */,
				6A6F1F1E21B01D3A00F48300 /* PPUIKitActionSheetViewController.h */,
				6A6F1F1F21B01D3A00F48300 /* PPUIKitActionSheetViewController.m */,
			);
			path = UIKit2;
			sourceTree = "<group>";
		};
		6E1A9C6D1DE7113200BB472B /* H5_ICON */ = {
			isa = PBXGroup;
			children = (
				6E1A9C6E1DE7115700BB472B /* <EMAIL> */,
				6E1A9C6F1DE7115700BB472B /* <EMAIL> */,
				6E1A9C701DE7115700BB472B /* <EMAIL> */,
				6E1A9C711DE7115700BB472B /* <EMAIL> */,
				6E1A9C721DE7115700BB472B /* <EMAIL> */,
				6E1A9C731DE7115700BB472B /* <EMAIL> */,
				6E1A9C741DE7115700BB472B /* <EMAIL> */,
				6E1A9C751DE7115700BB472B /* <EMAIL> */,
				6E1A9C761DE7115700BB472B /* <EMAIL> */,
				6E1A9C771DE7115700BB472B /* <EMAIL> */,
				6E1A9C781DE7115700BB472B /* <EMAIL> */,
				6E1A9C791DE7115700BB472B /* <EMAIL> */,
				6E1A9C7A1DE7115700BB472B /* <EMAIL> */,
				6E1A9C7B1DE7115700BB472B /* <EMAIL> */,
				6E1A9C7C1DE7115700BB472B /* <EMAIL> */,
			);
			name = H5_ICON;
			sourceTree = "<group>";
		};
		6EA4DC6A1DCAF3C900C316D3 /* DebugWindow */ = {
			isa = PBXGroup;
			children = (
				6EA4DC6B1DCAF3E300C316D3 /* PPDebugWindow.h */,
				6EA4DC6C1DCAF3E300C316D3 /* PPDebugWindow.m */,
			);
			name = DebugWindow;
			sourceTree = "<group>";
		};
		B4A8E7821BA6AD9D008C89D0 /* Module */ = {
			isa = PBXGroup;
			children = (
				67754C6F248140D50000590B /* PaymentKit */,
				41452BC6226DDD070018567A /* HostSwitch */,
				0E35CC382136E48B000CAA36 /* QRCodeScan */,
				41A09A7121182CAC0020B8B4 /* RiskController */,
				41C7670320B6D66D006A911A /* OpenPayDebug */,
				BCB55199200DDC6300711A42 /* PicassoDebug */,
				D33FB0A8200CF32B00E60F1D /* Crash */,
				561A89661F380B8E000F4336 /* Debug */,
				5AB978671E52AB210089C6C2 /* Titants */,
				6EA4DC6A1DCAF3C900C316D3 /* DebugWindow */,
				B4A8E7831BA6ADB4008C89D0 /* AppSelect */,
			);
			path = Module;
			sourceTree = "<group>";
		};
		B4A8E7831BA6ADB4008C89D0 /* AppSelect */ = {
			isa = PBXGroup;
			children = (
				B4A8E7841BA6ADCF008C89D0 /* PPAppSelectViewController.h */,
				B4A8E7851BA6ADCF008C89D0 /* PPAppSelectViewController.m */,
				B4A8E7871BA6B2B1008C89D0 /* PPAppSelectTableViewCell.h */,
				B4A8E7881BA6B2B1008C89D0 /* PPAppSelectTableViewCell.m */,
			);
			path = AppSelect;
			sourceTree = "<group>";
		};
		BCB55199200DDC6300711A42 /* PicassoDebug */ = {
			isa = PBXGroup;
			children = (
				0784C21821A6B1F100624997 /* JSPathData.plist */,
				BCB5519A200DDCA000711A42 /* PPPicassoDebugViewController.h */,
				BCB5519B200DDCA000711A42 /* PPPicassoDebugViewController.m */,
				0784C21521A69B5B00624997 /* PPPicassoVCDebugViewController.h */,
				0784C21621A69B5B00624997 /* PPPicassoVCDebugViewController.m */,
			);
			path = PicassoDebug;
			sourceTree = "<group>";
		};
		BCDEA8C1200E4FBE0084517C /* MemoryLeak */ = {
			isa = PBXGroup;
			children = (
				BCDEA8C2200E4FDD0084517C /* UITextField+MemoryLeak.h */,
				BCDEA8C3200E4FDD0084517C /* UITextField+MemoryLeak.m */,
				56F7DEC92046C3F600E4C1BC /* NSObject+MLeaksFinderSwitch.h */,
				56F7DECA2046C3F600E4C1BC /* NSObject+MLeaksFinderSwitch.m */,
			);
			path = MemoryLeak;
			sourceTree = "<group>";
		};
		C68A6398F89FF3E7C77DA8CC /* Pods */ = {
			isa = PBXGroup;
			children = (
				23282186CDA6A31079E3CAF0 /* Pods-ipaymentapp.debug.xcconfig */,
				DC452069872A18B906441813 /* Pods-ipaymentapp.release.xcconfig */,
				0D10C2B115D21214C28EA2FC /* Pods-ipaymentapp.releaseinhouse.xcconfig */,
				42B4E0CBE48E46E9D7FE139F /* Pods-ipaymentappTests.debug.xcconfig */,
				624FECA4E4E8D6B4A61E3D44 /* Pods-ipaymentappTests.release.xcconfig */,
				9D7AC605843107E2C9604338 /* Pods-ipaymentappTests.releaseinhouse.xcconfig */,
				7A75A24BF69A1E060F1D0E33 /* Pods-ipaymentapp.dailybuild.xcconfig */,
				354E323A88BC676126FFF525 /* Pods-ipaymentappTests.dailybuild.xcconfig */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
		D31B5EDC20301F1800FC3482 /* UIKit */ = {
			isa = PBXGroup;
			children = (
				D31B5EE2203021F900FC3482 /* PPAgreementViewController.h */,
				D31B5EE3203021F900FC3482 /* PPAgreementViewController.m */,
				D31B5EE1203021F900FC3482 /* PPAlertViewController.h */,
				D31B5EE4203021F900FC3482 /* PPAlertViewController.m */,
				D31B5EEF20303EFF00FC3482 /* PPBannerViewController.h */,
				D31B5EF020303EFF00FC3482 /* PPBannerViewController.m */,
				D31B5EF52030774C00FC3482 /* PPLoadingViewController.h */,
				D31B5EF62030774C00FC3482 /* PPLoadingViewController.m */,
				D31B5EF820307E7000FC3482 /* PPNetworkErrorViewController.h */,
				D31B5EF920307E7000FC3482 /* PPNetworkErrorViewController.m */,
				D31B5EE5203021F900FC3482 /* PPNormalizedCellViewController.h */,
				D31B5EE0203021F900FC3482 /* PPNormalizedCellViewController.m */,
				D31B5EE6203021F900FC3482 /* PPScrollPageViewController.h */,
				D31B5EDD203021F900FC3482 /* PPScrollPageViewController.m */,
				D31B5EE8203021FA00FC3482 /* PPSPKRefreshTestViewController.h */,
				D31B5EDF203021F900FC3482 /* PPSPKRefreshTestViewController.m */,
				D31B5EF220306DB500FC3482 /* PPToastViewController.h */,
				D31B5EF320306DB500FC3482 /* PPToastViewController.m */,
				D31B5EDE203021F900FC3482 /* PPUIKitViewController.h */,
				D31B5EE7203021F900FC3482 /* PPUIKitViewController.m */,
			);
			path = UIKit;
			sourceTree = "<group>";
		};
		D31B5EFB203E977200FC3482 /* PayBase */ = {
			isa = PBXGroup;
			children = (
				672D372B1E600308001DFB54 /* PPDataStorageViewController.h */,
				672D372C1E600308001DFB54 /* PPDataStorageViewController.m */,
				D31B5F0F203EBE6500FC3482 /* PPFinacialUtilityViewController.h */,
				D31B5F10203EBE6500FC3482 /* PPFinacialUtilityViewController.m */,
				D31B5F12203EBEA800FC3482 /* PPFingerPrintPayViewController.h */,
				D31B5F13203EBEA800FC3482 /* PPFingerPrintPayViewController.m */,
				6E82B2301F96EDE800CBECA9 /* PPNFCManagementViewController.h */,
				6E82B2311F96EDE800CBECA9 /* PPNFCManagementViewController.m */,
				D3DEA9381FC3D65B0005E26B /* PPPaymentKitViewController.h */,
				D3DEA9391FC3D65B0005E26B /* PPPaymentKitViewController.m */,
				D31B5EFC203E97DD00FC3482 /* PPScanCardViewController.h */,
				D31B5EFD203E97DD00FC3482 /* PPScanCardViewController.m */,
				D30606281FD584AA00D5D849 /* PPTestViewController.h */,
				D30606291FD584AA00D5D849 /* PPTestViewController.m */,
				D30606251FCEAAEA00D5D849 /* PPVerifyPasswordViewController.h */,
				D30606261FCEAAEA00D5D849 /* PPVerifyPasswordViewController.m */,
			);
			path = PayBase;
			sourceTree = "<group>";
		};
		D33FB0A8200CF32B00E60F1D /* Crash */ = {
			isa = PBXGroup;
			children = (
				D33FB0A9200CF32B00E60F1D /* PPCrashReporter.h */,
				D33FB0AA200CF32B00E60F1D /* PPCrashReporter.m */,
			);
			path = Crash;
			sourceTree = "<group>";
		};
		E746830622A12FBF00C49A17 /* mesh */ = {
			isa = PBXGroup;
			children = (
				E746830C22A12FE400C49A17 /* demo */,
			);
			path = mesh;
			sourceTree = "<group>";
		};
		E746830C22A12FE400C49A17 /* demo */ = {
			isa = PBXGroup;
			children = (
				E746830D22A12FE400C49A17 /* MESHDebugViewController.h */,
				E746830E22A12FE400C49A17 /* MESHDebugViewController.m */,
				E746830F22A12FE400C49A17 /* MESHH5DebugViewController.h */,
				E746831022A12FE400C49A17 /* MESHH5DebugViewController.m */,
				E746831122A12FE400C49A17 /* MESHTest1ViewController.h */,
				E746831222A12FE400C49A17 /* MESHTest1ViewController.m */,
				E746831322A12FE400C49A17 /* SAKPayMeshService.h */,
				E746831422A12FE400C49A17 /* SAKPayMeshService.m */,
			);
			path = demo;
			sourceTree = "<group>";
		};
		************************ /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				3498D9E62D75A3E3001490BC /* CoreML.framework */,
				FF9B00A52823CEBB00EF96A9 /* libSAKAccount-Core.a */,
				AD7CEEF2264CC8B10066A10F /* libbz2.1.0.tbd */,
				0E35CC3C2136E4FE000CAA36 /* AVFoundation.framework */,
				D33FB0A6200CF30600E60F1D /* MessageUI.framework */,
				6EB5D7981DE31FA7007D7807 /* libSAKAccount.a */,
				0AD377851B3BA08E00F67750 /* libPods-ipaymentapp.a */,
				0AD377831B3BA06A00F67750 /* libPods-ipaymentapp-AutoCoding.a */,
				************************ /* libPods-ipaymentapp.a */,
				F0152918C8AC14CF369ED16E /* libPods-ipaymentappTests.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		0A6F02061B3AC46800F64F7B /* ipaymentapp */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0A6F022D1B3AC46900F64F7B /* Build configuration list for PBXNativeTarget "ipaymentapp" */;
			buildPhases = (
				4EBCAD72D597F4B7E8A1D6BB /* [CP] Check Pods Manifest.lock */,
				0A6F02031B3AC46800F64F7B /* Sources */,
				0A6F02041B3AC46800F64F7B /* Frameworks */,
				0A6F02051B3AC46800F64F7B /* Resources */,
				05E874F791015AE33EB642A3 /* [MRN] Preset & Trigger Local MRN Projects Build */,
				38B11CA39A85004576992FE9 /* [CP] Copy Pods Resources */,
				BAE94D6651A7DE7F8C15B9E5 /* [MRN] Preset MRN bundles */,
				A1AD6B9E2507BF77004B6862 /* uploadCoverage */,
				E24A75A3DB53F47E4AFB8FC8 /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = ipaymentapp;
			productName = ipaymentapp;
			productReference = 0A6F02071B3AC46800F64F7B /* ipaymentapp.app */;
			productType = "com.apple.product-type.application";
		};
		0A6F02221B3AC46900F64F7B /* ipaymentappTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0A6F02301B3AC46900F64F7B /* Build configuration list for PBXNativeTarget "ipaymentappTests" */;
			buildPhases = (
				997B48DCD42621715E20ECD2 /* [CP] Check Pods Manifest.lock */,
				0A6F021F1B3AC46900F64F7B /* Sources */,
				0A6F02201B3AC46900F64F7B /* Frameworks */,
				0A6F02211B3AC46900F64F7B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				0A6F02251B3AC46900F64F7B /* PBXTargetDependency */,
			);
			name = ipaymentappTests;
			productName = ipaymentappTests;
			productReference = 0A6F02231B3AC46900F64F7B /* ipaymentappTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		0A6F01FF1B3AC46800F64F7B /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0900;
				ORGANIZATIONNAME = Meituan.com;
				TargetAttributes = {
					0A6F02061B3AC46800F64F7B = {
						CreatedOnToolsVersion = 6.3.1;
						LastSwiftMigration = 0920;
						SystemCapabilities = {
							com.apple.ApplePay = {
								enabled = 0;
							};
							com.apple.ApplicationGroups.iOS = {
								enabled = 0;
							};
							com.apple.BackgroundModes = {
								enabled = 1;
							};
							com.apple.InAppPurchase = {
								enabled = 0;
							};
							com.apple.Keychain = {
								enabled = 0;
							};
							com.apple.NearFieldCommunicationTagReading = {
								enabled = 0;
							};
							com.apple.Push = {
								enabled = 0;
							};
						};
					};
					0A6F02221B3AC46900F64F7B = {
						CreatedOnToolsVersion = 6.3.1;
						TestTargetID = 0A6F02061B3AC46800F64F7B;
					};
				};
			};
			buildConfigurationList = 0A6F02021B3AC46800F64F7B /* Build configuration list for PBXProject "ipaymentapp" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				English,
				en,
				Base,
			);
			mainGroup = 0A6F01FE1B3AC46800F64F7B;
			productRefGroup = 0A6F02081B3AC46800F64F7B /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				0A6F02061B3AC46800F64F7B /* ipaymentapp */,
				0A6F02221B3AC46900F64F7B /* ipaymentappTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		0A6F02051B3AC46800F64F7B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				421F14E921BA77A30045599C /* <EMAIL> in Resources */,
				0A6F027D1B3AC65F00F64F7B /* <EMAIL> in Resources */,
				2348A478211871CE00AA1AA7 /* <EMAIL> in Resources */,
				2348A479211871CE00AA1AA7 /* <EMAIL> in Resources */,
				0784C21921A6B1F100624997 /* JSPathData.plist in Resources */,
				B43BDFF31D4F71BC00F797F8 /* Default-568.png in Resources */,
				B4A8E7801BA6ABFD008C89D0 /* <EMAIL> in Resources */,
				6AE9D3FC21B8CDEF00113E50 /* bankcheck.png in Resources */,
				6AE9D3F421B7D8A100113E50 /* MTFUTableViewCellAccessoryImage.png in Resources */,
				0E89D5502180BE5E0066135A /* mt_security.png in Resources */,
				2348A47A211871CE00AA1AA7 /* <EMAIL> in Resources */,
				0A6F021E1B3AC46900F64F7B /* LaunchScreen.xib in Resources */,
				0A6F02821B3AC65F00F64F7B /* <EMAIL> in Resources */,
				0A6F027F1B3AC65F00F64F7B /* <EMAIL> in Resources */,
				412C5C98229D94ED0062F553 /* <EMAIL> in Resources */,
				0A1DAB951B4CE05A00C34A65 /* PortalSettings.plist in Resources */,
				0A6F02851B3AC65F00F64F7B /* <EMAIL> in Resources */,
				B4A8E7811BA6ABFD008C89D0 /* <EMAIL> in Resources */,
				0A6F027C1B3AC65F00F64F7B /* <EMAIL> in Resources */,
				0A6F02861B3AC65F00F64F7B /* DefaultAppInfo.plist in Resources */,
				6AE9D3FA21B8C04B00113E50 /* bankicon.png in Resources */,
				2348A47E211871CE00AA1AA7 /* <EMAIL> in Resources */,
				7AB625FF1D52F96700804FCE /* WalletEntranceInfo.plist in Resources */,
				2348A47C211871CE00AA1AA7 /* <EMAIL> in Resources */,
				2348A47D211871CE00AA1AA7 /* <EMAIL> in Resources */,
				0A6F02841B3AC65F00F64F7B /* <EMAIL> in Resources */,
				0A6F02811B3AC65F00F64F7B /* <EMAIL> in Resources */,
				2348A47B211871CE00AA1AA7 /* <EMAIL> in Resources */,
				412C5C99229D94ED0062F553 /* <EMAIL> in Resources */,
				421F14E821BA77A30045599C /* <EMAIL> in Resources */,
				0A6F02831B3AC65F00F64F7B /* <EMAIL> in Resources */,
				0A6F02801B3AC65F00F64F7B /* <EMAIL> in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0A6F02211B3AC46900F64F7B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				6EC7EF2D1CD0C358006921EE /* monkey.sh in Resources */,
				6E07D2F61D3614FB007F0EE1 /* alertViewButtonTitles.plist in Resources */,
				6EC7EF2F1CD0C443006921EE /* touch.png in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		05E874F791015AE33EB642A3 /* [MRN] Preset & Trigger Local MRN Projects Build */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "[MRN] Preset & Trigger Local MRN Projects Build";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/bash;
			shellScript = "#!/bin/bash\nexport PATH=\"$PATH:/usr/local/bin/\"\nexport NVM_DIR=\"$HOME/.nvm\"\n[ -s \"$NVM_DIR/nvm.sh\" ] && \\. \"$NVM_DIR/nvm.sh\"  # This loads nvm\n\nproject_dir=$SRCROOT\ntask_list_file=$project_dir/\".mrn_pack_task_list\"\npreset_package_path=$SRCROOT/\"MRNBundle\"\npkgs_dir=$CODESIGNING_FOLDER_PATH\"/MRNBundle\"\n\n# find MRNBundle path\nif [ ! -d $preset_package_path ];then\n    preset_package_path=$(dirname \"$SRCROOT\")/\"MRNBundle\"\nfi\n\n# create package folder if not exists\nif [ ! -d $pkgs_dir ];then\n    mkdir -p $pkgs_dir\nfi\n\n# copy preset packages\nif [ -d $preset_package_path ];then\n    find $preset_package_path -name \\*.zip -exec cp {} $pkgs_dir/ \\;\nfi\n\n# triger local project build\nif [ ! -f $task_list_file ];then\n    exit 0\nfi\n\n# pack dir one by one\nwhile IFS='' read -r line || [[ -n \"$line\" ]]; do\n\n    line=`echo $line | tr -d \" \\t\\n\\r\" | sed \"s?~?$HOME?\"`\n    if [ ! -d $line ];then\n        continue\n    fi\n\n    if [[ $a == \"#\"* ]];then\n        continue\n    fi\n\n    if [ -z $line ];then\n        continue\n    fi\n\n    cd $line\n    npm run build\n    cp dist/*.zip $pkgs_dir\n    cd -\ndone < $task_list_file";
			showEnvVarsInLog = 0;
		};
		38B11CA39A85004576992FE9 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-ipaymentapp/PodInputFileList.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-ipaymentapp/PodOutputFileList.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${SRCROOT}/../Pods/Target Support Files/Pods-ipaymentapp/Pods-ipaymentapp-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		4EBCAD72D597F4B7E8A1D6BB /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-ipaymentapp-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		997B48DCD42621715E20ECD2 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-ipaymentappTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		A1AD6B9E2507BF77004B6862 /* uploadCoverage */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = uploadCoverage;
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export LANG=en_US.UTF-8\nexport LANGUAGE=en_US.UTF-8\nexport LC_ALL=en_US.UTF-8\n\nif [ \"${CONFIGURATION}\" != \"ReleaseInhouse\" ]; then\necho \"不需要统计测试覆盖率\"\nexit\nfi;\n\nif [ -e ${PODS_ROOT}/../ipaymentapp-test.zip ]\nthen\nrm -f ${PODS_ROOT}/../ipaymentapp-test.zip\nfi;\n\nif [ -d ${PODS_ROOT}/../ipaymentapp-test ]\nthen\nrm -r ${PODS_ROOT}/../ipaymentapp-test\nfi;\n\necho \"准备创建目录\"\nmkdir -p ${PODS_ROOT}/../ipaymentapp-test\nmkdir -p ${PODS_ROOT}/../ipaymentapp-test/Pods\nmkdir -p ${PODS_ROOT}/../ipaymentapp-test/gcno\necho \"创建目录成功\"\n\ncd ${PODS_ROOT}/Headers\ncp -rf ./ ${PODS_ROOT}/../ipaymentapp-test/Pods/Headers\necho \"拷贝Headers结束\"\n\ncd ${PODS_ROOT}/../\n# folder path\nFOLDER_PATH=`pwd`\n\n#支付相关pod，除了headers之外，共计15个，SAKFinBusiness需要放到最后，因为它的分支信息带subspecs需要单独处理\n#!!!!!!!新增pod的话，记得在http://orange.fsp.test.sankuai.com/#/mobileCenter/podManage 先配置！！！！\nPodsArray=(SAKPaymentKit SAKPaymentChannel SAKWallet SAKCashier SAKBarcodeCashier SAKMeituanPay SAKPaymentGuard  SAKFinanceThirdPayAdpater  SAKIdentityCardRecognizer SAKFinVerificationCenter SAKBankCardRecognizer SAKPaymentAdapter SAKMesh SAKHybridCashier SAKOneClickPayCashier SAKNeoHybrid SAKHybridMeituanPay)\nPodsCount=${#PodsArray[@]}\necho \"PodsCount : $PodsCount\"\n\n#commitId\ncommitId=\"\"\npodCommitId=\"\"\ntemp=\"\"\n\n\nfor((j=0;j<$PodsCount;j++))\ndo\n   if [ -d \"${OBJROOT}/Pods.build/ReleaseInhouse-iphoneos/${PodsArray[j]}.build/Objects-normal\" ];then\n         echo \"${PodsArray[j]} 是使用源码集成\"\n         cp -rf ${OBJROOT}/Pods.build/ReleaseInhouse-iphoneos/${PodsArray[j]}.build/Objects-normal ipaymentapp-test/gcno/${PodsArray[j]}\n         TEST_PATH=`pwd`\n         echo \"当前路径是：${TEST_PATH}\"\n         echo `ls`\n         cp -rf ./Pods/${PodsArray[j]} ${PODS_ROOT}/../ipaymentapp-test/Pods\n         if [ -z \"$(grep -v \"#\" ${PODS_ROOT}/../.Podfile.patch | grep ${PodsArray[j]})\" ];then\n             #通过podfile获取\n             temp=`grep -v \"#\" ${PODS_ROOT}/../Podfile | grep ${PodsArray[j]}`\n             if [[ $temp == *git* ]]; then\n                podCommitId=`echo ${temp} | cut -d \">\" -f 3 | sed $'s/\\'//g' | sed $'s/,//g' | cut -d ' ' -f 2`\n                echo \"${PodsArray[j]} 通过Podfile获取到的tag号是 ${podCommitId}\"\n             else\n                echo \"${PodsArray[j]} 使用tag号打包\"\n                podCommitId=`grep -v \"#\" ${PODS_ROOT}/../Podfile | grep \"${PodsArray[j]}\" | cut -d \"'\" -f 4 | cut -d \",\" -f 1`\n                echo \"${PodsArray[j]} 通过Podfile获取到的tag号是 ${podCommitId}\"\n                podSourceCodePath=`grep -v \"#\" ${PODS_ROOT}/../Podfile | grep \"${PodsArray[j]}\" | cut -d \"'\" -f 4 | cut -d \",\" -f 1`\n                echo \"podSourceCodePath ${podSourceCodePath}\"\n                cd ${podSourceCodePath}\n                # echo \"拷贝${PodsArray[j]}源码\"\n                # cp -rf ./ ${PODS_ROOT}/../ipaymentapp-test/Pods/${PodsArray[j]}\n                # cd ${PODS_ROOT}/../\n             fi\n         elif [[ $(grep -v \"#\" ${PODS_ROOT}/../.Podfile.patch | grep ${PodsArray[j]}) == *git* ]];then\n              #带分支信息的\n              temp=`grep -v \"#\" ${PODS_ROOT}/../.Podfile.patch | grep ${PodsArray[j]}`\n              podCommitId=`echo ${temp} | cut -d \">\" -f 3 | sed $'s/\\'//g' | sed $'s/,//g' | cut -d ' ' -f 2`\n\n         elif [ -n \"$(grep -v \"#\" ${PODS_ROOT}/../.Podfile.patch | grep ${PodsArray[j]})\" ];then\n              #tag号\n              echo \"${PodsArray[j]} 使用tag号打包\"\n              podCommitId=`grep -v \"#\" ${PODS_ROOT}/../.Podfile.patch | grep \"${PodsArray[j]}\" | cut -d \"'\" -f 4 | cut -d \",\" -f 1`\n              echo \"${PodsArray[j]} 通过Podfile.patch获取到的tag号是 ${podCommitId}\"\n         fi\n\n         if [ -z \"$commitId\" ];then\n              commitId=\"${PodsArray[j]}:${podCommitId}\"\n              echo \"当前commitId是 ${commitId}\"\n         else\n              commitId=\"${commitId};${PodsArray[j]}:${podCommitId}\"\n              echo \"加上${PodsArray[j]}当前commitId是${commitId}\"\n         fi\n   else\n       echo \"${PodsArray[j]} 使用了二进制组件\"\n   fi\ndone\n\necho \"确认下最终对不对 ${commitId}\"\n\n\n# bundle id\nbundle_id=\"com.meituan.ipaymentapp\"\n# folder path\nFOLDER_PATH=`pwd`\n\n# bundle version\nbundle_version=`/usr/libexec/PlistBuddy -c 'Print :CFBundleVersion' ${PODS_ROOT}/../ipaymentapp/ipaymentapp/Info.plist`\necho \"bundle_version ${bundle_version}\"\n# CFBundleShortVersionString\nversion=`/usr/libexec/PlistBuddy -c 'Print :CFBundleShortVersionString' ${PODS_ROOT}/../ipaymentapp/ipaymentapp/Info.plist`\necho \"version ${version}\"\n# zip\nzip -r -q ipaymentapp-test.zip ./ipaymentapp-test\n# sha1 hash\nsha1_hash=`shasum ipaymentapp-test.zip | cut -d ' ' -f1`\n# upload\nbaseDirForScriptSelf=$(cd \"$(dirname \"$0\")\"; pwd)\necho \"full path to currently executed script is : ${baseDirForScriptSelf}\"\n\nresult=$(curl -i -F \"userFile=@ipaymentapp-test.zip;type=application/octet-stream\" -F hashKey=$sha1_hash -F app=$bundle_id -F fileName=\"ipaymentapp-test.zip\" -F buildNumber=$bundle_version -F ciSourcePath=$FOLDER_PATH -F version=$version --form-string \"commitId=${commitId}\" -F folderName=\"ipaymentapp-test\" http://precisionguide.fsp.test.sankuai.com/ci/upload)\n\necho $result;\n#判断接口返回是否成功\nif(echo $result | grep -q \"succeed\");then\n  echo \"恭喜你上报成功了！这个包支持覆盖率统计！\";\nelse\n  echo \"覆盖率上传失败。\";\nexit 1\nfi;\n";
		};
		BAE94D6651A7DE7F8C15B9E5 /* [MRN] Preset MRN bundles */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "[MRN] Preset MRN bundles";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/bash;
			shellScript = "#!/bin/bash\npreset_package_path=$SRCROOT/\"MRNBundle\"\npkgs_dir=$CODESIGNING_FOLDER_PATH\"/MRNBundle\"\n\n# find MRNBundle path\nif [ ! -d $preset_package_path ];then\n    preset_package_path=$(dirname \"$SRCROOT\")/\"MRNBundle\"\nfi\n\n# create package folder if not exists\nif [ ! -d $pkgs_dir ];then\n    mkdir -p $pkgs_dir\nfi\n\n# copy preset packages\nif [ -d $preset_package_path ];then\n    find $preset_package_path -name \\*.zip -exec cp {} $pkgs_dir/ \\;\nfi";
			showEnvVarsInLog = 0;
		};
		E24A75A3DB53F47E4AFB8FC8 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${SRCROOT}/../Pods/Target Support Files/Pods-ipaymentapp/Pods-ipaymentapp-frameworks.sh",
				"${PODS_ROOT}/LookinServer/LookinServer.framework",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
			);
			outputPaths = (
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/LookinServer.framework",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${SRCROOT}/../Pods/Target Support Files/Pods-ipaymentapp/Pods-ipaymentapp-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		0A6F02031B3AC46800F64F7B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				D33FB0AB200CF32B00E60F1D /* PPCrashReporter.m in Sources */,
				D306062A1FD584AA00D5D849 /* PPTestViewController.m in Sources */,
				67754C72248140EA0000590B /* SPKTitleSwitchTableViewCell.m in Sources */,
				0784C21721A69B5B00624997 /* PPPicassoVCDebugViewController.m in Sources */,
				0E35CC432136F4A1000CAA36 /* PPBarCoverView.m in Sources */,
				6745287921D390BC0018BB05 /* PPDebugPanel.m in Sources */,
				56F7DECB2046C3F600E4C1BC /* NSObject+MLeaksFinderSwitch.m in Sources */,
				07686C082059107C00046188 /* NSObject+Addtion.swift in Sources */,
				4112DA3C26C6981000E6A58A /* PPHalfPageDebugViewController.m in Sources */,
				D30606271FCEAAEA00D5D849 /* PPVerifyPasswordViewController.m in Sources */,
				67754CAB2481460A0000590B /* SPKTitleTipTableViewCell.m in Sources */,
				67754C75248141420000590B /* SPKCommonTableView.m in Sources */,
				7AB2EAF21D45E14700F24AEA /* PPMineModel.m in Sources */,
				56C953BA1E600D980012B8AF /* PPRefundedOrderCellTableViewCell.m in Sources */,
				67754C86248142BA0000590B /* SPKRefreshNormalHeader.m in Sources */,
				6A7418A222CB890C00337AEB /* PPCertificateCameraViewController.m in Sources */,
				0A6F026C1B3AC65F00F64F7B /* PPOrder.m in Sources */,
				41452BC9226DDD070018567A /* PPHostSwitcher.m in Sources */,
				41C7670220B6D3A2006A911A /* PPOpenPayDebugViewController.m in Sources */,
				67754C9C248144680000590B /* SPKTitleSwitchCell.m in Sources */,
				0790B080205FAFE0004C8833 /* PPMineViewController.swift in Sources */,
				5A8ED17A1E6D3DBD00D2F558 /* PPRegisterAPPMock.m in Sources */,
				56F7DEC82046AAF600E4C1BC /* UIFont+Addition.swift in Sources */,
				E746831822A12FE400C49A17 /* MESHH5DebugViewController.m in Sources */,
				56C953B71E6009360012B8AF /* PPRefundedOrderListViewController.m in Sources */,
				5A8ED17D1E6D3EC000D2F558 /* SAKBaseModel+PPHook.m in Sources */,
				07E744CE2226644B00896C60 /* PPCacheManagerViewController.m in Sources */,
				41A09A7421182CAC0020B8B4 /* PPRiskErrorProcessController.m in Sources */,
				359B724524B71BD400B74517 /* UIColor+Addition.m in Sources */,
				6A7418A122CB890C00337AEB /* PPTestCertificateCameraViewController.m in Sources */,
				0A6F02751B3AC65F00F64F7B /* PPMyAccountView.m in Sources */,
				D31B5EFA20307E7000FC3482 /* PPNetworkErrorViewController.m in Sources */,
				6A6F1F1A21B01CF700F48300 /* PPUIKit2ToastViewController.m in Sources */,
				BCB55198200C9D6900711A42 /* MTBaseViewController+IPayment.m in Sources */,
				6A6F1F1421B01C9D00F48300 /* PPUIKit2ListItemViewController.m in Sources */,
				6A6F1EFF21B01BB400F48300 /* PPUIKit2TabSwitchViewController.m in Sources */,
				67754C8D248142FD0000590B /* SPKRefreshComponent.m in Sources */,
				2CF69FD2235839DF00093F0C /* MerchantWalletEntranceViewController.m in Sources */,
				D31B5EEA203021FA00FC3482 /* PPSPKRefreshTestViewController.m in Sources */,
				41C7670720B71CEF006A911A /* MeituanPaySDK.m in Sources */,
				0E35CC402136E84F000CAA36 /* PPQRScanChildViewController.m in Sources */,
				07686C0620590A8A00046188 /* PPGlobalConstant.swift in Sources */,
				0A6F02101B3AC46800F64F7B /* AppDelegate.m in Sources */,
				67754C782481418F0000590B /* SPKScrollViewRefreshControl.m in Sources */,
				D31B5EEE203021FA00FC3482 /* PPUIKitViewController.m in Sources */,
				6A6F1F0E21B01C6600F48300 /* PPUIKit2BannerViewController.m in Sources */,
				67754C902481431D0000590B /* UIScrollView+SPKRefresh.m in Sources */,
				564A63942048FD7500AA03FC /* PPHelloPayController.swift in Sources */,
				0A6F026A1B3AC65F00F64F7B /* PPModel.m in Sources */,
				356E0BAA255299AF001F94E0 /* PPRefundedMoneyInputModalView.m in Sources */,
				6A6F1F1121B01C7900F48300 /* PPUIKit2CardViewController.m in Sources */,
				D31B5EFE203E97DD00FC3482 /* PPScanCardViewController.m in Sources */,
				5AB9786A1E52ABC80089C6C2 /* PPTitansAdapter.m in Sources */,
				56F7DEC32046824200E4C1BC /* PPSubmitOrderViewController.swift in Sources */,
				07686C0A20592FB100046188 /* PPSelectMockViewController.swift in Sources */,
				6A6F1EFC21B015EC00F48300 /* PPUIKit2ViewController.m in Sources */,
				D31B5EF120303EFF00FC3482 /* PPBannerViewController.m in Sources */,
				6A6F1F1721B01CDA00F48300 /* PPUIKit2ActivityViewController.m in Sources */,
				0A6F02711B3AC65F00F64F7B /* PPContentTextFieldCell.m in Sources */,
				67754C9F248145640000590B /* SPKCommonCell.m in Sources */,
				B4A8E7861BA6ADCF008C89D0 /* PPAppSelectViewController.m in Sources */,
				67754C962481434F0000590B /* SPKRefreshFooter.m in Sources */,
				35148B4D2553DE3300EF3E38 /* PPOrderDetailViewCustomCell.m in Sources */,
				B4A8E7891BA6B2B1008C89D0 /* PPAppSelectTableViewCell.m in Sources */,
				42BAFDDC1FCC10A700106D04 /* PPFinancialKitViewController.m in Sources */,
				6E82B2321F96EDE800CBECA9 /* PPNFCManagementViewController.m in Sources */,
				67754CB12481465A0000590B /* SPKTitleInputTableViewCell.m in Sources */,
				67754C7E248142680000590B /* SPKScrollViewLoadMoreControl.m in Sources */,
				6A6F1F0221B01BD000F48300 /* PPUIKit2ButtonViewController.m in Sources */,
				5AB9786D1E52AC250089C6C2 /* PPTitansNamespace.m in Sources */,
				67754C8A248142DB0000590B /* SPKRefreshHeader.m in Sources */,
				E746831922A12FE400C49A17 /* MESHTest1ViewController.m in Sources */,
				E746831A22A12FE400C49A17 /* SAKPayMeshService.m in Sources */,
				67754CAE248146310000590B /* SPKTitleImageTableViewCell.m in Sources */,
				6A6F1F1D21B01D1100F48300 /* PPUIKit2AlertViewController.m in Sources */,
				BCDEA8C4200E4FDD0084517C /* UITextField+MemoryLeak.m in Sources */,
				0A5DEB9B1B54D943002FCF3F /* PPAccountController.m in Sources */,
				56C953C31E600F9C0012B8AF /* PPRefundedOrder.m in Sources */,
				67754C93248143350000590B /* SPKRefreshConst.m in Sources */,
				0A6F02681B3AC65F00F64F7B /* NSDate+Time.m in Sources */,
				67754C7B248141C30000590B /* SPKScrollViewDragControl.m in Sources */,
				561A89691F380B8E000F4336 /* SAKDomainObject+Debug.m in Sources */,
				07DACD01205B97EB00FB01F0 /* PPUtil.swift in Sources */,
				0A6F02691B3AC65F00F64F7B /* PPContentInfo.m in Sources */,
				D31B5F14203EBEA800FC3482 /* PPFingerPrintPayViewController.m in Sources */,
				7AB2EAF51D45ECCC00F24AEA /* PPMineViewModel.m in Sources */,
				35FCDEC824AF4B0A0090CE72 /* MTBaseViewController+DarkModeNavBarStyle.m in Sources */,
				D31B5EED203021FA00FC3482 /* PPAlertViewController.m in Sources */,
				D31B5EE9203021FA00FC3482 /* PPScrollPageViewController.m in Sources */,
				67754CA2248145990000590B /* SPKTitleTipCell.m in Sources */,
				0A6F02761B3AC65F00F64F7B /* PPOrderDetailViewController.m in Sources */,
				BCB5519C200DDCA000711A42 /* PPPicassoDebugViewController.m in Sources */,
				56F7DEC72046AAF600E4C1BC /* UIColor+Addtion.swift in Sources */,
				6A6F1F0821B01C2F00F48300 /* PPUIKit2PickerViewController.m in Sources */,
				D3DEA93A1FC3D65B0005E26B /* PPPaymentKitViewController.m in Sources */,
				6A6F1F0B21B01C4B00F48300 /* PPUIKit2NoticeViewController.m in Sources */,
				0790B07E205FADFB004C8833 /* ReactiveCocoaBindings.swift in Sources */,
				67754CA5248145B20000590B /* SPKTitleImageCell.m in Sources */,
				0A0CE28F1B58E76E00DE7304 /* PPOrderStatus.m in Sources */,
				67754C99248144320000590B /* SPKScrollPageView.m in Sources */,
				6A6F1F0521B01C0900F48300 /* PPUIKit2AgreementViewController.m in Sources */,
				D31B5F11203EBE6500FC3482 /* PPFinacialUtilityViewController.m in Sources */,
				67754C87248142BA0000590B /* SPKRefreshNormalFooter.m in Sources */,
				0E35CC3B2136E4DB000CAA36 /* PPQRCodeScanViewController.m in Sources */,
				D31B5EF72030774C00FC3482 /* PPLoadingViewController.m in Sources */,
				56C953C61E6024F00012B8AF /* PPRefundedOrderViewModel.m in Sources */,
				E746831722A12FE400C49A17 /* MESHDebugViewController.m in Sources */,
				239EEE4C21D0D8F500CF0556 /* PPBarcodeCashierHasParametersViewController.m in Sources */,
				564A63962049332700AA03FC /* PPPaymentSettingView.swift in Sources */,
				67754CA8248145CB0000590B /* SPKTitleInputCell.m in Sources */,
				0A6F02771B3AC65F00F64F7B /* PPOrderlistViewController.m in Sources */,
				0A6F02781B3AC65F00F64F7B /* PPOrderTableViewCell.m in Sources */,
				7AB2EAEB1D45C59000F24AEA /* PPWalletEntranceInfo.m in Sources */,
				0A6F026D1B3AC65F00F64F7B /* PPService.m in Sources */,
				D31B5EEB203021FA00FC3482 /* PPNormalizedCellViewController.m in Sources */,
				07686C042058F39900046188 /* PPConfigViewController.swift in Sources */,
				6A6F1F2021B01D3A00F48300 /* PPUIKitActionSheetViewController.m in Sources */,
				5A0F380F1E4B1465003D98CA /* SAKHostSwitcherURLProtocol+APPMock.m in Sources */,
				67754C81248142870000590B /* SPKNetworkErrorView.m in Sources */,
				672D372D1E600308001DFB54 /* PPDataStorageViewController.m in Sources */,
				0A6F026E1B3AC65F00F64F7B /* SAKBaseModel+CashierApp.m in Sources */,
				56C953C01E600F7E0012B8AF /* PPRefundedOrderUIObject.m in Sources */,
				0A6F020D1B3AC46800F64F7B /* main.m in Sources */,
				6EA4DC6D1DCAF3E300C316D3 /* PPDebugWindow.m in Sources */,
				0A6F026F1B3AC65F00F64F7B /* PPConfigTableViewCell.m in Sources */,
				D31B5EEC203021FA00FC3482 /* PPAgreementViewController.m in Sources */,
				D31B5EF420306DB500FC3482 /* PPToastViewController.m in Sources */,
				0AAAAA641B42C95B0099BC4A /* MTNavigationController+IPayment.m in Sources */,
				0A6F02741B3AC65F00F64F7B /* PPMyAccountCell.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0A6F021F1B3AC46900F64F7B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				6EC7EF2C1CD0C358006921EE /* paymentapp_monkey.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		0A6F02251B3AC46900F64F7B /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 0A6F02061B3AC46800F64F7B /* ipaymentapp */;
			targetProxy = 0A6F02241B3AC46900F64F7B /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		0A6F021C1B3AC46900F64F7B /* LaunchScreen.xib */ = {
			isa = PBXVariantGroup;
			children = (
				0A6F021D1B3AC46900F64F7B /* Base */,
			);
			name = LaunchScreen.xib;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		0A6F022B1B3AC46900F64F7B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = NO;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		0A6F022C1B3AC46900F64F7B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = NO;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		0A6F022E1B3AC46900F64F7B /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 23282186CDA6A31079E3CAF0 /* Pods-ipaymentapp.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CLANG_WARN_STRICT_PROTOTYPES = NO;
				CODE_SIGN_ENTITLEMENTS = ipaymentapp/ipaymentapp.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 649;
				DEFINES_MODULE = NO;
				DEVELOPMENT_TEAM = "";
				ENABLE_BITCODE = NO;
				ENABLE_DEBUG_DYLIB = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = NO;
				EXCLUDED_ARCHS = arm64;
				GCC_GENERATE_TEST_COVERAGE_FILES = YES;
				GCC_INSTRUMENT_PROGRAM_FLOW_ARCS = YES;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "ipaymentapp/ipaymentapp-Prefix.pch";
				GCC_TREAT_WARNINGS_AS_ERRORS = NO;
				INFOPLIST_FILE = ipaymentapp/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
				);
				OTHER_SWIFT_FLAGS = "-D DEBUG";
				PRODUCT_BUNDLE_IDENTIFIER = com.meituan.ipaymentapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "Meituan payment app inhouse distribution";
				SWIFT_OBJC_BRIDGING_HEADER = "ipaymentapp/ipaymentapp-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_PRECOMPILE_BRIDGING_HEADER = YES;
				SWIFT_VERSION = 4.2;
				VALID_ARCHS = "arm64 x86_64";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		0A6F022F1B3AC46900F64F7B /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = DC452069872A18B906441813 /* Pods-ipaymentapp.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CLANG_WARN_STRICT_PROTOTYPES = NO;
				CODE_SIGN_ENTITLEMENTS = ipaymentapp/ipaymentapp.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 650;
				DEFINES_MODULE = NO;
				DEVELOPMENT_TEAM = "";
				ENABLE_BITCODE = NO;
				ENABLE_DEBUG_DYLIB = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_GENERATE_TEST_COVERAGE_FILES = YES;
				GCC_INSTRUMENT_PROGRAM_FLOW_ARCS = YES;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "ipaymentapp/ipaymentapp-Prefix.pch";
				GCC_TREAT_WARNINGS_AS_ERRORS = NO;
				INFOPLIST_FILE = ipaymentapp/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.meituan.ipaymentapp310;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "Meituan payment app inhouse distribution";
				SWIFT_OBJC_BRIDGING_HEADER = "ipaymentapp/ipaymentapp-Bridging-Header.h";
				SWIFT_PRECOMPILE_BRIDGING_HEADER = YES;
				SWIFT_VERSION = 4.2;
				VALID_ARCHS = "arm64 x86_64";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		0A6F02311B3AC46900F64F7B /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 42B4E0CBE48E46E9D7FE139F /* Pods-ipaymentappTests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = "$(inherited)";
				FRAMEWORK_SEARCH_PATHS = (
					"$(SDKROOT)/Developer/Library/Frameworks",
					"$(inherited)",
				);
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = ipaymentappTests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.meituan.ipaymentapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/ipaymentapp.app/ipaymentapp";
			};
			name = Debug;
		};
		0A6F02321B3AC46900F64F7B /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 624FECA4E4E8D6B4A61E3D44 /* Pods-ipaymentappTests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = "$(inherited)";
				FRAMEWORK_SEARCH_PATHS = (
					"$(SDKROOT)/Developer/Library/Frameworks",
					"$(inherited)",
				);
				INFOPLIST_FILE = ipaymentappTests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.meituan.ipaymentapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/ipaymentapp.app/ipaymentapp";
			};
			name = Release;
		};
		A1C679C2231FEE2800AB1E42 /* DailyBuild */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = NO;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = DailyBuild;
		};
		A1C679C3231FEE2800AB1E42 /* DailyBuild */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7A75A24BF69A1E060F1D0E33 /* Pods-ipaymentapp.dailybuild.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CLANG_WARN_STRICT_PROTOTYPES = NO;
				CODE_SIGN_ENTITLEMENTS = ipaymentapp/ipaymentapp.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution: Beijing Sankuai Technology Co., Ltd.";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 649;
				DEFINES_MODULE = NO;
				DEVELOPMENT_TEAM = "";
				ENABLE_BITCODE = NO;
				ENABLE_DEBUG_DYLIB = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				EXCLUDED_ARCHS = arm64;
				GCC_GENERATE_TEST_COVERAGE_FILES = YES;
				GCC_INSTRUMENT_PROGRAM_FLOW_ARCS = YES;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "ipaymentapp/ipaymentapp-Prefix.pch";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
					"XCODE_VERSION=$(XCODE_VERSION_MAJOR)",
					"$(inherited)",
					"RETAIN_CYCLE_DETECTOR_ENABLED=1",
					"SAKPERFORMANCE_VERSION=\\@\\\"3.14.0\\\"",
					"SAKSTATISTICS_VERSION=\\@\\\"3.6.3\\\"",
					"$(inherited)",
					"SD_WEBP=1",
					"TEST=1",
				);
				GCC_TREAT_WARNINGS_AS_ERRORS = NO;
				INFOPLIST_FILE = ipaymentapp/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
				);
				OTHER_SWIFT_FLAGS = "-D TEST";
				PRODUCT_BUNDLE_IDENTIFIER = com.meituan.ipaymentapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "943545a0-ede6-4aba-9c5c-ec8494a3337d";
				PROVISIONING_PROFILE_SPECIFIER = "Meituan payment app inhouse distribution";
				SWIFT_OBJC_BRIDGING_HEADER = "ipaymentapp/ipaymentapp-Bridging-Header.h";
				SWIFT_PRECOMPILE_BRIDGING_HEADER = YES;
				SWIFT_VERSION = 4.2;
				VALID_ARCHS = "arm64 x86_64";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = DailyBuild;
		};
		A1C679C4231FEE2800AB1E42 /* DailyBuild */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 354E323A88BC676126FFF525 /* Pods-ipaymentappTests.dailybuild.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = "$(inherited)";
				FRAMEWORK_SEARCH_PATHS = (
					"$(SDKROOT)/Developer/Library/Frameworks",
					"$(inherited)",
				);
				INFOPLIST_FILE = ipaymentappTests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.meituan.ipaymentapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/ipaymentapp.app/ipaymentapp";
			};
			name = DailyBuild;
		};
		B424AD971D49FD96002A5A47 /* ReleaseInhouse */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = NO;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = ReleaseInhouse;
		};
		B424AD981D49FD96002A5A47 /* ReleaseInhouse */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0D10C2B115D21214C28EA2FC /* Pods-ipaymentapp.releaseinhouse.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CLANG_WARN_STRICT_PROTOTYPES = NO;
				CODE_SIGN_ENTITLEMENTS = ipaymentapp/ipaymentapp.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution: Beijing Sankuai Technology Co., Ltd.";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 649;
				DEFINES_MODULE = NO;
				DEVELOPMENT_TEAM = "";
				ENABLE_BITCODE = NO;
				ENABLE_DEBUG_DYLIB = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_GENERATE_TEST_COVERAGE_FILES = YES;
				GCC_INSTRUMENT_PROGRAM_FLOW_ARCS = YES;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "ipaymentapp/ipaymentapp-Prefix.pch";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
					"XCODE_VERSION=$(XCODE_VERSION_MAJOR)",
					"$(inherited)",
					"RETAIN_CYCLE_DETECTOR_ENABLED=1",
					"SAKPERFORMANCE_VERSION=\\@\\\"3.14.0\\\"",
					"SAKSTATISTICS_VERSION=\\@\\\"3.6.3\\\"",
					"$(inherited)",
					"SD_WEBP=1",
					"TEST=1",
				);
				GCC_TREAT_WARNINGS_AS_ERRORS = NO;
				INFOPLIST_FILE = ipaymentapp/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
				);
				OTHER_SWIFT_FLAGS = "-D TEST";
				PRODUCT_BUNDLE_IDENTIFIER = com.meituan.ipaymentapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "943545a0-ede6-4aba-9c5c-ec8494a3337d";
				PROVISIONING_PROFILE_SPECIFIER = "Meituan payment app inhouse distribution";
				SWIFT_OBJC_BRIDGING_HEADER = "ipaymentapp/ipaymentapp-Bridging-Header.h";
				SWIFT_PRECOMPILE_BRIDGING_HEADER = YES;
				SWIFT_VERSION = 4.2;
				VALID_ARCHS = "arm64 x86_64";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = ReleaseInhouse;
		};
		B424AD991D49FD96002A5A47 /* ReleaseInhouse */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9D7AC605843107E2C9604338 /* Pods-ipaymentappTests.releaseinhouse.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = "$(inherited)";
				FRAMEWORK_SEARCH_PATHS = (
					"$(SDKROOT)/Developer/Library/Frameworks",
					"$(inherited)",
				);
				INFOPLIST_FILE = ipaymentappTests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.meituan.ipaymentapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/ipaymentapp.app/ipaymentapp";
			};
			name = ReleaseInhouse;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		0A6F02021B3AC46800F64F7B /* Build configuration list for PBXProject "ipaymentapp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0A6F022B1B3AC46900F64F7B /* Debug */,
				0A6F022C1B3AC46900F64F7B /* Release */,
				B424AD971D49FD96002A5A47 /* ReleaseInhouse */,
				A1C679C2231FEE2800AB1E42 /* DailyBuild */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		0A6F022D1B3AC46900F64F7B /* Build configuration list for PBXNativeTarget "ipaymentapp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0A6F022E1B3AC46900F64F7B /* Debug */,
				0A6F022F1B3AC46900F64F7B /* Release */,
				B424AD981D49FD96002A5A47 /* ReleaseInhouse */,
				A1C679C3231FEE2800AB1E42 /* DailyBuild */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		0A6F02301B3AC46900F64F7B /* Build configuration list for PBXNativeTarget "ipaymentappTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0A6F02311B3AC46900F64F7B /* Debug */,
				0A6F02321B3AC46900F64F7B /* Release */,
				B424AD991D49FD96002A5A47 /* ReleaseInhouse */,
				A1C679C4231FEE2800AB1E42 /* DailyBuild */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 0A6F01FF1B3AC46800F64F7B /* Project object */;
}
