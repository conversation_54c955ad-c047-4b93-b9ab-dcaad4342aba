<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>scheme</key>
	<string>imeituan</string>
	<key>maps</key>
	<array>
		<dict>
			<key>comment</key>
			<string>专题列表页</string>
			<key>URL</key>
			<string>www.meituan.com/hottopiclist</string>
			<key>page</key>
			<string>METHotTopicListViewController</string>
			<key>rootPage</key>
			<string>MTHomePageViewController</string>
			<key>segue</key>
			<string>push</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>首页</string>
			<key>URL</key>
			<string>www.meituan.com/home</string>
			<key>page</key>
			<string>MTHomePageViewController</string>
			<key>rootPage</key>
			<string>MTHomePageViewController</string>
			<key>segue</key>
			<string>NULL</string>
		</dict>
		<dict>
			<key>needSignIn</key>
			<true/>
			<key>comment</key>
			<string>消息中心</string>
			<key>URL</key>
			<string>www.meituan.com/message/list</string>
			<key>page</key>
			<string>MTMessageCenterViewController</string>
			<key>rootPage</key>
			<string>MTMineViewController</string>
			<key>segue</key>
			<string>push</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>专题，参数tid</string>
			<key>URL</key>
			<string>www.meituan.com/hottopic</string>
			<key>page</key>
			<string>MTHotTopicDetailViewController</string>
			<key>rootPage</key>
			<string>MTHomePageViewController</string>
			<key>segue</key>
			<string>push</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>团购相册，参数did</string>
			<key>URL</key>
			<string>www.meituan.com/deal/albums</string>
			<key>page</key>
			<string>MTDealAlbumViewController</string>
			<key>rootPage</key>
			<string>MTHomePageViewController</string>
			<key>segue</key>
			<string>present-1</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>团购图文详情页，参数did</string>
			<key>URL</key>
			<string>www.meituan.com/deal/about</string>
			<key>page</key>
			<string>MTDealMoreDetailViewController</string>
			<key>rootPage</key>
			<string>MTHomePageViewController</string>
			<key>segue</key>
			<string>push</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>团购列表页，参数，group_category_id=1，category_id=40，category_name=测试</string>
			<key>URL</key>
			<string>www.meituan.com/deal/list</string>
			<key>page</key>
			<string>MTHomeDealListViewController</string>
			<key>rootPage</key>
			<string>MTHomePageViewController</string>
			<key>segue</key>
			<string>push</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>团购详情页，参数did</string>
			<key>URL</key>
			<string>www.meituan.com/deal</string>
			<key>page</key>
			<string>MTDealDetailViewController</string>
			<key>rootPage</key>
			<string>MTHomePageViewController</string>
			<key>segue</key>
			<string>push</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>周边</string>
			<key>URL</key>
			<string>www.meituan.com/near/list</string>
			<key>page</key>
			<string>MTNearbyViewController</string>
			<key>rootPage</key>
			<string>MTNearbyViewController</string>
			<key>segue</key>
			<string>NULL</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>周边全部商家列表页</string>
			<key>URL</key>
			<string>www.meituan.com/near/merchant/all/list</string>
			<key>page</key>
			<string>MTNearbyViewController</string>
			<key>rootPage</key>
			<string>MTNearbyViewController</string>
			<key>segue</key>
			<string>NULL</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>周边团购商家列表页</string>
			<key>URL</key>
			<string>www.meituan.com/near/merchant/group/list</string>
			<key>page</key>
			<string>MTNearbyViewController</string>
			<key>rootPage</key>
			<string>MTNearbyViewController</string>
			<key>segue</key>
			<string>NULL</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>商家详情页，参数id</string>
			<key>URL</key>
			<string>www.meituan.com/merchant</string>
			<key>page</key>
			<string>MTMerchantViewController</string>
			<key>rootPage</key>
			<string>MTNearbyViewController</string>
			<key>segue</key>
			<string>push</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>搜索页，参数q</string>
			<key>URL</key>
			<string>www.meituan.com/search</string>
			<key>page</key>
			<string>MTSearchResultViewController</string>
			<key>rootPage</key>
			<string>MTHomePageViewController</string>
			<key>segue</key>
			<string>push</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>未登录反馈</string>
			<key>URL</key>
			<string>www.meituan.com/suggestion</string>
			<key>page</key>
			<string>MTFeedbackViewController</string>
			<key>rootPage</key>
			<string>MTMiscViewController</string>
			<key>segue</key>
			<string>push</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>已登录反馈</string>
			<key>URL</key>
			<string>www.meituan.com/feedback</string>
			<key>page</key>
			<string>MTFeedbackViewController</string>
			<key>rootPage</key>
			<string>MTMiscViewController</string>
			<key>segue</key>
			<string>push</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>设置页</string>
			<key>URL</key>
			<string>www.meituan.com/setting</string>
			<key>page</key>
			<string>MTMiscViewController</string>
			<key>rootPage</key>
			<string>MTMiscViewController</string>
			<key>segue</key>
			<string>NULL</string>
		</dict>
		<dict>
			<key>needSignIn</key>
			<true/>
			<key>comment</key>
			<string>订单评价页，参数oid</string>
			<key>URL</key>
			<string>www.meituan.com/order/review</string>
			<key>page</key>
			<string>MTOrderCustomerReviewViewController</string>
			<key>rootPage</key>
			<string>MTMineViewController</string>
			<key>segue</key>
			<string>push</string>
		</dict>
		<dict>
			<key>needSignIn</key>
			<true/>
			<key>comment</key>
			<string>订单列表页，参数to</string>
			<key>URL</key>
			<string>www.meituan.com/order/list</string>
			<key>page</key>
			<string>METMineGroupOrderListViewController</string>
			<key>rootPage</key>
			<string>MTMineViewController</string>
			<key>segue</key>
			<string>push</string>
		</dict>
		<dict>
			<key>needSignIn</key>
			<true/>
			<key>comment</key>
			<string>选座订单列表页</string>
			<key>URL</key>
			<string>www.meituan.com/movieorder/list</string>
			<key>page</key>
			<string>METReserveOrderViewController</string>
			<key>rootPage</key>
			<string>MTMineViewController</string>
			<key>segue</key>
			<string>push</string>
		</dict>
		<dict>
			<key>needSignIn</key>
			<true/>
			<key>comment</key>
			<string>团购订单详情页，参数oid、channel</string>
			<key>URL</key>
			<string>www.meituan.com/order?channel=group</string>
			<key>page</key>
			<string>METDefaultOrderDetailViewController</string>
			<key>rootPage</key>
			<string>MTMineViewController</string>
			<key>segue</key>
			<string>push</string>
		</dict>
		<dict>
			<key>needSignIn</key>
			<true/>
			<key>comment</key>
			<string>选座订单详情页，参数oid</string>
			<key>URL</key>
			<string>www.meituan.com/movieorder</string>
			<key>page</key>
			<string>METMovieOrderDetailViewController</string>
			<key>rootPage</key>
			<string>MTMineViewController</string>
			<key>segue</key>
			<string>push</string>
		</dict>
		<dict>
			<key>needSignIn</key>
			<true/>
			<key>comment</key>
			<string>美团券列表页</string>
			<key>URL</key>
			<string>www.meituan.com/coupon/list</string>
			<key>page</key>
			<string>MTCouponListViewController</string>
			<key>rootPage</key>
			<string>MTMineViewController</string>
			<key>segue</key>
			<string>push</string>
		</dict>
		<dict>
			<key>needSignIn</key>
			<true/>
			<key>comment</key>
			<string>单张美团券，参数oid（表示团购订单）或者moid（表示选座订单）</string>
			<key>URL</key>
			<string>www.meituan.com/coupon</string>
			<key>page</key>
			<string>MTCouponViewController</string>
			<key>rootPage</key>
			<string>MTMineViewController</string>
			<key>segue</key>
			<string>push</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>收藏列表页</string>
			<key>URL</key>
			<string>www.meituan.com/collection/list</string>
			<key>page</key>
			<string>METMyCollectionContainerViewController</string>
			<key>rootPage</key>
			<string>MTMineViewController</string>
			<key>segue</key>
			<string>push</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>用webView打开，参数url</string>
			<key>URL</key>
			<string>www.meituan.com/web</string>
			<key>page</key>
			<string>SAKWebViewController</string>
			<key>rootPage</key>
			<string></string>
			<key>segue</key>
			<string>push</string>
		</dict>
		<dict>
			<key>needSignIn</key>
			<true/>
			<key>comment</key>
			<string>我的奖励</string>
			<key>URL</key>
			<string>www.meituan.com/rewards</string>
			<key>page</key>
			<string>MTRewardsViewController</string>
			<key>rootPage</key>
			<string>MTMineViewController</string>
			<key>segue</key>
			<string>push</string>
		</dict>
		<dict>
			<key>needSignIn</key>
			<true/>
			<key>comment</key>
			<string>我的帐户</string>
			<key>URL</key>
			<string>www.meituan.com/account</string>
			<key>page</key>
			<string>MTMyAccountViewController</string>
			<key>rootPage</key>
			<string>MTMineViewController</string>
			<key>segue</key>
			<string>push</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>我的首页</string>
			<key>URL</key>
			<string>www.meituan.com/user</string>
			<key>page</key>
			<string>MTMineViewController</string>
			<key>rootPage</key>
			<string>MTMineViewController</string>
			<key>segue</key>
			<string>NULL</string>
		</dict>
		<dict>
			<key>needSignIn</key>
			<true/>
			<key>comment</key>
			<string>代金券列表页</string>
			<key>URL</key>
			<string>www.meituan.com/voucher/list</string>
			<key>page</key>
			<string>MTVouchersVC</string>
			<key>rootPage</key>
			<string>MTMineViewController</string>
			<key>segue</key>
			<string>push</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>广告推荐列表页</string>
			<key>URL</key>
			<string>www.meituan.com/recommend</string>
			<key>page</key>
			<string>MTADRecommendViewController</string>
			<key>rootPage</key>
			<string>MTHomePageViewController</string>
			<key>segue</key>
			<string>push</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>团购详情页，参数dealid</string>
			<key>URL</key>
			<string>www.meituan.com/deal/</string>
			<key>page</key>
			<string>MTDealDetailViewController</string>
			<key>rootPage</key>
			<string>MTHomePageViewController</string>
			<key>segue</key>
			<string>push</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>影院选座页，参数showId date</string>
			<key>URL</key>
			<string>www.meituan.com/selectseat</string>
			<key>page</key>
			<string>MTReservViewController</string>
			<key>rootPage</key>
			<string>MTHomePageViewController</string>
			<key>segue</key>
			<string>push</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>影片的上映影院列表，movie_id=78329 coupon参数取值：hasgroup（只看团购影院）、choosesitting（只看选座影院）</string>
			<key>URL</key>
			<string>www.meituan.com/movie/cinemalist</string>
			<key>page</key>
			<string>MTMovieCinemaListViewController</string>
			<key>rootPage</key>
			<string>MTHomePageViewController</string>
			<key>segue</key>
			<string>push</string>
		</dict>
		<dict>
			<key>comment</key>
			<string>名店特惠，参数id</string>
			<key>URL</key>
			<string>www.meituan.com/todayspecial/list</string>
			<key>page</key>
			<string>METTodaySpecialListViewController</string>
			<key>rootPage</key>
			<string>MTHomePageViewController</string>
			<key>segue</key>
			<string>push</string>
		</dict>
	</array>
	<key>rootPages</key>
	<array>
		<string>MTHomePageViewController</string>
		<string>MTNearbyViewController</string>
		<string>MTMineViewController</string>
		<string>MTMiscViewController</string>
	</array>
	<key>version</key>
	<integer>0</integer>
</dict>
</plist>
