//
//  PPPicassoDebugViewController.m
//  ipaymentapp
//
//  Created by Zhang<PERSON>heng<PERSON> on 2018/1/16.
//  Copyright © 2018年 Meituan.com. All rights reserved.
//

#import "PPPicassoDebugViewController.h"
#import "SPGObjectCacheManager.h"
#import "UIImage+SAKColor.h"
#import "SAKPortal.h"
#import "CIPURLComponents.h"
#import "PicassoDebugMode.h"
#import <CIPServiceRegistry/SAKFinCrashProtectProtocol.h>
#import <CIPServiceRegistry/SAKFinPicassoModuleProtocol.h>
#import <QHOServiceManager/QHOServiceManager.h>
#import "UIColor+Addition.h"

static NSString *const kCCHPicassoTokenCacheKey = @"pp-conch-picasso-token";
static NSString *const kCCHPicassoPathCacheKey = @"pp-conch-picasso-path";

NSString *const kCCHPicassoDebugViewControllerURL = @"iconch://debug/playpicasso";

@interface PPPicassoDebugViewController () <SAKPortalable, UITableViewDataSource, UITableViewDelegate>

@property (nonatomic, strong) UITextField *tokenField;
@property (nonatomic, strong) UITextField *pathField;
@property (nonatomic, strong) UIButton *beginButton;
@property (nonatomic, strong) UITableView *pathTableView;
@property (nonatomic, copy) NSArray *pathArray;

@end

@implementation PPPicassoDebugViewController

SAK_PORTAL_REGISTER()
{
#if TEST || DEBUG
    NSString *defaultURLString = [NSString stringWithFormat:@"%@?serverip=xxx", kCCHPicassoDebugViewControllerURL];
    [SAKPortal registerPortalWithURL:[NSURL URLWithString:defaultURLString] forClass:[PPPicassoDebugViewController class]];
#endif
    
    [SAKPortal registerPortalWithHandler:^UIViewController<SAKPortalable> * _Nullable(NSURL *URL, BOOL shouldTransfer, UIViewController *sourceViewController) {
        
        if ([URL hasSameTrunkWithURL:[NSURL URLWithString:kCCHPicassoDebugViewControllerURL]]) {
            NSMutableDictionary *paramDictonary = [[[CIPURLComponents alloc] initWithURL:URL] queryParameterDictionary];
            
            NSString *token = paramDictonary[@"token"];

            PPPicassoDebugViewController *destinationController = [[PPPicassoDebugViewController alloc] initWithToken:token];
            [sourceViewController.navigationController pushViewController:destinationController animated:YES];
            
            return destinationController;
        } else {
            return nil;
        }
    } prefixURL:[NSURL URLWithString:kCCHPicassoDebugViewControllerURL] pageInfo:[SAKPortalPageInfo pageInfoWithPageName:@"picasso调试页面" className:NSStringFromClass([PPPicassoDebugViewController class]) path:kCCHPicassoDebugViewControllerURL requiredParameters:@"serverip|服务ip,token" optionalParameters:nil]];
}

- (instancetype)initWithToken:(NSString *)token
{
    self = [super init];
    if (self) {
        self.tokenField.text = token;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.title = @"Picasso调试";
    self.view.backgroundColor = [UIColor generalBackgroundColor];
    [self setupUI];
}

- (void)jumpToPicassoModuleVC
{
    [[SPGObjectCacheManager sharedCacheManager] saveObject:self.tokenField.text forKey:kCCHPicassoTokenCacheKey];
    [[SPGObjectCacheManager sharedCacheManager] saveObject:self.pathField.text forKey:kCCHPicassoPathCacheKey];
    
//    [[PicassoDebugMode instance] startMonitorWithToken:self.tokenField.text];
    UIViewController<SFPicassoWalletModule> *destinationController = QHO_OPTIONAL_SERVICE_FOR(SFPicassoWalletModule);
    
    [self.navigationController pushViewController:destinationController animated:YES];
}

- (void)setupUI
{
    [self.view addSubview:self.tokenField];
    [self.view addSubview:self.pathField];
    [self.view addSubview:self.beginButton];
    [self.view addSubview:self.pathTableView];
    
    [self.tokenField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(SCREEN_WIDTH - 100, 35));
        make.top.equalTo(self.view).offset(20);
        make.centerX.equalTo(self.view);
    }];
    
    [self.pathField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.equalTo(self.tokenField);
        make.centerX.equalTo(self.tokenField);
        make.top.equalTo(self.tokenField.mas_bottom).offset(15);
    }];
    
    [self.beginButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.equalTo(self.tokenField);
        make.centerX.equalTo(self.tokenField);
        make.top.equalTo(self.pathField.mas_bottom).offset(15);
    }];
    
    [self.pathTableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.bottom.right.equalTo(self.view);
        make.top.equalTo(self.beginButton.mas_bottom).offset(30);
    }];
}

#pragma mark - Properties

- (UITextField *)tokenField
{
    if (!_tokenField) {
        _tokenField = [[UITextField alloc] init];
        _tokenField.placeholder = @"输入token";
        _tokenField.layer.borderColor = [HEXCOLOR(0x31BCAD) CGColor];
        _tokenField.layer.borderWidth = 1;
        _tokenField.textAlignment = NSTextAlignmentCenter;
        
        NSString *tokenString = [[SPGObjectCacheManager sharedCacheManager] objectForKey:kCCHPicassoTokenCacheKey];
        if (tokenString.length > 0) {
            _tokenField.text = tokenString;
        }
    }
    return _tokenField;
}

- (UITextField *)pathField
{
    if (!_pathField) {
        _pathField = [[UITextField alloc] init];
        _pathField.placeholder = @"输入后端接口path";
        _pathField.layer.borderColor = [HEXCOLOR(0x31BCAD) CGColor];
        _pathField.layer.borderWidth = 1;
        _pathField.textAlignment = NSTextAlignmentCenter;
        
        NSString *pathString = [[SPGObjectCacheManager sharedCacheManager] objectForKey:kCCHPicassoPathCacheKey];
        if (pathString.length > 0) {
            _pathField.text = pathString;
        }
    }
    return _pathField;
}

- (UIButton *)beginButton
{
    if (!_beginButton) {
        _beginButton = [UIButton new];
        [_beginButton setTitle:@"开始" forState:UIControlStateNormal];
        [_beginButton setBackgroundImage:[UIImage imageWithColor:HEXCOLOR(0x31BCAD)] forState:UIControlStateNormal];
        [_beginButton addTarget:self action:@selector(jumpToPicassoModuleVC)
         forControlEvents:UIControlEventTouchUpInside];
    }
    return _beginButton;
}

- (UITableView *)pathTableView
{
    if (!_pathTableView) {
        _pathTableView = [UITableView new];
        
        [_pathTableView registerClass:[UITableViewCell class] forCellReuseIdentifier:@"UITableViewCell"];
        
        _pathTableView.delegate = self;
        _pathTableView.dataSource = self;
    }
    return _pathTableView;
}

- (NSArray *)pathArray
{
    // 新加的页面可以在这儿加，方便调试
    return @[
             @"/conch/picasso/wallet/walletMain(钱包首页)"
             ];
}

#pragma mark - TableView delegate

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.pathArray.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"UITableViewCell"];
    if (!cell) {
        cell =  [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:@"UITableViewCell"];
    }
    
    cell.textLabel.text = self.pathArray[indexPath.row];
    
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return 40;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    self.pathField.text = self.pathArray[indexPath.row];
}

#pragma mark - SAKPortalable

- (NSString *)pageDescription
{
    return NSStringFromClass([self class]);
}

- (void)dealloc
{
    _pathTableView.delegate = nil;
    _pathTableView.dataSource = nil;
}

@end
