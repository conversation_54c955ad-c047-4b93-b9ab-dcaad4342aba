//
//  PPPicassoVCDebugViewController.m
//  ipaymentapp
//
//  Created by qinchengbo on 2018/11/22.
//  Copyright © 2018 Meituan.com. All rights reserved.
//

#import "PPPicassoVCDebugViewController.h"
#import "NSDictionary+SPKShortcuts.h"
#import "PicassoDebugMode.h"
#import "SAKPortal.h"
#import "UIColor+Addition.h"

@interface PPPicassoVCDebugViewController () <UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UITextField *tokenTextView;
@property (nonatomic, strong) UITextField *picassoIDTextView;
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, copy) NSDictionary<NSString *, NSArray<NSString *> *> *dataSource;
@property (nonatomic, copy) NSArray<NSString *> *allSection;
@property (nonatomic, copy) NSString *token;
@property (nonatomic, copy) NSString *JSPath;
@property (nonatomic, copy) NSString *selectedSection;

@end

@implementation PPPicassoVCDebugViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self initUI];
    [self loadLocalData];
    [self.tableView registerClass:[UITableViewCell class] forCellReuseIdentifier:@"cell"];
    [self.tableView registerClass:[UITableViewHeaderFooterView class] forHeaderFooterViewReuseIdentifier:@"header"];
}

- (void)initUI
{
    self.edgesForExtendedLayout = UIRectEdgeNone;
    self.view.backgroundColor = [UIColor generalBackgroundColor];
    self.tableView = [[UITableView alloc] initWithFrame:CGRectZero];
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    self.tokenTextView = [[UITextField alloc] initWithFrame:CGRectZero];
    self.tokenTextView.font = Font(14);
    self.tokenTextView.placeholder = @"输入调试 Token";
    RAC(self, token) = self.tokenTextView.rac_textSignal;
    self.picassoIDTextView = [[UITextField alloc] initWithFrame:CGRectZero];
    self.picassoIDTextView.placeholder = @"输入 JSPath";
    self.picassoIDTextView.font = Font(14);
    RAC(self, JSPath) = self.picassoIDTextView.rac_textSignal;
    UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
    [button setTitle:@"Let's GO" forState:UIControlStateNormal];
    [button setTitleColor:[UIColor generalLabelTextColor] forState:UIControlStateNormal];
    [button addTarget:self action:@selector(goToDebug:) forControlEvents:UIControlEventTouchUpInside];
    UILabel *tokenLabel = [[UILabel alloc] initWithFrame:CGRectZero];
    tokenLabel.text = @"Token:";
    tokenLabel.font = Font(14);
    UILabel *picassoIDLabel = [[UILabel alloc] initWithFrame:CGRectZero];
    picassoIDLabel.text = @"PicassoID:";
    picassoIDLabel.font = Font(14);
    
    [self.view addSubview:tokenLabel];
    [self.view addSubview:picassoIDLabel];
    [self.view addSubview:button];
    [self.view addSubview:self.tableView];
    [self.view addSubview:self.tokenTextView];
    [self.view addSubview:self.picassoIDTextView];
    
    [tokenLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).offset(15);
        make.top.equalTo(self.view).offset(21);
    }];
    [picassoIDLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).offset(15);
        make.top.equalTo(tokenLabel.mas_bottom).offset(26);
    }];
    [button mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(picassoIDLabel.mas_bottom).offset(25);
        make.centerX.equalTo(self.view);
    }];
    [self.tokenTextView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).offset(90);
        make.centerY.equalTo(tokenLabel);
    }];
    [self.picassoIDTextView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).offset(90);
        make.centerY.equalTo(picassoIDLabel);
    }];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.bottom.right.equalTo(self.view);
        make.top.equalTo(button.mas_bottom).offset(25);
    }];
}

- (NSMutableDictionary *)plistFile
{
    return [NSMutableDictionary dictionaryWithContentsOfFile:[[NSBundle mainBundle] pathForResource:@"JSPathData" ofType:@"plist"]];;
}

- (void)loadLocalData
{
    NSMutableDictionary *localData = [self plistFile];
    self.dataSource = [localData spk_dictionaryForKey:@"picassoIDs"];
    self.allSection = self.dataSource.allKeys;
    self.token = [localData spk_stringForKey:@"token"];
    self.tokenTextView.text = self.token;
}

- (void)saveTokenWith:(NSString *)token
{
    if (token.length > 0) {
        NSMutableDictionary *localData = [self plistFile];
        [localData spk_setStringParameter:token forKey:@"token"];
    }
}

- (void)goToDebug:(UIButton *)sender {
    [self saveTokenWith:self.tokenTextView.text];
//    [[PicassoDebugMode instance] startMonitorWithToken:self.token ? : @""];
    NSString *picassoID = [NSString stringWithFormat:@"%@%@", self.selectedSection, self.picassoIDTextView.text];
    NSString *URLStr = [NSString stringWithFormat:@"meituanpayment://financedynamic/picassobox?jsPath=%@",picassoID];
    [SAKPortal transferFromViewController:self toURL:[NSURL URLWithString:URLStr] completion:nil];
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return self.allSection.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.dataSource[self.allSection[section]].count;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section
{
    UITableViewHeaderFooterView *view = [tableView dequeueReusableHeaderFooterViewWithIdentifier:@"header"];
    view.textLabel.text = self.allSection[section];
    return view;
}

- (NSString *)getCellInfoWith:(NSIndexPath *)indexPath
{
    return self.dataSource[self.allSection[indexPath.section]][indexPath.row];
}

- (NSString *)getSeletedSectionNameWith:(NSIndexPath *)indexPath
{
    return [NSString stringWithFormat:@"%@", self.allSection[indexPath.section]];
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"cell" forIndexPath:indexPath];
    cell.textLabel.text = [self getCellInfoWith:indexPath];
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    self.picassoIDTextView.text = [self getCellInfoWith:indexPath];
    self.selectedSection = [self getSeletedSectionNameWith:indexPath];
}

@end
