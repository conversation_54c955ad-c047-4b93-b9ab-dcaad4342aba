//
//  PPDebugWindow.m
//  imeituan
//
//  Created by 赵恩生 on 15/7/31.
//  Copyright © 2015年 meituan.com. All rights reserved.
//

#import "PPDebugWindow.h"

NSString* const PPDebugWindowDidReceiveShakeNotification = @"PPDebugWindowDidReceiveShakeNotification";

@implementation PPDebugWindow

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        
    }
    return self;
}

#pragma mark -

#pragma mark UIResponder

- (void)motionEnded:(UIEventSubtype)motion withEvent:(UIEvent *)event
{
    if (self.detectsShake) {
        if (event.type == UIEventTypeMotion && motion == UIEventSubtypeMotionShake) {
            [[NSNotificationCenter defaultCenter] postNotificationName:PPDebugWindowDidReceiveShakeNotification object:self];
        }
    }
    else {
        //
        // NOTE:
        // By calling super-class method, default shaking method
        // e.g. UITextView's undo/redo will be safely performed.
        //
        [super motionEnded:motion withEvent:event];
    }
}

@end
