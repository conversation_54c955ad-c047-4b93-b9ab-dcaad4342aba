//
//  PPQRCodeScanViewController.m
//  ipaymentapp
//
//  Created by honglianglu on 2018/8/29.
//  Copyright © 2018 Meituan.com. All rights reserved.
//

#import "PPQRCodeScanViewController.h"
#import <AVFoundation/AVFoundation.h>
#import "SAKPortal.h"
#import "CIPStringAdditions.h"
#import "SPKAlertView.h"
#import "PPQRScanChildViewController.h"

@interface PPQRCodeScanViewController () <SAKPortalable>

@property (nonatomic, strong) UIButton *closeButton;

@property (nonatomic, strong) AVCaptureSession *captureSession;
@property (nonatomic, strong) AVCaptureDeviceInput *deviceInput;
@property (nonatomic, strong) AVCaptureVideoPreviewLayer *previewLayer;
@property (nonatomic, strong) PPQRScanChildViewController *QRScanViewController;

@property (nonatomic, assign) BOOL authorizationDenied;

@end

@implementation PPQRCodeScanViewController

SAK_PORTAL_REGISTER()
{
#if TEST || DEBUG
    NSString *defaultURLString = @"imeituan://www.meituan.com/scanQRCode";
    [SAKPortal registerPortalWithURL:[NSURL URLWithString:defaultURLString] forClass:[PPQRCodeScanViewController class]];
#endif
    [SAKPortal registerPortalWithHandler:^UIViewController<SAKPortalable> * _Nullable(NSURL *URL, BOOL shouldTransfer, UIViewController *sourceViewController) {
        PPQRCodeScanViewController *scanViewController = [[PPQRCodeScanViewController alloc] init];
        if (shouldTransfer) {
            [sourceViewController presentViewController:scanViewController animated:YES completion:nil];
        }
        return scanViewController;
    } prefixURL:[NSURL URLWithString:@"imeituan://www.meituan.com/scanQRCode"]  pageInfo:[SAKPortalPageInfo pageInfoWithPageName:@"二维码扫描页面" className:@"PPQRCodeScanViewController" path:@"imeituan://www.meituan.com/scanQRCode" requiredParameters:nil optionalParameters:nil]];
}

#pragma mark - SAKPortalable

- (NSString *)pageDescription
{
    return NSStringFromClass([self class]);
}

#pragma mark - View lifecyle

- (void)viewDidLoad
{
    [super viewDidLoad];

    [self checkCameraAvailable];
    //两个VC共用一个Session,因为多个Session切换会卡顿
    if ([self configSession]) {
        [self configQR];
    }
    
    [self.view addSubview:self.closeButton];

}

- (UIButton *)closeButton
{
    if (_closeButton) {
        return _closeButton;
    }
    _closeButton = [UIButton buttonWithType:UIButtonTypeCustom];
    _closeButton.backgroundColor = RGBACOLOR(0x00, 0x00, 0x00, 0.4);
    _closeButton.frame = CGRectMake(15.0f, STATUS_BAR_HEIGHT + 5, 38, 38);
    _closeButton.layer.cornerRadius = 19.0f;
    [_closeButton addTarget:self action:@selector(close:) forControlEvents:UIControlEventTouchUpInside];
    _closeButton.isAccessibilityElement = YES;
    _closeButton.accessibilityLabel = @"返回";
    return _closeButton;
}

- (void)close:(id)sender
{
    if (self.codeScanCancel) {
        self.codeScanCancel();
    }
    [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)configQR {
    [self addChildViewController:self.QRScanViewController];
    [self.view addSubview:self.QRScanViewController.view];
    
    self.QRScanViewController.codeScanFailed = self.codeScanFailed;
    self.QRScanViewController.codeScanSucceed = self.codeScanSucceed;
    
    self.QRScanViewController.previewLayer = self.previewLayer;
    self.QRScanViewController.session = self.captureSession;
}
- (void)dealloc
{
    [self.captureSession removeInput:self.deviceInput];
}

#pragma mark - Private

- (BOOL)configSession
{
    self.captureSession = [[AVCaptureSession alloc] init];
    
    NSError *error = nil;
    AVCaptureDevice *captureDevice = [AVCaptureDevice defaultDeviceWithMediaType:AVMediaTypeVideo];
    AVCaptureDeviceInput *deviceInput = [AVCaptureDeviceInput deviceInputWithDevice:captureDevice error:&error];
    if (error || !deviceInput) {
        NSLog(@"%@", [error localizedDescription]);
        return NO;
    }
    self.deviceInput = deviceInput;
    
    if ([self.captureSession canAddInput:deviceInput]) {
        [self.captureSession addInput:deviceInput];
    }
    if ([self.captureSession canSetSessionPreset:AVCaptureSessionPreset1920x1080]) {
        self.captureSession.sessionPreset = AVCaptureSessionPreset1920x1080;
    } else {
        self.captureSession.sessionPreset = AVCaptureSessionPreset1280x720;
    }
    
    // output 让子vc自己控制
    self.QRScanViewController = [[PPQRScanChildViewController alloc] init];
    AVCaptureOutput *output = [self.QRScanViewController customOutput];
    if ([self.captureSession canAddOutput:output]) {
        [self.captureSession addOutput:output];
    }
    
    [captureDevice lockForConfiguration:nil];
    if ([captureDevice isSmoothAutoFocusSupported]) {
        captureDevice.smoothAutoFocusEnabled = YES;
    }
    [captureDevice unlockForConfiguration];
    
    self.previewLayer = [AVCaptureVideoPreviewLayer layerWithSession:self.captureSession];
    self.previewLayer.videoGravity = AVLayerVideoGravityResizeAspectFill;
    self.previewLayer.frame = self.view.bounds;
    [self.view.layer addSublayer:self.previewLayer];
    
    return YES;
}

- (void)checkCameraAvailable
{
    if(![UIImagePickerController isSourceTypeAvailable:UIImagePickerControllerSourceTypeCamera]) {
        [SPKAlertView showAlertViewWithMessage:@"该设备不支持识别二维码功能"
                         completionButtonTitle:@"我知道了"
                                    completion:^{
                                        [self dismissViewControllerAnimated:YES completion:nil];
                                    }];
        return;
    } else {
        if([AVCaptureDevice respondsToSelector:@selector(authorizationStatusForMediaType:)]) {
            NSString *mediaType = AVMediaTypeVideo; // Or AVMediaTypeAudio
            AVAuthorizationStatus authStatus = [AVCaptureDevice authorizationStatusForMediaType:mediaType];
            if(authStatus == AVAuthorizationStatusDenied) {
                self.authorizationDenied = YES;
            } else {
                self.authorizationDenied = NO;
            }
        }
    }
    NSError *error = nil;
    AVCaptureDevice *captureDevice = [AVCaptureDevice defaultDeviceWithMediaType:AVMediaTypeVideo];
    AVCaptureDeviceInput *deviceInput = [AVCaptureDeviceInput deviceInputWithDevice:captureDevice error:&error];
    
    if (error || !deviceInput) {
        
        if (self.authorizationDenied) {
            NSString *alertTitleString = @"相机授权未开启";
            NSString *messageString = @"请在系统设置中开启相机授权";
            NSString *cancelTitleString = @"暂不";
            NSString *otherButtonTitle = @"去设置";
            [SPKAlertView showAlertViewWithTitle:alertTitleString
                                         message:messageString
                               cancelButtonTitle:cancelTitleString
                           completionButtonTitle:otherButtonTitle
                                        canceled:^{
#pragma deploymate push "ignored-api-availability"
                                            [[UIApplication sharedApplication] openURL:[NSURL URLWithString:UIApplicationOpenSettingsURLString]];
#pragma deploymate pop
                                            // 跳过去的时候就 pop，不然取消之后回来会一直白屏（一个什么都没加载的状态）
                                            [self dismissViewControllerAnimated:YES completion:nil];
                                        } completion:^{
                                            [self dismissViewControllerAnimated:YES completion:nil];
                                            
                                        }];
        } else {
            [SPKAlertView showAlertViewWithMessage:@"该设备不支持识别二维码功能"
                             completionButtonTitle:@"我知道了"
                                        completion:^{
                                            [self dismissViewControllerAnimated:YES completion:nil];
                                        }];
        }
    }
    
}

- (UIInterfaceOrientationMask)supportedInterfaceOrientations
{
    return UIInterfaceOrientationMaskPortrait;
}


@end
