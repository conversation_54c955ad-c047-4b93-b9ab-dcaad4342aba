//
//  PPBarCoverView.m
//  ipaymentapp
//
//  Created by ho<PERSON>lianglu on 2018/8/29.
//  Copyright © 2018 Meituan.com. All rights reserved.
//

#import "PPBarCoverView.h"

@implementation PPBarCoverView

- (void)drawRect:(CGRect)rect
{
    [super drawRect:rect];
    
    CGContextRef context = UIGraphicsGetCurrentContext();
    CGContextSaveGState(context);
    CGContextSetRGBFillColor(context, 0, 0, 0, 0.5);
    
    CGRect clipRect = CGRectMake((rect.size.width - _scanWith) / 2.0, (rect.size.height - _scanWith) / 2.0, _scanWith, _scanWith);
    CGContextAddRect(context,clipRect);
    
    CGRect boundingRect = CGContextGetClipBoundingBox(context);
    CGContextAddRect(context, boundingRect);
    CGContextEOClip(context);
    
    
    CGContextBeginPath(context);
    CGContextAddRect(context,CGRectMake(0.0f, 0.0f, rect.size.width, rect.size.height));
    CGContextClosePath(context);
    
    CGContextFillPath(context);
    
    CGContextRestoreGState(context);
}

@end
