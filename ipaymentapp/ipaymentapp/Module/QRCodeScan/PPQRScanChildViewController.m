//
//  PPQRScanChildViewController.m
//  ipaymentapp
//
//  Created by honglianglu on 2018/8/29.
//  Copyright © 2018 Meituan.com. All rights reserved.
//

#import "PPQRScanChildViewController.h"
#import "PPBarCoverView.h"
#import "TKAlertCenter+NSError.h"
#import "DSActivityView.h"
#import "SPKAlertView.h"
#import "SAKEnvironment.h"
#import "NVDebugNetworkAgent.h"
#import "CIPStringAdditions.h"
#import "NSObject+SPKStatistics.h"
#import "SAKPortal.h"
#import <SAKStatistics.h>
#import <SAKPaymentKit/SPKToastCenter.h>

CGFloat const kPPQRCodeScanButtonWH = 38;
CGFloat const kPPQRCodeScanMargin = 15;
static const CGFloat kPPQRCodeCoverWidth = 240.0f;
static const CGFloat kPPQRCodeScanWidth = 280.0f;

@interface PPQRScanChildViewController () <AVCaptureMetadataOutputObjectsDelegate, UINavigationControllerDelegate>

@property (nonatomic, strong) UIView *coverView;
@property (nonatomic, strong) AVCaptureMetadataOutput *output;
@property (nonatomic, readonly) NSArray *supportScheme;
@property (nonatomic, strong) UIVisualEffectView *visualEffectView;

@end

@implementation PPQRScanChildViewController

- (NSArray *)supportScheme
{
#if TEST || DEBUG
    return @[@"https", @"http", @"imeituan", @"meituanpayment", @"portm"];
#endif
    return @[@"https", @"http", @"imeituan", @"meituanpayment"];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self.view addSubview:self.visualEffectView];
    [self.view addSubview:self.coverView];

    self.view.backgroundColor = [UIColor clearColor];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(didReceiveUIApplicationDidEnterBackgroundNotification:)
                                                 name:UIApplicationDidEnterBackgroundNotification
                                               object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(didReceiveUIApplicationDidBecomeActiveNotification:)
                                                 name:UIApplicationDidBecomeActiveNotification object:nil];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self hiddenVisualEffectView];
    
    if (![self.session isRunning]) {
        [self startScanning];
    }
    if (self.navigationController) {
        [self.navigationController setNavigationBarHidden:YES animated:NO];
    }
}

- (void)viewWillLayoutSubviews {
    [super viewWillLayoutSubviews];
    self.visualEffectView.frame = self.view.bounds;
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    [self showVisualEffectView];
}

- (void)dealloc
{
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    [self.session removeOutput:self.output];
    [self stopScanning];
}

#pragma mark - delegate

- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingImage:(UIImage *)image editingInfo:(nullable NSDictionary<NSString *,id> *)editingInfo
{
    [DSBezelActivityView activityViewForView:picker.view withLabel:@"加载中..."];
    //识别图片
    CIDetector *detector = [CIDetector detectorOfType:CIDetectorTypeQRCode context:nil options:@{CIDetectorAccuracy:CIDetectorAccuracyHigh}];
    NSArray *features = [detector featuresInImage:[CIImage imageWithCGImage:image.CGImage]];
    
    NSString *content = @"";
    //取出探测到的数据
    for (CIQRCodeFeature *result in features) {
        if (result.messageString.length > 0) {
            content = result.messageString;
        }
    }
    
    //识别结果
    if (features.count > 0) {
        @weakify(self);
        [self handleScanResult:content completion:^(BOOL success, NSURL *resultURL) {
            @strongify(self);
            [picker dismissViewControllerAnimated:YES completion:nil];
            if (success) {
                [self dismissViewControllerAnimated:YES completion:^{
                    [SAKPortal transferFromViewController:nil toURL:resultURL completion:nil];
                }];
            } else {
                [[TKAlertCenter defaultCenter] postAlertWithMessage:@"加载失败，请重新上传照片进行识别"];
            }
            [DSBezelActivityView removeViewAnimated:YES];
        }];
    } else {
        [picker dismissViewControllerAnimated:YES completion:nil];
        [[TKAlertCenter defaultCenter] postAlertWithMessage:@"照片中未识别到二维码"];
        [DSBezelActivityView removeViewAnimated:YES];
    }
}

- (void)showVisualEffectView {
    [UIView animateWithDuration:0.25 animations:^{
        self.visualEffectView.effect = [UIBlurEffect effectWithStyle:UIBlurEffectStyleLight];
    }];
}

- (void)hiddenVisualEffectView {
    [UIView animateWithDuration:0.25 animations:^{
        self.visualEffectView.effect = nil;
    }];
}

#pragma mark - Private
- (void)startScanning
{
    [self updateCodeTypes];
    [self.session startRunning];
}

- (void)stopScanning
{
    if ([self.session isRunning]) {
        [self.session stopRunning];
    }
}

- (AVCaptureOutput *)customOutput
{
    self.output = [[AVCaptureMetadataOutput alloc] init];
    CGFloat width = SCREEN_WIDTH;
    CGFloat height = SCREEN_HEIGHT;
    CGRect focusRect = CGRectMake(((width - kPPQRCodeScanWidth) / 2), ((height - kPPQRCodeScanWidth) / 2), kPPQRCodeScanWidth, kPPQRCodeScanWidth);
    CGFloat p1 = height/width;
    CGFloat p2 = 1920.0/1080.0;
    if (p1 < p2) {
        CGFloat pictureHeight = width * p2;
        CGFloat pictureWidth = width;
        CGFloat padding = (pictureHeight - height)/2.0;
        self.output.rectOfInterest = CGRectMake((focusRect.origin.y + padding)/pictureHeight, focusRect.origin.x/pictureWidth, focusRect.size.height/pictureHeight, focusRect.size.width/pictureWidth);
    } else {
        CGFloat pictureWidth = height / p2;
        CGFloat pictureHeight = height;
        CGFloat padding = (pictureWidth - width)/2.0;
        self.output.rectOfInterest = CGRectMake(focusRect.origin.y/pictureHeight, (focusRect.origin.x + padding)/pictureWidth, focusRect.size.height/pictureHeight, focusRect.size.width/pictureWidth);
    }
    [self.output setMetadataObjectsDelegate:self queue:dispatch_get_main_queue()];
    return self.output;
}

- (UIView *)coverView
{
    if (_coverView) {
        return _coverView;
    }
    
    _coverView = [[UIView alloc] initWithFrame:self.view.bounds];
    CGFloat width = SCREEN_WIDTH;
    CGFloat height = SCREEN_HEIGHT;
    _coverView.backgroundColor = [UIColor clearColor];
    
    PPBarCoverView *view = [[PPBarCoverView alloc] initWithFrame:CGRectMake(0.0f, 0.0f, width, height)];
    view.scanWith = kPPQRCodeCoverWidth;
    view.backgroundColor = [UIColor clearColor];
    [_coverView addSubview:view];
    
    CGRect focusRect = CGRectMake(((width - kPPQRCodeCoverWidth) / 2), ((height - kPPQRCodeCoverWidth) / 2), kPPQRCodeCoverWidth, kPPQRCodeCoverWidth);
    UILabel *label = [[UILabel alloc] initWithFrame:CGRectMake(focusRect.origin.x, focusRect.origin.y + focusRect.size.height + 20.0f, focusRect.size.width, 32.0f)];
    label.numberOfLines = 1;
    label.font = Font(13.0f);
    label.layer.cornerRadius = label.height / 2.0;
    label.clipsToBounds = YES;
    label.textAlignment = NSTextAlignmentCenter;
    label.textColor = RGBCOLOR(0xff, 0xff, 0xff);
    [_coverView addSubview:label];
    
    return _coverView;
}

- (UIVisualEffectView *)visualEffectView {
    if (!_visualEffectView) {
        UIBlurEffect *blurEffect = [UIBlurEffect effectWithStyle:UIBlurEffectStyleLight];
        UIVisualEffectView *effectView = [[UIVisualEffectView alloc] initWithEffect:blurEffect];
        _visualEffectView = effectView;
    }
    return _visualEffectView;
}

#pragma mark - Detection
#pragma deploymate push "ignored-api-availability"
- (void)detectCodeObject:(AVMetadataMachineReadableCodeObject *)codeObject
{
    [self stopScanning];
    @weakify(self);
    [self handleScanResult:codeObject.stringValue completion:^(BOOL success, NSURL *resultURL) {
        @strongify(self);
        if (success) {
            [self dismissViewControllerAnimated:YES completion:^{
                [SAKPortal transferFromViewController:nil toURL:resultURL completion:nil];
            }];
        } else {
            [SPKAlertView showAlertViewWithMessage:@"对不起，暂不支持此二维码"
                             completionButtonTitle:@"知道了"
                                        completion:^{
                                            [self startScanning];
                                        }];
        }
    }];
}
#pragma deploymate pop

#pragma mark - AVCaptureMetadataOutputObjectsDelegate

- (void)captureOutput:(AVCaptureOutput *)captureOutput didOutputMetadataObjects:(NSArray *)metadataObjects fromConnection:(AVCaptureConnection *)connection {
    if (self.view.hidden) {
        return;
    }
    NSMutableArray *validCodes = [[NSMutableArray alloc] init];
    for (AVMetadataObject *metadataObject in metadataObjects) {
#pragma deploymate push "ignored-api-availability"
        AVMetadataMachineReadableCodeObject *codeObject = (AVMetadataMachineReadableCodeObject *)[self.previewLayer transformedMetadataObjectForMetadataObject:metadataObject];
        if ([codeObject respondsToSelector:@selector(stringValue)]) {
            if (codeObject && codeObject.stringValue) {
                [validCodes addObject:codeObject];
            }
        }
#pragma deploymate pop
    }
    
    if ([validCodes count] > 0) {
        [self detectCodeObject:validCodes[0]];
    }
}

#pragma mark - Code Types
- (void)updateCodeTypes
{
    NSMutableSet *metadataObjectTypesSet = [NSMutableSet setWithArray:self.output.availableMetadataObjectTypes];
    [metadataObjectTypesSet removeObject:AVMetadataObjectTypeFace];
    self.output.metadataObjectTypes = [metadataObjectTypesSet allObjects];
}

#pragma mark - Action
- (void)handleScanResult:(NSString *)result completion:(void (^)(BOOL success, NSURL *resultURL))completion
{
#if TEST || DEBUG
    if (result != nil && result.length) {
        if ([result isEqualToString:@"imeituan://www.meituan.com/error"]) { // 测试相册扫码时间过长时的 loading 效果
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                completion(NO, nil);
            });
            return;
        }
        [SPKAlertView showAlertViewWithTitle:@"扫一扫操作选择"
                                     message:@"复制到剪切板？"
                           cancelButtonTitle:@"打开网址"
                       completionButtonTitle:@"复制到剪切板"
                                    canceled:^{
            NSURL* tempURL = [NSURL URLWithString:result];
            if (!tempURL) {
                [SPKAlertView showAlertViewWithMessage:result completionButtonTitle:@"知道了" completion:nil];
                return;
            }
            
            NSURLComponents *urlComponents = [[NSURLComponents alloc] initWithString:result];
            NSURLQueryItem *queryItem = [NSURLQueryItem queryItemWithName:@"uuid" value:[SAKEnvironment environment].UUID ? : @""];
            urlComponents.queryItems = [[urlComponents queryItems] arrayByAddingObject:queryItem];
            NSString *resultForNova = [urlComponents.URL absoluteString];
            if ([[NVDebugNetworkAgent instance] handleMockDomain:resultForNova] || [SAKStatistics handleMockURL:[NSURL URLWithString:result]]) {
                [self dismissViewControllerAnimated:YES completion:nil];
                return;
            }
        } completion:^{
            UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
            pasteboard.string = result;
            [[SPKToastCenter defaultCenter] toastWithMessage:@"复制成功"];
        }];
    }
    
#endif
    // 弹窗
    [SPKAlertView showAlertViewWithTitle:@"扫一扫操作选择"
                                 message:@"复制到剪切板？"
                       cancelButtonTitle:@"打开网址"
                   completionButtonTitle:@"复制到剪切板"
                                canceled:^{
        NSString *resultCopy = result;
        NSURL *resultURL = nil;
        if (result) {
            resultCopy = [result stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
            resultCopy = [result stringByReplacingOccurrencesOfString:@"\r" withString:@""];
            resultCopy = [result stringByReplacingOccurrencesOfString:@"\n" withString:@""];
            resultCopy = [result stringByReplacingOccurrencesOfString:@" " withString:@""];
            resultURL = [NSURL URLWithString:resultCopy];
        }
        // 如果NSURL生成失败，将非法字符urlEncode后再次尝试
        if (result && !resultURL) {
            NSMutableCharacterSet *urlAllowedCharacterSet = [[NSMutableCharacterSet alloc] init];
            [urlAllowedCharacterSet formUnionWithCharacterSet:[NSCharacterSet URLHostAllowedCharacterSet]];
            [urlAllowedCharacterSet formUnionWithCharacterSet:[NSCharacterSet URLQueryAllowedCharacterSet]];
            [urlAllowedCharacterSet formUnionWithCharacterSet:[NSCharacterSet URLPathAllowedCharacterSet]];
            [urlAllowedCharacterSet formUnionWithCharacterSet:[NSCharacterSet URLQueryAllowedCharacterSet]];
            [urlAllowedCharacterSet formUnionWithCharacterSet:[NSCharacterSet URLFragmentAllowedCharacterSet]];
            
            NSString *encodedResult = [result stringByAddingPercentEncodingWithAllowedCharacters:urlAllowedCharacterSet];
            resultURL = [NSURL URLWithString:encodedResult];
        }
        
        if (resultURL) {
            if (self.codeScanSucceed) {
                self.codeScanSucceed(resultURL);
            } else if ([self.supportScheme containsObject:resultURL.scheme]) {
                if ([resultURL.scheme isEqualToString:@"http"] ||
                    [resultURL.scheme isEqualToString:@"https"]) {
                    if (resultURL.host) {
                        resultURL = [NSURL URLWithString:[NSString stringWithFormat:@"imeituan://www.meituan.com/web?url=%@",
                                                             [resultURL.absoluteString cipf_URLEncodedString]]];
                    }
                }
                completion(YES, resultURL);
            } else {
                completion(NO, nil);
            }
        } else {
            if (self.codeScanFailed) {
                self.codeScanFailed();
            } else {
                completion(NO, nil);
            }
        }
    } completion:^{
        UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
        pasteboard.string = result;
        [[SPKToastCenter defaultCenter] toastWithMessage:@"复制成功"];
    }];
}

#pragma mark - Protocol
- (void)showScanVC {
    self.view.hidden = NO;
}

- (void)hideScanVC {
    self.view.hidden = YES;
}

- (void)didReceiveUIApplicationDidEnterBackgroundNotification:(NSNotification *)notification
{
    UIViewController *currentViewController = [NSObject spk_getTopViewController];
    if (currentViewController == self) {
        [self.session stopRunning];
    }
}

- (void)didReceiveUIApplicationDidBecomeActiveNotification:(NSNotification *)notification {
    UIViewController *currentViewController = [NSObject spk_getTopViewController];
    if (currentViewController == self) {
        [self.session startRunning];
    }
}

@end
