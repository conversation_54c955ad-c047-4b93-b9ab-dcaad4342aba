//
//  PPHostSwitcher.m
//  ipaymentapp
//
//  Created by <PERSON><PERSON><PERSON> on 2018/8/23.
//  Copyright © 2018年 Meituan.com. All rights reserved.
//

#import "PPHostSwitcher.h"
#import "SAKOneClickEvnModule.h"
#import "SAKOneClickDataManager.h"
#import "PPContentInfo.h"
#import "MTNavigationController.h"
#import "SAKOneClickURLSwitchModel.h"
#import "PPService.h"
#import <SAKHorn/SAKHorn.h>

@interface PPHostSwitcher()

@property (nonatomic, strong) NSDictionary *params;

@end

@implementation PPHostSwitcher

+ (instancetype)sharedHostSwitcher
{
    static dispatch_once_t onceToken;
    static PPHostSwitcher *hostSwitcher;
    dispatch_once(&onceToken, ^{
        hostSwitcher = [[PPHostSwitcher alloc] init];
    });
    return hostSwitcher;
}

- (void)initDataWithContentInfo:(NSArray *)contentInfos
{
    self.contentArray = contentInfos;
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(receiveUpdateNotification:)
                                                 name:SAKModelSwitchItemUpdateNotification
                                               object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(receiveFinishNotification:)
                                                 name:SAKModelSwitchItemFinishNotification
                                               object:nil];
}

- (void)receiveUpdateNotification:(NSNotification *)sender{
    [[SAKOneClickDataManager sharedManager] defaultEnvironmentId];
    NSLog(@"%@",sender.userInfo);
    [self setupWithParams:self.params];
    // 修改Horn开关进入（我的-余额）
    [self updatebalanceHornSwitch];
}
- (void)receiveFinishNotification:(NSNotification *)sender{
    [[SAKOneClickDataManager sharedManager] defaultEnvironmentId];
    NSLog(@"%@",sender.userInfo);
}

- (void)setupWithParams:(NSDictionary *)paramDic;
{
    self.params = [paramDic copy];
    BOOL isUseCreditPay = [[self.params valueForKey:@"isUseCreditPay"] boolValue];
    //线上：Prod：1000，stage：1001，greypay：2271，支付ST环境：2512，支付Prod环境：3562
    //线下：QA：2270，Test：1002，Dev：1003
    NSInteger envId = [[SAKOneClickDataManager sharedManager] defaultEnvironmentId];
    PPContentInfo *contentInfo = [self findPartnerKeyContentInfo];
    PPContentInfo *merchantNo = [self findIphPayMerchantNoContentInfo];
    switch (envId) {
        case 1000:
        case 1001:
        case 2271:
        case 2512:
        case 3562:
            contentInfo.value = isUseCreditPay ? kOnlineCreditPartnerKey : contentInfo.value;
            contentInfo.defaultValue = kOnlinePartnerKey;
            
            merchantNo.value = isUseCreditPay ? kOnlineCreditMerchantNO : merchantNo.value;
            merchantNo.defaultValue = kOnlineMerchantNO;
            break;
    
        default:
            // 因为默认进入配置环境界面也会触发SAKModelSwitchItemUpdateNotification消息，此时就会进入default判断，这里会将商户号信息置为默认值，所以需要删除相关逻辑
            break;
    }
    
    // 充值单商户号
    if([self isRechargeType]) {
        NSInteger envId = [[SAKOneClickDataManager sharedManager] defaultEnvironmentId];
        PPContentInfo *contentInfo = [self findPartnerKeyContentInfo];
        PPContentInfo *merchantNo = [self findIphPayMerchantNoContentInfo];
        switch (envId) {
            case 1003:
            case 1004:
            case 2622:
                contentInfo.value = kOfflineReChargeTypePartnerKey;
                merchantNo.value = kOfflineReChargeTypeMerchantNO;
                break;
            case 1000:
            case 1001:
            case 2271:
            case 2512:
                contentInfo.value = kOnlineReChargeTypePartnerKey;
                merchantNo.value = kOnlineReChargeTypeMerchantNO;
                break;
                
            default:
                break;
        }
    }
    
    //DCEP切换
    if([self isSwitchDCEP]) {
        NSInteger envId = [[SAKOneClickDataManager sharedManager] defaultEnvironmentId];
        PPContentInfo *contentInfo = [self findPartnerKeyContentInfo];
        PPContentInfo *merchantNo = [self findIphPayMerchantNoContentInfo];
        switch (envId) {
            case 1003:
            case 1004:
            case 2622:
                contentInfo.value = kOfflineReChargeTypePartnerKey;
                merchantNo.value = kOfflineReChargeTypeMerchantNO;
                break;
            case 1000:
            case 1001:
            case 2271:
            case 2512:
            case 3562:
                merchantNo.value = kOnlineDCEPMerchantNO;
                break;
                
            default:
                break;
        }
    }
}

- (PPContentInfo *)findPartnerKeyContentInfo
{
    __block PPContentInfo *partnerKeyContentInfo = nil;
    
    [self.contentArray enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        PPContentInfo *contentInfo = (PPContentInfo *)obj;
        if ([contentInfo.identifier isEqualToString:@"partner_key"]) {
            partnerKeyContentInfo = contentInfo;
            *stop = YES;
        }
    }];
    
    return partnerKeyContentInfo;
}

- (PPContentInfo *)findIphPayMerchantNoContentInfo
{
    __block PPContentInfo *merchantNoContentInfo = nil;
    
    [self.contentArray enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        PPContentInfo *contentInfo = (PPContentInfo *)obj;
        if ([contentInfo.identifier isEqualToString:@"iph_pay_merchant_no"]) {
            merchantNoContentInfo = contentInfo;
            *stop = YES;
        }
    }];
    
    return merchantNoContentInfo;
}

// 是否是充值单
- (BOOL)isRechargeType
{
    __block BOOL result = NO;
    [self.contentArray enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        PPContentInfo *contentInfo = (PPContentInfo *)obj;
        if ([contentInfo.identifier isEqualToString:@"recharge_type"]) {
            result = YES;
            *stop = YES;
        }
    }];
    
    return result;
}

//是否为DCEP商户号切换
- (BOOL)isSwitchDCEP
{
    __block BOOL result = NO;
    [self.contentArray enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        PPContentInfo *contentInfo = (PPContentInfo *)obj;
        if ([contentInfo.identifier isEqualToString:@"switch_DCEP"]) {
            result = YES;
            *stop = YES;
        }

    }];
    
    return result;
}

- (void)updatebalanceHornSwitch
{
    //线上：Prod：1000，stage：1001，greypay：2271，支付ST环境：2512，支付Prod环境：3562
    //线下：QA：2270，Test：1002，Dev：1003
    NSInteger envId = [[SAKOneClickDataManager sharedManager] defaultEnvironmentId];
    BOOL isTestEnv = YES;
    switch (envId) {
        case 1000:
        case 1001:
        case 2271:
        case 2512:
        case 3562:
            isTestEnv = NO;
            break;
            
        case 2270:
        case 1002:
        case 1003:
            isTestEnv = YES;
            break;
            
        default:
            break;
    }
    [SAKHorn setEnableDebug:isTestEnv forType:@"offline_native_page"];
}
@end
