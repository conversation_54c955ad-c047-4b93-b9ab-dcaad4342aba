//
//  PPTitansAdapter.m
//  ipaymentapp
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2017/2/14.
//  Copyright © 2017年 Meituan.com. All rights reserved.
//

#import "PPTitansAdapter.h"
#import "PPTitansNamespace.h"

#import "TTWebViewStyleManager.h"
#import "SPKUIKitMacros.h"
#import "TKAlertCenter.h"
#import "TTWebViewConfig.h"
#import "SAKNetworkManager.h"
#import "MRDLocationManager.h"
#import "SAKEnvironment.h"
#import "SAKStatistics.h"
#import "SAKUserService.h"
#import "SAKURLChecker.h"
#import "SAKPortal.h"
#import "SAKWindFingerprintGenerator.h"
#import "TTWebViewBaseController.h"
#import "NSMutableDictionary+CIPSafe.h"
#import "NSDictionary+CIPFoundation.h"
#import "PPQRCodeScanViewController.h"
#import "TTWebViewURLProtocol.h"
//#import "TTServiceSlave.h"

@implementation PPTitansAdapter
+ (void)setup
{
    //register namespace
    PPTitansNamespace *namespace = [[PPTitansNamespace alloc] init];
    [TTWebViewJSBridge registerNamespaceWithName:@"papp" andInstance:namespace];
    
    //style
    [TTWebViewStyleManager sharedManager].titleColor = HEXCOLOR(0x1D1C1E);
    [TTWebViewStyleManager sharedManager].subtitleColor = HEXCOLOR(0x999999);
    [TTWebViewStyleManager sharedManager].titleFont = Font(18.0);
    [TTWebViewStyleManager sharedManager].subtitleFont = Font(11.0);
    [TTWebViewStyleManager sharedManager].backgroundColor = HEXCOLOR(0xF3F3F6);
    [TTWebViewStyleManager sharedManager].barTextColor = kSPKBaseThemeColor;
    [TTWebViewStyleManager sharedManager].sourceTextColor = HEXCOLOR(0x999999);
    [TTWebViewStyleManager sharedManager].sourceTextFont = Font(13.0);
    [TTWebViewStyleManager sharedManager].progressBarColor = kSPKBaseThemeColor;
    [TTWebViewStyleManager sharedManager].barTextFont = Font(15.0);
    [TTWebViewStyleManager sharedManager].otherTitleFont = Font(15.0);
    [TTWebViewStyleManager sharedManager].otherTitleColor = [UIColor blackColor];
    [TTWebViewStyleManager sharedManager].showSplashActionBlock = ^(NSString *content, UIViewController *viewController) {
        [[TKAlertCenter defaultCenter] postAlertWithMessage:content];
    };
    
    //UA Cookies
    [TTWebViewConfig shared].appName = @"meituangroup";
    [TTWebViewConfig shared].version = [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"];
    
    [[TTWebViewConfig shared] setGetCityId:^NSInteger{
        return [SAKEnvironment environment].city.cityID;
    }];
    
    [[TTWebViewConfig shared] setGetCityName:^NSString *{
        return [SAKEnvironment environment].city.cityName;
    }];
    
    [[TTWebViewConfig shared] setGetLocatedCityId:^NSInteger {
        return [[MRDLocationManager defaultManager].lastPlaceMark.cityID integerValue];
    }];
    
    [[TTWebViewConfig shared] setGetLocatedCityName:^NSString *{
        return [MRDLocationManager defaultManager].lastPlaceMark.cityName;
    }];
    
    [[TTWebViewConfig shared] setGetLocation:^CLLocation *{
        return [MRDLocationManager defaultManager].lastLocation;
    }];
    
    [[TTWebViewConfig shared] setGetToken:^NSString *{
        return [SAKEnvironment environment].user.token;
    }];
    
    [[TTWebViewConfig shared] setIsLogin:^BOOL{
        return [SAKEnvironment environment].user ? YES : NO;
    }];
    
    [[TTWebViewConfig shared] setGetUserId:^NSNumber *{
        return [SAKEnvironment environment].user.userID;
    }];
    
    [[TTWebViewConfig shared] setGet_utm_medium:^NSString *{
        return @"iphone";
    }];
    
    [[TTWebViewConfig shared] setGet_utm_source:^NSString *{
        return [[SAKEnvironment environment] appInfo].channel;
    }];
    
    [[TTWebViewConfig shared] setGet_utm_content:^NSString *{
        return [[SAKEnvironment environment] UUID];
    }];
    
    [[TTWebViewConfig shared] setGet_utm_term:^NSString *{
        return [[[SAKEnvironment environment] appInfo] version];
    }];
    
    [[TTWebViewConfig shared] setGet_utm_campaign:^NSString *{
        return [[SAKEnvironment environment] commonParameter][MTUTMParamCampainKey];
    }];
    
    [TTWebViewConfig shared].getUUID = ^NSString *() {
        return [[SAKEnvironment environment] UUID];
    };
    
//    [TTWebViewConfig shared].redirectURL = @"https://npay.meituan.com/resource/ptp/pages/conch-error-page.html";
    
    [TTWebViewConfig shared].prefixURL = @"meituanpayment://www.meituan.com/web";
    
    [[TTWebViewConfig shared] setGetNetworkType:^NSString *{
        NSString *netType = nil;
        SAKNetworkManager *networkMananger = [SAKNetworkManager sharedManager];
        if ([networkMananger networkStatus] == MTNetworkStatusUnavailable) {
            netType = @"none";
        } else if ([networkMananger networkStatus] == MTNetworkStatusWiFi) {
            netType = @"wifi";
        } else {
            switch ([networkMananger mobileNetworkStatus]) {
                case SAKMobileNetworkStatus2G: {
                    netType = @"2g";
                    break;
                }
                case SAKMobileNetworkStatus3G: {
                    netType = @"3g";
                    break;
                }
                case SAKMobileNetworkStatus4G: {
                    netType = @"4g";
                    break;
                }
                default: {
                    netType = @"unknown";
                    break;
                }
            }
        }
        return netType;
    }];
    
    [[TTBridgeManager shared] setCloseWindowActionBlock:^(TTParamBaseCloseWindow *parameter, UIViewController *viewController) {
        if (parameter.isModal.boolValue) {
            [viewController dismissViewControllerAnimated:YES completion:nil];
        } else {
            [viewController.navigationController popViewControllerAnimated:YES];
        }
    }];
    
#if DEBUG || TEST
    [TTBridgeManager shared].isDebugMode = YES;
#endif
    
//    [[TTBridgeManager shared] setAnalyticsActionBlock:^(TTParamBaseAnalyticsTag *parameter){
//        [SAKStatistics setTag:[parameter.value tt_objectFromJSONString]?:@{}
//                       forKey:parameter.key];
//    }];
    
    [[TTBridgeManager shared] setAccountLoginActionBlock:^(UIViewController *viewController, TTJSBridgeCallbackBlock callback, BOOL forceJump) {
        // TODO zzy login
    }];
    
//    [[TTBridgeManager shared] setAccountLogoutActionBlock:^(TTJSBridgeCallbackBlock callback, NSInteger reason) {
//        // TODO zzy login
//    }];
    
    [[TTBridgeManager shared] setFingerprintActionBlock:^() {
        NSDictionary *corpse = [[SAKWindFingerprintGenerator sharedGenerator] requestSyncCorpse];
        return corpse[@"fingerprint"] ?: @"";
    }];
    
    [[TTBridgeManager shared] setOpenSchemeActionBlock:^(TTParamBaseOpenScheme *parameter, UIViewController *viewController) {
        NSURL *URL = [[NSURL alloc] initWithString:parameter.url];
        [SAKPortal transferFromViewController:nil toURL:URL completion:^(UIViewController<SAKPortalable> * _Nullable viewController, NSError * _Nullable error) {
        }];
    }];
    
    [[TTBridgeManager shared] setJumpToSchemeActionBlock:^(TTParamBaseJumpToScheme *parameter, UIViewController *viewController) {
        NSURL *URL = [[NSURL alloc] initWithString:parameter.url];
        [viewController.navigationController popViewControllerAnimated:NO];
        [SAKPortal transferFromViewController:nil toURL:URL completion:^(UIViewController<SAKPortalable> * _Nullable viewController, NSError * _Nullable error) {
        }];
    }];
    
    [[TTBridgeManager shared] setSetResultActionBlock:^(TTParamBaseSetResult *param, UIViewController *viewController){
        [viewController setPortalResultCode:param.resultCode content:param.resultData];
    }];
    
    [[TTBridgeManager shared] setScanQRCodeActionBlock:^(TTParamBaseScanQRCode *parameter, TTJSBridgeCallbackBlock callback) {
        NSURL *URL = [[NSURL alloc ] initWithString:@"imeituan://www.meituan.com/scanQRCode"];
        [SAKPortal transferFromViewController:nil toURL:URL completion:^(UIViewController<SAKPortalable> * _Nullable viewController, NSError * _Nullable error) {
            if (callback == nil) {
                return;
            }
            if (error) {
                callback(NO, nil, nil);
                return;
            }
            
            if ([viewController isKindOfClass:[PPQRCodeScanViewController class]]) {
                PPQRCodeScanViewController *scanViewController = (PPQRCodeScanViewController *)viewController;
                if (parameter.needResult == 1) {
                    @weakify(scanViewController);
                    scanViewController.codeScanSucceed = ^(NSURL *resultURL) {
                        @strongify(scanViewController);
                        [scanViewController dismissViewControllerAnimated:YES completion:^{
                            TTModelBaseScanQRCode *result = [TTModelBaseScanQRCode new];
                            result.scanResult = [resultURL absoluteString] ? : @"";
                            callback(YES, nil, result);
                        }];
                    };
                    scanViewController.codeScanFailed = ^ {
                        callback(NO, nil, nil);
                    };
                }
            } else {
                callback(NO, nil, nil);
            }
        }];
    }];
    [TTWebViewURLProtocol setup];
    
//    [TTServiceSlave setup]; // 离线库初始化
    
    [[TTBridgeManager shared] setViewWillAppearBlock:^(TTWebViewBaseController *viewController, BOOL animated) {
        if (!viewController.isNested) { // 内嵌模式的 webview 啥都不做
            /** 这里的 animated 设置为NO
             *  setViewWillDisappearBlock 中透传animated 满足了以下场景的导航栏过度效果：
             *  1: 不隐藏导航栏的A页面跳转到隐藏了导航栏的B页面后 通过手势滑动返回 显示正常
             *  2: 隐藏导航栏的A页面跳转到隐藏了导航栏的B页面后  通过手势返回 显示正常
             */
            [viewController.navigationController setNavigationBarHidden:viewController.needHideNavigationBar animated:NO];
        }
    }];
    
    [[TTBridgeManager shared] setViewWillDisappearBlock:^(TTWebViewBaseController *viewController, BOOL animated) {
        if (!viewController.isNested) { // 内嵌模式的 webview 啥都不做
            [viewController.navigationController setNavigationBarHidden:NO animated:animated];
        }
    }];
    
    [[TTWebViewConfig shared] setAppID:@"92"];
}

#pragma mark - private function

+ (NSString *)webViewUrlString:(TTWebViewBaseController *)webViewController
{
    NSString *urlString = @"";
    if (webViewController && [webViewController isKindOfClass:[TTWebViewBaseController class]]) {
        NSURL *url = ((TTWebViewBaseController *)webViewController).URL;
        if ([url absoluteString].length > 0) {
            return [url absoluteString];
        }
    }
    return urlString;
}
@end
