//
//  SPKTitleInputTableViewCell.m
//  SAKPaymentKit
//
//  Created by <PERSON> on 2018/1/5.
//

#import "SPKTitleInputTableViewCell.h"
#import "MTTextField.h"
#import "Masonry.h"
#import "SPKUIKitMacros.h"

@implementation SPKTitleInputTableViewCell

- (id)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.accessoryType = UITableViewCellAccessoryNone;
        _titleLabel = ({
            UILabel *label = [[UILabel alloc] init];
            label.backgroundColor = [UIColor clearColor];
            label.textColor = kSPKTitleLabelTextColor;
            label.font = Font(15);
            label;
        });
        [self.contentView addSubview:_titleLabel];
        
        _textField = ({
            MTTextField *field = [[MTTextField alloc] init];
            field.mtStyle = MTTextFieldStyleCustom;
            field.textColor = HEXCOLOR(0x4A494B);
            field.font = Font(15);
            [field setValue:HEXCOLOR(0xC6C6C7) forKeyPath:@"_placeholderLabel.textColor"];
            field;
        });
        [self.contentView addSubview:_textField];
    }
    return self;
}

+ (BOOL)requiresConstraintBasedLayout
{
    return YES;
}

- (void)updateConstraints
{
    [self.titleLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).with.offset(15);
        make.centerY.equalTo(self.contentView);
        make.height.mas_equalTo(21);
        make.width.mas_equalTo(60);
    }];
    
    [self.textField mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.titleLabel.mas_right).with.offset(16);
        make.right.equalTo(self.contentView.mas_right).with.offset(-15);
        make.centerY.equalTo(self.contentView);
        make.height.mas_equalTo(21);
    }];
    
    [super updateConstraints];
}

@end

