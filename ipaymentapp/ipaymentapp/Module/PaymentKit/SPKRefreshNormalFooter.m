//
//  SPKRefreshNormalFooter.m
//  SAKPaymentKit
//
//  Created by <PERSON> on 2018/1/11.
//

#import "SPKRefreshNormalFooter.h"
#import "SPKUIKitMacros.h"
#import "Masonry.h"

@interface SPKRefreshNormalFooter ()

@property (nonatomic, strong) UIActivityIndicatorView *loadingView;
/** 所有状态对应的文字 */
@property (nonatomic, strong) NSMutableDictionary *stateTitles;

@end

@implementation SPKRefreshNormalFooter

- (NSMutableDictionary *)stateTitles
{
    if (!_stateTitles) {
        self.stateTitles = [NSMutableDictionary dictionary];
    }
    return _stateTitles;
}

#pragma mark - 公共方法
- (void)setTitle:(NSString *)title forState:(SPKRefreshState)state
{
    if (title == nil) return;
    self.stateTitles[@(state)] = title;
    self.stateLabel.text = self.stateTitles[@(self.state)];
}

#pragma mark - 私有方法
- (void)stateLabelClick
{
    if (self.state == SPKRefreshStateIdle) {
        [self beginRefreshing];
    }
}

- (UIActivityIndicatorView *)loadingView
{
    if (!_loadingView) {
        UIActivityIndicatorView *loadingView = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:self.activityIndicatorViewStyle];
        loadingView.hidesWhenStopped = YES;
        [self addSubview:_loadingView = loadingView];
    }
    return _loadingView;
}

- (void)setActivityIndicatorViewStyle:(UIActivityIndicatorViewStyle)activityIndicatorViewStyle
{
    _activityIndicatorViewStyle = activityIndicatorViewStyle;
    
    self.loadingView = nil;
    [self setNeedsLayout];
}

- (void)prepare
{
    [super prepare];
    
    _stateLabel = [[UILabel alloc] init];
    _stateLabel.font = Font(14);
    _stateLabel.textColor = kSPKLabelTextDisableColor;
    [self addSubview:_stateLabel];
    
    self.activityIndicatorViewStyle = UIActivityIndicatorViewStyleGray;
    self.refreshingTitleHidden = YES;
    _stateLabel.userInteractionEnabled = YES;
    [_stateLabel addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(stateLabelClick)]];
}

- (void)placeSubviews
{
    [super placeSubviews];
    
    [self.stateLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self);
    }];
    if (self.refreshingTitleHidden) {
        [self.loadingView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.equalTo(self);
            make.height.width.mas_equalTo(30);
        }];
    } else {
        [self.loadingView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.height.width.mas_equalTo(30);
            make.centerY.equalTo(self);
            make.right.equalTo(self.stateLabel.mas_left).with.offset(-6);
        }];
    }
}


- (void)setState:(SPKRefreshState)state
{
    SPKRefreshCheckState;
    
    if (self.refreshingTitleHidden && state == SPKRefreshStateRefreshing) {
        self.stateLabel.text = nil;
    } else {
        self.stateLabel.text = self.stateTitles[@(state)];
    }
    // 根据状态做事情
    if (state == SPKRefreshStateNoMoreData || state == SPKRefreshStateIdle) {
        [self.loadingView stopAnimating];
    } else if (state == SPKRefreshStateRefreshing) {
        [self.loadingView startAnimating];
    } else {
        //do nothing
    }
    
}

@end
