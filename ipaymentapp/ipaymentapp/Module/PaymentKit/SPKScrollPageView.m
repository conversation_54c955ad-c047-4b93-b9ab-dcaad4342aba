//
//  SPKScrollPageView.m
//  AFNetworking
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2018/1/11.
//

#import "SPKScrollPageView.h"
#import <Masonry/Masonry.h>
#import "SAKUIKitMacros.h"
#import "NSObject+SPKReturnSelf.h"
#import "SPKFoundationMacros.h"

static const CGFloat kSPKPageViewMargin = 15.0;
static const CGFloat kSPKPageButtonMargin = 22.0; // 按钮间距
static const CGFloat kSPKPageTitleDefaultFont = 14.0;

@interface SPKScrollPageButton : UIControl

@property (nonatomic, strong) UIView *highlightLine;
@property (nonatomic, strong) UILabel *titleLabel;

@end

@implementation SPKScrollPageButton

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        _titleLabel = ({
            UILabel *label = [[UILabel alloc] init];
            label.backgroundColor = [UIColor clearColor];
            label.font = Font(kSPKPageTitleDefaultFont);
            label.textColor = HEXCOLOR(0x8E8D8E);
            label.textAlignment = NSTextAlignmentCenter;
            label.numberOfLines = 1;
            label;
        });
        [self addSubview:_titleLabel];
        
        _highlightLine = [[UIView alloc] init];
        _highlightLine.backgroundColor = HEXCOLOR(0x31BCAD);
        _highlightLine.hidden = YES;
        [self addSubview:_highlightLine];
    }
    return self;
}

- (void)setSelected:(BOOL)selected
{
    _highlightLine.hidden = !selected;
    if (selected) {
        _titleLabel.textColor = HEXCOLOR(0x31BCAD);
    } else {
        _titleLabel.textColor = HEXCOLOR(0x8E8D8E);
    }
}

+ (BOOL)requiresConstraintBasedLayout
{
    return YES;
}

- (void)updateConstraints
{
    [self.highlightLine mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.equalTo(self);
        make.height.mas_equalTo(2);
    }];
    
    [self.titleLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.bottom.equalTo(self);
    }];
    
    [super updateConstraints];
}

@end

@interface SPKScrollPageView ()

@property (nonatomic, strong) UIScrollView *scrollView;
@property (nonatomic, strong) UIView *seperateLine;
@property (nonatomic, copy) NSArray *labelArray;

@property (nonatomic, strong) UIButton *selectedButton;

@end

@implementation SPKScrollPageView

- (instancetype)initWithTitles:(NSArray *)titles
{
    self = [super initWithFrame:CGRectZero];
    if (self) {
        self.backgroundColor = [UIColor whiteColor];
        
        _scrollView = ({
            UIScrollView *scrollView = [[UIScrollView alloc] init];
            scrollView.showsHorizontalScrollIndicator = NO;
            scrollView.backgroundColor = [UIColor whiteColor];
            scrollView;
        });
        [self addSubview:_scrollView];
        
        _seperateLine = ({
            UIView *view = [[UIView alloc] init];
            view.backgroundColor = HEXCOLOR(0xE9E8EA);
            view;
        });
        [self addSubview:_seperateLine];
        
        [self createLabelsWithTitles:titles];
    }
    return self;
}

- (void)createLabelsWithTitles:(NSArray *)titles
{
    if (titles.count > 0) {
        NSMutableArray *labelArray = [[NSMutableArray alloc] init];
        [titles enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            NSString *title = [obj spk_as:[NSString class]];
            SPKScrollPageButton *button = [[SPKScrollPageButton alloc] init];
            button.titleLabel.text = title;
            [button addTarget:self action:@selector(changedLabel:) forControlEvents:UIControlEventTouchUpInside];
            
            [self.scrollView addSubview:button];
            [labelArray addObject:button];
        }];
        
        if (self.defaultIndex >= 0 && self.defaultIndex < labelArray.count ) {
            self.selectedButton = [labelArray objectAtIndex:self.defaultIndex] OR labelArray.firstObject;
            self.selectedButton.selected = YES;
            self.selectedButton.titleLabel.font = BoldFont(kSPKPageTitleDefaultFont);
        }
        
        self.labelArray = [labelArray copy];
    }
}

- (void)changedLabel:(UIButton *)button
{
    if (![button isEqual:self.selectedButton]) {
        self.selectedButton.selected = NO;
        self.selectedButton.titleLabel.font = Font(kSPKPageTitleDefaultFont);
        
        button.selected = YES;
        button.titleLabel.font = BoldFont(kSPKPageTitleDefaultFont);
        self.selectedButton = button;
        
        if (self.didClickedTitleBlock) {
            self.didClickedTitleBlock(button.titleLabel.text);
        }
    }
}

+ (BOOL)requiresConstraintBasedLayout
{
    return YES;
}

- (void)updateConstraints
{
    [self.scrollView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.equalTo(self);
        make.left.equalTo(self).offset(kSPKPageViewMargin);
        make.right.equalTo(self).offset(-kSPKPageViewMargin);
    }];
    
    UIView *previousButton = nil;
    for (int index = 0; index < self.labelArray.count; index ++) {
        UIButton *button = self.labelArray[index];
        [button mas_updateConstraints:^(MASConstraintMaker *make) {
            if (previousButton) {
                make.left.equalTo(previousButton.mas_right).offset(kSPKPageButtonMargin);
            } else {
                make.left.equalTo(self.scrollView);
            }
            make.top.height.equalTo(self.scrollView);
            
            NSString *title = button.titleLabel.text;
            CGFloat width = [title boundingRectWithSize:CGSizeMake(CGFLOAT_MAX, 16)
                                                options:NSStringDrawingUsesLineFragmentOrigin | NSStringDrawingUsesFontLeading
                                             attributes:@{NSFontAttributeName : button.titleLabel.font}
                                                context:nil].size.width;
            make.width.mas_equalTo(ceil(width));
            
            if (index == self.labelArray.count - 1) {
                make.right.equalTo(self.scrollView).offset(-kSPKPageViewMargin);
            }
        }];
        
        previousButton = button;
    }
    
    [self.seperateLine mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.equalTo(self);
        make.height.mas_equalTo(0.5);
    }];
    
    [super updateConstraints];
}

@end
