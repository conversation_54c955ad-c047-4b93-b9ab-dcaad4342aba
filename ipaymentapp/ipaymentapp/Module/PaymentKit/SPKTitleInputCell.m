//
//  SPKTitleInputCell.m
//  Pods
//
//  Created by <PERSON> on 2018/1/4.
//

#import "SPKTitleInputCell.h"
#import "MTTextField.h"
#import "Masonry.h"
#import "SPKUIKitMacros.h"

@implementation SPKTitleInputCell

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        _textField = ({
            MTTextField *field = [[MTTextField alloc] init];
            field.mtStyle = MTTextFieldStyleCustom;
            field.textColor = HEXCOLOR(0x4A494B);
            field.font = Font(15);
            [field setValue:HEXCOLOR(0xC6C6C7) forKeyPath:@"_placeholderLabel.textColor"];
            field;
        });
        [self addSubview:_textField];
    }
    return self;
}

+ (BOOL)requiresConstraintBasedLayout
{
    return YES;
}

- (void)updateConstraints
{
    [self.titleLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(60).priorityHigh();
    }];
    [self.textField mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.titleLabel.mas_right).with.offset(16);
        make.right.equalTo(self.mas_right).with.offset(-15);
        make.centerY.equalTo(self);
        make.height.mas_equalTo(21);
    }];
    [super updateConstraints];
}

@end
