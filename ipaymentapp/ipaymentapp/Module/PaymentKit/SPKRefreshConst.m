//
//  SPKRefreshConst.m
//  SAKPaymentKit
//
//  Created by <PERSON> on 2018/1/11.
//

#import <UIKit/UIKit.h>

const CGFloat SPKRefreshLabelLeftInset = 25;
const CGFloat SPKRefreshHeaderHeight = 54.0;
const CGFloat SPKRefreshFooterHeight = 44.0;
const CGFloat SPKRefreshFastAnimationDuration = 0.25;
const CGFloat SPKRefreshSlowAnimationDuration = 0.4;

NSString *const SPKRefreshKeyPathContentOffset = @"contentOffset";
NSString *const SPKRefreshKeyPathContentInset = @"contentInset";
NSString *const SPKRefreshKeyPathContentSize = @"contentSize";
NSString *const SPKRefreshKeyPathPanState = @"state";

NSString *const SPKRefreshHeaderLastUpdatedTimeKey = @"SPKRefreshHeaderLastUpdatedTimeKey";

NSString *const SPKRefreshHeaderIdleText = @"SPKRefreshHeaderIdleText";
NSString *const SPKRefreshHeaderPullingText = @"SPKRefreshHeaderPullingText";
NSString *const SPKRefreshHeaderRefreshingText = @"SPKRefreshHeaderRefreshingText";

NSString *const SPKRefreshAutoFooterIdleText = @"SPKRefreshAutoFooterIdleText";
NSString *const SPKRefreshAutoFooterRefreshingText = @"SPKRefreshAutoFooterRefreshingText";
NSString *const SPKRefreshAutoFooterNoMoreDataText = @"SPKRefreshAutoFooterNoMoreDataText";

