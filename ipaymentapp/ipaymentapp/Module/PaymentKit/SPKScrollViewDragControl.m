//
//  METScrollViewDragControl.m
//  imeituan
//
//  Created by zjs on 14/9/10.
//  Copyright (c) 2014年 meituan.com. All rights reserved.
//

#import "SPKScrollViewDragControl.h"
#import <ReactiveCocoa/ReactiveCocoa.h>
#import "UIViewAdditions.h"
#import <libextobjc/extobjc.h>
#import "SPKUIKitMacros.h"
#import "SPKFoundationMacros.h"

static const CGFloat METScrollViewDragControl_threshold = 60;
static const CGFloat METScrollViewDragControl_animatedDuration = 0.3;


@implementation SPKScrollViewDragControl {
    __weak id _target;
    SEL _selector;
    UIEdgeInsets _contentInset;
    //refresh
    UIImageView *_pullImageView;
    
    //load more
    UILabel *_statusLabel;
    UIActivityIndicatorView *_activeView;
}

- (id)initWithFrame:(CGRect)frame type:(SPKScrollViewDragControlType)type;
{
    self = [super initWithFrame:frame];
    if (self) {
        self.autoresizingMask = UIViewAutoresizingFlexibleWidth;
        self.threshold = METScrollViewDragControl_threshold;
        _refreshViewTopInset = kSystemVersionGreaterThanOrEqualiOS7 ? kSPKNavigationBarHeight : 0;
        [self setType:type];
        @weakify(self);
        [RACObserve(self, scrollView.contentOffset) subscribeNext:^(NSNumber *point) {
            @strongify(self);
            if (!self.scrollView) {
                return;
            }
            if (self.status == SPKScrollViewDragControlStatusDisabled) {
                return;
            } else if (self.status == SPKScrollViewDragControlStatusLoading) {
                return;
            } else if (self.scrollView.isDragging) {
                if ([self isTriggerAvailable]) {
                    self.status = SPKScrollViewDragControlStatusPullingOver;
                } else {
                    self.status = SPKScrollViewDragControlStatusPulling;
                }
            } else if (!self.scrollView.isDragging) {
                if (self.status == SPKScrollViewDragControlStatusPullingOver) {
                    [self setStatus:SPKScrollViewDragControlStatusLoading animated:YES];
                } else {
                    [self setStatus:SPKScrollViewDragControlStatusNormal animated:YES];
                }
            }
        }];
    }
    return self;
}

- (void)setRefreshViewTopInset:(CGFloat)refreshViewTopInset
{
    if (kSystemVersionGreaterThanOrEqualiOS7) {
        _refreshViewTopInset = refreshViewTopInset;
    }
}

- (void)setType:(SPKScrollViewDragControlType)type
{
    _type = type;
    if (_type == SPKScrollViewDragControlTypeLoadMore) {
        
        self.userInteractionEnabled = YES;
        UITapGestureRecognizer *gesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(didTapSelf:)];
        gesture.cancelsTouchesInView = NO;
        [self addGestureRecognizer:gesture];
        
        @weakify(self);
        [RACObserve(self, scrollView.contentSize) subscribeNext:^(NSValue *value) {
            @strongify(self);
            CGSize size = [value CGSizeValue];
            self.top = size.height;
        }];
    }
}

- (void)setStatus:(SPKScrollViewDragControlStatus)status
{
    [self setStatus:status animated:NO];
}

- (void)setStatus:(SPKScrollViewDragControlStatus)status animated:(BOOL)animated
{
    if (_status != status) {
        SPKScrollViewDragControlStatus lastStatus = _status;
        _status = status;
        
        if (lastStatus == SPKScrollViewDragControlStatusDisabled) {
            [self __scrollToEnableAnimated:animated];
        }
        
        switch (_status) {
            case SPKScrollViewDragControlStatusNormal: {
                if (lastStatus == SPKScrollViewDragControlStatusPulling || lastStatus == SPKScrollViewDragControlStatusPullingOver) {
                    [self __scrollToNormalFromPullStatusAnimated:animated];
                } else if (lastStatus == SPKScrollViewDragControlStatusLoading) {
                    [self __scrollToNormalFromLoadingStatusAnimated:animated];
                }else {
                    [self __scrollToNormalAnimated:animated];
                }
            }
                break;
            case SPKScrollViewDragControlStatusPulling: {
                [self __scrollToPullingAnimated:animated];
            }
                break;
            case SPKScrollViewDragControlStatusPullingOver: {
                [self __scrollToPullingOverAnimated:animated];
            }
                break;
            case SPKScrollViewDragControlStatusLoading: {
                [self __scrollToLoadingAnimated:animated];
            }
                break;
            case SPKScrollViewDragControlStatusDisabled: {
                [self __scrollToDisabledAnimated:animated];
            }
                break;
            default:
                break;
        }
    }
}

- (void)setEnable:(BOOL)enable
{
    if (!enable) {
        [self setStatus:SPKScrollViewDragControlStatusDisabled];
    } else {
        [self setStatus:SPKScrollViewDragControlStatusNormal];
    }
}

- (void)reset
{
    if (self.type == SPKScrollViewDragControlTypeLoadMore) {
        UIEdgeInsets contentInset = _scrollView.contentInset;
        contentInset.bottom = _contentInset.bottom - self.height;
        _scrollView.contentInset = contentInset;
    }
    [self removeFromSuperview];
    _scrollView = nil;
    _contentInset = UIEdgeInsetsZero;
    _status = SPKScrollViewDragControlStatusNormal;
    [self __scrollToNormalAnimated:NO];
}

- (void)setScrollView:(UIScrollView *)scrollView
{
    if (_scrollView != scrollView) {
        [self reset];
        _scrollView = scrollView;
        if (self.type == SPKScrollViewDragControlTypeLoadMore) {
            UIEdgeInsets contentInset = scrollView.contentInset;
            contentInset.bottom += self.height;
            _scrollView.contentInset = contentInset;
            _contentInset = contentInset;
        }
        _contentInset = _scrollView.contentInset;
        [_scrollView addSubview:self];
        if (self.type == SPKScrollViewDragControlTypeLoadMore) {
            self.width = _scrollView.width;
            self.top = _scrollView.contentSize.height;
        } else {
            self.bottom = 0;
        }
    }
}

- (BOOL)isTriggerAvailable
{
    if (self.type == SPKScrollViewDragControlTypeRefresh) {
        BOOL flag = (-_scrollView.contentInset.top - _scrollView.contentOffset.y >= self.threshold);
        return flag;
    } else {
        CGFloat natureHeight = _scrollView.contentSize.height + _scrollView.contentInset.bottom;
        CGFloat judgeHeight = natureHeight > _scrollView.height ? _scrollView.height : natureHeight;
        CGFloat bottomOffset = _scrollView.contentSize.height - _scrollView.contentOffset.y + _scrollView.contentInset.bottom;
        return (judgeHeight - bottomOffset >= self.threshold);
    }
}

- (void)finishLoading
{
    if (self.status != SPKScrollViewDragControlStatusDisabled) {
        [self setStatus:SPKScrollViewDragControlStatusNormal animated:YES];
    }
}

- (void)addTarget:(id)target action:(SEL)action
{
    _target = target;
    _selector = action;
}

- (void)didTapSelf:(id)sender
{
    if (self.status == SPKScrollViewDragControlStatusNormal && self.type == SPKScrollViewDragControlTypeLoadMore && self.scrollView) {
        SuppressPerformSelectorLeakWarning([_target performSelector:_selector withObject:self]);
        [self setStatus:SPKScrollViewDragControlStatusLoading animated:YES];
    }
}

#pragma mark private method

- (void)__scrollToNormalAnimated:(BOOL)animated
{
    UIEdgeInsets contentInset = _scrollView.contentInset;
    if (self.type == SPKScrollViewDragControlTypeRefresh) {
        contentInset.top = _contentInset.top + self.refreshViewTopInset;
        if (animated) {
            [UIView animateWithDuration:METScrollViewDragControl_animatedDuration animations:^{
                self.scrollView.contentInset = contentInset;
            }];
        } else {
            self.scrollView.contentInset = contentInset;
        }
    } else {
    }
    [self scrollToNormalAnimated:animated];
}

- (void)__scrollToNormalFromLoadingStatusAnimated:(BOOL)animated
{
    UIEdgeInsets contentInset = _scrollView.contentInset;
    if (self.type == SPKScrollViewDragControlTypeRefresh) {
        contentInset.top = _contentInset.top + self.refreshViewTopInset;
        if (animated) {
            [UIView animateWithDuration:METScrollViewDragControl_animatedDuration animations:^{
                self.scrollView.contentInset = contentInset;
            } completion:^(BOOL finished) {
                [self scrollToNormalAnimated:animated];
            }];
        } else {
            [self scrollToNormalAnimated:animated];
        }
    } else {
        [self scrollToNormalAnimated:animated];
    }
}

- (void)__scrollToNormalFromPullStatusAnimated:(BOOL)animated
{
    UIEdgeInsets contentInset = _scrollView.contentInset;
    if (self.type == SPKScrollViewDragControlTypeRefresh) {
        contentInset.top = _contentInset.top + self.refreshViewTopInset;
        [self performSelector:@selector(scrollToNormalAnimated:) withObject:@(animated) afterDelay:METScrollViewDragControl_animatedDuration];
    } else {
        [self scrollToNormalAnimated:animated];
    }
}

- (void)__scrollToPullingAnimated:(BOOL)animated
{
    [self scrollToPullingAnimated:animated];
}

- (void)__scrollToPullingOverAnimated:(BOOL)animated
{
    [self scrollToPullingOverAnimated:animated];
}

- (void)__scrollToLoadingAnimated:(BOOL)animated
{
    UIEdgeInsets contentInset = _scrollView.contentInset;
    if (self.type == SPKScrollViewDragControlTypeRefresh) {
        contentInset.top = _contentInset.top + self.height + self.refreshViewTopInset;
        if (animated) {
            [UIView animateWithDuration:METScrollViewDragControl_animatedDuration animations:^{
                self.scrollView.contentInset = contentInset;
            }];
        } else {
            _scrollView.contentInset = contentInset;
        }
        [_scrollView setContentOffset:CGPointMake(_scrollView.contentOffset.x, -contentInset.top) animated:animated];
    }
    [self scrollToLoadingAnimated:animated];
    SuppressPerformSelectorLeakWarning([_target performSelector:_selector withObject:self]);
}

- (void)__scrollToEnableAnimated:(BOOL)animated
{
    self.hidden = NO;
    if (self.type == SPKScrollViewDragControlTypeLoadMore) {
        UIEdgeInsets contentInset = _scrollView.contentInset;
        contentInset.bottom = _contentInset.bottom;
        _scrollView.contentInset = contentInset;
    }
    [self scrollToEnableAnimated:animated];
}

- (void)__scrollToDisabledAnimated:(BOOL)animated
{
    self.hidden = YES;
    if (self.type == SPKScrollViewDragControlTypeLoadMore) {
        UIEdgeInsets contentInset = _scrollView.contentInset;
        contentInset.bottom = _contentInset.bottom - self.height;
        _scrollView.contentInset = contentInset;
    }
    [self scrollToDisabledAnimated:animated];
    
}

#pragma mark override method

- (void)scrollToNormalAnimated:(BOOL)animated {};
- (void)scrollToPullingAnimated:(BOOL)animated {};
- (void)scrollToPullingOverAnimated:(BOOL)animated {};
- (void)scrollToLoadingAnimated:(BOOL)animated {};
- (void)scrollToEnableAnimated:(BOOL)animated {};
- (void)scrollToDisabledAnimated:(BOOL)animated {};

@end
