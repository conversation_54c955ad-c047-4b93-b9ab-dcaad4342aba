//
//  SPKRefreshConst.h
//  SAKPaymentKit
//
//  Created by Eric on 2018/1/11.
//  参照 SPKRefresh

#import <UIKit/UIKit.h>

// 常量
UIKIT_EXTERN const CGFloat SPKRefreshLabelLeftInset;
UIKIT_EXTERN const CGFloat SPKRefreshHeaderHeight;
UIKIT_EXTERN const CGFloat SPKRefreshFooterHeight;
UIKIT_EXTERN const CGFloat SPKRefreshFastAnimationDuration;
UIKIT_EXTERN const CGFloat SPKRefreshSlowAnimationDuration;

UIKIT_EXTERN NSString *const SPKRefreshKeyPathContentOffset;
UIKIT_EXTERN NSString *const SPKRefreshKeyPathContentSize;
UIKIT_EXTERN NSString *const SPKRefreshKeyPathContentInset;
UIKIT_EXTERN NSString *const SPKRefreshKeyPathPanState;

UIKIT_EXTERN NSString *const SPKRefreshHeaderIdleText;
UIKIT_EXTERN NSString *const SPKRefreshHeaderPullingText;
UIKIT_EXTERN NSString *const SPKRefreshHeaderRefreshingText;

UIKIT_EXTERN NSString *const SPKRefreshAutoFooterIdleText;
UIKIT_EXTERN NSString *const SPKRefreshAutoFooterRefreshingText;
UIKIT_EXTERN NSString *const SPKRefreshAutoFooterNoMoreDataText;

// 状态检查
#define SPKRefreshCheckState \
SPKRefreshState oldState = self.state; \
if (state == oldState) return; \
[super setState:state];
