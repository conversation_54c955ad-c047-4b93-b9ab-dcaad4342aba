//
//  MTWTitleTipCell.m
//  Pods
//
//  Created by wang<PERSON> on 15/9/8.
//
//

#import "SPKTitleTipCell.h"
#import "Masonry.h"
#import "SPKUIKitMacros.h"

@interface SPKTitleTipCell ()

@property (nonatomic, strong) UIView *redCircleTag;

@end

@implementation SPKTitleTipCell

- (id)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        _tipLabel = ({
            UILabel *label = [[UILabel alloc] init];
            label.textColor = kSPKDescriptionTextColor;
            label.font = Font(13);
            label;
        });
        [self addSubview:_tipLabel];
        
        _redCircleTag = ({
            UIView *view = [[UIView alloc] init];
            view.backgroundColor = HEXCOLOR(0xF84048);
            view.layer.cornerRadius = 2.5;
            view;
        });
        [self addSubview:_redCircleTag];
        
        _redCircleVisible = NO;
        _redCircleTag.hidden = !_redCircleVisible;

    }
    return self;
}

- (void)setRedCircleVisible:(BOOL)redCircleVisible
{
    _redCircleVisible = redCircleVisible;
    self.redCircleTag.hidden = !redCircleVisible;
}

+ (BOOL)requiresConstraintBasedLayout
{
    return YES;
}

- (void)updateConstraints
{
    if (self.customAccessoryVisible) {
        [self.tipLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.customAccessoryView.mas_left).offset(-10);
            make.centerY.equalTo(self);
            make.height.mas_equalTo(13);
        }];
    } else {
        [self.tipLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.mas_right).offset(-15);
            make.centerY.equalTo(self);
            make.height.mas_equalTo(13);
        }];
    }

    [self.redCircleTag mas_updateConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.tipLabel.mas_left).offset(-6);
        make.centerY.equalTo(self);
        make.height.width.mas_equalTo(5);
    }];
    
    [super updateConstraints];
}

@end
