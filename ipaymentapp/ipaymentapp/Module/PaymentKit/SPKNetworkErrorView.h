//
//  SPKNetworkErrorView.h
//  Pods
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2017/9/8.
//
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@class RACCommand;

/**
 错误视图，规范见 https://wiki.sankuai.com/pages/viewpage.action?pageId=724259758
 */
@interface SPKNetworkErrorView : UIView

@property (nullable, nonatomic, strong) RACCommand *refreshCommand; // 刷新命令，当用户点击错误视图的“点击刷新”按钮时触发该命令，调用方可以用于刷新数据

/**
 错误视图初始化方法

 @param errorMessage 错误提示文案，默认为“您的网络好像不大给力”
 @param iconImage 错误提示 icon，如果不传则使用默认图，样式可参考 https://wiki.sankuai.com/pages/viewpage.action?pageId=724259758
 @param refreshButtonTitle 刷新按钮标题，如果不传则使用默认标题“点击刷新”
 @return 错误视图对象实例
 */
- (nonnull instancetype)initWithErrorMessage:(nullable NSString *)errorMessage iconImage:(nullable UIImage *)iconImage refreshButtonTitle:(nullable NSString *)refreshButtonTitle NS_DESIGNATED_INITIALIZER;

@end

NS_ASSUME_NONNULL_END
