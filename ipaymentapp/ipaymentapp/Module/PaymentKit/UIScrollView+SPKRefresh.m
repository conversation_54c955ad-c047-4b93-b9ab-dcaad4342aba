//
//  UIScrollView+SPKRefresh.m
//  SAKPaymentKit
//
//  Created by <PERSON> on 2018/1/10.
//

#import "UIScrollView+SPKRefresh.h"
#import "SPKRefreshHeader.h"
#import "SPKRefreshFooter.h"
#import "SPKFoundationMacros.h"
#import <objc/runtime.h>

#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wunguarded-availability-new"

@implementation UIScrollView (SPKRefresh)

- (UIEdgeInsets)spk_inset
{
#ifdef __IPHONE_11_0
    if (SPK_SYSTEM_VERSION_GREATER_THAN_OR_EQUAL_TO(@"11.0")) {
        return self.adjustedContentInset;
    }
#endif
    return self.contentInset;
}

- (void)setSpk_insetT:(CGFloat)spk_insetT
{
    UIEdgeInsets inset = self.contentInset;
    inset.top = spk_insetT;
#ifdef __IPHONE_11_0
    if (SPK_SYSTEM_VERSION_GREATER_THAN_OR_EQUAL_TO(@"11.0")) {
        inset.top -= (self.adjustedContentInset.top - self.contentInset.top);
    }
#endif
    self.contentInset = inset;
}

- (CGFloat)spk_insetT
{
    return self.spk_inset.top;
}

- (void)setSpk_insetB:(CGFloat)spk_insetB
{
    UIEdgeInsets inset = self.contentInset;
    inset.bottom = spk_insetB;
#ifdef __IPHONE_11_0
    if (SPK_SYSTEM_VERSION_GREATER_THAN_OR_EQUAL_TO(@"11.0")) {
        inset.bottom -= (self.adjustedContentInset.bottom - self.contentInset.bottom);
    }
#endif
    self.contentInset = inset;
}

- (CGFloat)spk_insetB
{
    return self.spk_inset.bottom;
}

- (void)setSpk_insetL:(CGFloat)spk_insetL
{
    UIEdgeInsets inset = self.contentInset;
    inset.left = spk_insetL;
#ifdef __IPHONE_11_0
    if (SPK_SYSTEM_VERSION_GREATER_THAN_OR_EQUAL_TO(@"11.0")) {
        inset.left -= (self.adjustedContentInset.left - self.contentInset.left);
    }
#endif
    self.contentInset = inset;
}

- (CGFloat)spk_insetL
{
    return self.spk_inset.left;
}

- (void)setSpk_insetR:(CGFloat)spk_insetR
{
    UIEdgeInsets inset = self.contentInset;
    inset.right = spk_insetR;
#ifdef __IPHONE_11_0
    if (SPK_SYSTEM_VERSION_GREATER_THAN_OR_EQUAL_TO(@"11.0")) {
        inset.right -= (self.adjustedContentInset.right - self.contentInset.right);
    }
#endif
    self.contentInset = inset;
}

- (CGFloat)spk_insetR
{
    return self.spk_inset.right;
}

- (void)setSpk_offsetX:(CGFloat)spk_offsetX
{
    CGPoint offset = self.contentOffset;
    offset.x = spk_offsetX;
    self.contentOffset = offset;
}

- (CGFloat)spk_offsetX
{
    return self.contentOffset.x;
}

- (void)setSpk_offsetY:(CGFloat)spk_offsetY
{
    CGPoint offset = self.contentOffset;
    offset.y = spk_offsetY;
    self.contentOffset = offset;
}

- (CGFloat)spk_offsetY
{
    return self.contentOffset.y;
}

- (void)setSpk_contentW:(CGFloat)spk_contentW
{
    CGSize size = self.contentSize;
    size.width = spk_contentW;
    self.contentSize = size;
}

- (CGFloat)spk_contentW
{
    return self.contentSize.width;
}

- (void)setSpk_contentH:(CGFloat)spk_contentH
{
    CGSize size = self.contentSize;
    size.height = spk_contentH;
    self.contentSize = size;
}

- (CGFloat)spk_contentH
{
    return self.contentSize.height;
}

#pragma mark - header
static const char SPKRefreshHeaderKey = '\0';
- (void)setSpk_header:(SPKRefreshHeader *)spk_header
{
    if (spk_header != self.spk_header) {
        // 删除旧的，添加新的
        [self.spk_header removeFromSuperview];
        [self insertSubview:spk_header atIndex:0];
        
        // 存储新的
        [self willChangeValueForKey:@"spk_header"]; // KVO
        objc_setAssociatedObject(self, &SPKRefreshHeaderKey,
                                 spk_header, OBJC_ASSOCIATION_ASSIGN);
        [self didChangeValueForKey:@"spk_header"]; // KVO
    }
}

- (SPKRefreshHeader *)spk_header
{
    return objc_getAssociatedObject(self, &SPKRefreshHeaderKey);
}

#pragma mark - footer
static const char SPKRefreshFooterKey = '\0';
- (void)setSpk_footer:(SPKRefreshFooter *)spk_footer
{
    if (spk_footer != self.spk_footer) {
        // 删除旧的，添加新的
        [self.spk_footer removeFromSuperview];
        [self insertSubview:spk_footer atIndex:0];
        
        // 存储新的
        [self willChangeValueForKey:@"spk_footer"]; // KVO
        objc_setAssociatedObject(self, &SPKRefreshFooterKey,
                                 spk_footer, OBJC_ASSOCIATION_ASSIGN);
        [self didChangeValueForKey:@"spk_footer"]; // KVO
    }
}

- (SPKRefreshFooter *)spk_footer
{
    return objc_getAssociatedObject(self, &SPKRefreshFooterKey);
}
#pragma mark - other
- (NSInteger)spk_totalDataCount
{
    NSInteger totalCount = 0;
    if ([self isKindOfClass:[UITableView class]]) {
        UITableView *tableView = (UITableView *)self;
        
        for (NSInteger section = 0; section<tableView.numberOfSections; section++) {
            totalCount += [tableView numberOfRowsInSection:section];
        }
    } else if ([self isKindOfClass:[UICollectionView class]]) {
        UICollectionView *collectionView = (UICollectionView *)self;
        
        for (NSInteger section = 0; section<collectionView.numberOfSections; section++) {
            totalCount += [collectionView numberOfItemsInSection:section];
        }
    }
    return totalCount;
}

static const char SPKRefreshReloadDataBlockKey = '\0';
- (void)setSpk_reloadDataBlock:(void (^)(NSInteger))spk_reloadDataBlock
{
    [self willChangeValueForKey:@"spk_reloadDataBlock"]; // KVO
    objc_setAssociatedObject(self, &SPKRefreshReloadDataBlockKey, spk_reloadDataBlock, OBJC_ASSOCIATION_COPY_NONATOMIC);
    [self didChangeValueForKey:@"spk_reloadDataBlock"]; // KVO
}

- (void (^)(NSInteger))spk_reloadDataBlock
{
    return objc_getAssociatedObject(self, &SPKRefreshReloadDataBlockKey);
}

- (void)executeReloadDataBlock
{
    !self.spk_reloadDataBlock ? : self.spk_reloadDataBlock(self.spk_totalDataCount);
}

@end
#pragma clang diagnostic pop

@implementation UITableView (SPKRefresh)

- (void)spk_reloadData
{
    [self spk_reloadData];
    
    [self executeReloadDataBlock];
}
@end

@implementation UICollectionView (SPKRefresh)

- (void)spk_reloadData
{
    [self spk_reloadData];
    
    [self executeReloadDataBlock];
}
@end
