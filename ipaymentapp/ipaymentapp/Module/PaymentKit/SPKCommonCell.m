//
//  SPKCommonCell.m
//  Pods
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 16/1/12.
//
//

#import "SPKCommonCell.h"
#import "View+MASAdditions.h"
#import "UIImage+SPKPayment.h"
#import "SPKUIKitMacros.h"

@interface SPKCommonCell ()

@property (nonatomic, strong) UIView *outerBoundaryTopSeparatorLine;    // 外边界顶部分割线
@property (nonatomic, strong) UIView *outerBoundaryBottomSeparatorLine; // 外边界底部分割线
@property (nonatomic, strong) UIView *innerBoundaryBottomSeparatorLine; // 内边界底部分割线

@property (nonatomic, strong) UIImageView *customAccessoryView;

@end

@implementation SPKCommonCell

@synthesize titleLabel = _titleLabel;

- (id)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self iconTitleCellCommonInit];
    }
    return self;
}

- (void)iconTitleCellCommonInit
{
    self.accessoryVisible = NO;
    self.separatorLineViewVisible = NO;
    self.indentationWidth = 15;
    self.highlightedBackgroundColor = kSPKCellHighlightColor;
    _titleLabel = [[UILabel alloc] init];
    _titleLabel.backgroundColor = [UIColor clearColor];
    _titleLabel.textColor = kSPKTitleLabelTextColor;
    _titleLabel.font = [UIFont systemFontOfSize:15];
    [self addSubview:self.titleLabel];
    self.translatesAutoresizingMaskIntoConstraints = NO;
}

+ (BOOL)requiresConstraintBasedLayout
{
    return YES;
}

- (void)setSeparatorLineType:(SPKCommonCellSeparatorLineType)separatorLineType
{
    _separatorLineType = separatorLineType;
    
    [_outerBoundaryTopSeparatorLine removeFromSuperview];
    [_innerBoundaryBottomSeparatorLine removeFromSuperview];
    [_outerBoundaryBottomSeparatorLine removeFromSuperview];
    
    if (_separatorLineType == SPKCommonCellSeparatorLineTypeNone) {
        return;
    }
    
    if (_separatorLineType & SPKCommonCellSeparatorLineTypeOuterBoundaryTop) {
        if (!_outerBoundaryTopSeparatorLine) {
            _outerBoundaryTopSeparatorLine = [[UIView alloc] init];
            _outerBoundaryTopSeparatorLine.backgroundColor = kSPKSeparateLineColor;
        }
        if (!_outerBoundaryTopSeparatorLine.superview) {
            [self addSubview:_outerBoundaryTopSeparatorLine];
        }
    }
    
    if (_separatorLineType & SPKCommonCellSeparatorLineTypeInnerBoundaryBottom) {
        if (!_innerBoundaryBottomSeparatorLine) {
            _innerBoundaryBottomSeparatorLine = [[UIView alloc] init];
            _innerBoundaryBottomSeparatorLine.backgroundColor = kSPKSeparateLineColor;
        }
        if (!_innerBoundaryBottomSeparatorLine.superview) {
            [self addSubview:_innerBoundaryBottomSeparatorLine];
        }
    }
    
    if (_separatorLineType & SPKCommonCellSeparatorLineTypeOuterBoundaryBottom) {
        if (!_outerBoundaryBottomSeparatorLine) {
            _outerBoundaryBottomSeparatorLine = [[UIView alloc] init];
            _outerBoundaryBottomSeparatorLine.backgroundColor = kSPKSeparateLineColor;
        }
        if (!_outerBoundaryBottomSeparatorLine.superview) {
            [self addSubview:_outerBoundaryBottomSeparatorLine];
        }
    }
    // 在 iOS8 设备上，当动态添加分割线时，需要手动调用更新约束的方法
    [self setNeedsUpdateConstraints];
}

- (void)setCustomAccessoryVisible:(BOOL)customAccessoryVisible
{
    _customAccessoryVisible = customAccessoryVisible;
    if (_customAccessoryVisible) {
        if (_customAccessoryView) {
            return;
        }
        _customAccessoryView = [[UIImageView alloc] init];
        _customAccessoryView.contentMode = UIViewContentModeScaleAspectFit;
        _customAccessoryView.image = [UIImage imageNamed:@"payment_kit_icon_accessory_view"];
        [self addSubview:_customAccessoryView];
    } else {
        if (_customAccessoryView) {
            [_customAccessoryView removeFromSuperview];
            _customAccessoryView = nil;
        }
    }
}

- (void)updateConstraints
{
    [self.titleLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self).with.offset(15).priorityMedium();
        make.centerY.equalTo(self).priorityMedium();
    }];
    
    if ((self.separatorLineType & SPKCommonCellSeparatorLineTypeOuterBoundaryTop) && self.outerBoundaryTopSeparatorLine) {
        [self.outerBoundaryTopSeparatorLine mas_updateConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self);
            make.top.equalTo(self);
            make.right.equalTo(self);
            make.height.equalTo(@0.5);
        }];
    }
    
    if ((self.separatorLineType & SPKCommonCellSeparatorLineTypeInnerBoundaryBottom) && self.innerBoundaryBottomSeparatorLine) {
        [self.innerBoundaryBottomSeparatorLine mas_updateConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self).offset(self.indentationWidth);
            make.bottom.equalTo(self);
            make.right.equalTo(self);
            make.height.equalTo(@0.5);
        }];
    }
    
    if ((self.separatorLineType & SPKCommonCellSeparatorLineTypeOuterBoundaryBottom) && self.outerBoundaryBottomSeparatorLine) {
        [self.outerBoundaryBottomSeparatorLine mas_updateConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self);
            make.bottom.equalTo(self);
            make.right.equalTo(self);
            make.height.equalTo(@0.5);
        }];
    }
    
    if (self.customAccessoryVisible && self.customAccessoryView) {
        [self.customAccessoryView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(self);
            make.right.equalTo(self).offset(-15);
            make.width.equalTo(@6);
            make.height.equalTo(@11);
        }];
    }
    
    [super updateConstraints];
}

@end
