//
//  SPKRefreshNormalHeader.m
//  SAKPaymentKit
//
//  Created by <PERSON> on 2018/1/11.
//

#import "SPKRefreshNormalHeader.h"
#import "UIImage+SPKPayment.h"
#import "SPKUIKitMacros.h"
#import "Masonry.h"

@interface SPKRefreshNormalHeader ()

/** 所有状态对应的文字 */
@property (nonatomic, strong) NSMutableDictionary *stateTitles;

@end

@implementation SPKRefreshNormalHeader

- (NSMutableDictionary *)stateTitles
{
    if (!_stateTitles) {
        self.stateTitles = [NSMutableDictionary dictionary];
    }
    return _stateTitles;
}

#pragma mark - 重写父类的方法
- (void)prepare
{
    [super prepare];
    
    self.refreshingTitleHidden = YES;
    
    _stateLabel = ({
        UILabel *label = [[UILabel alloc] init];
        label.font = Font(13);
        label.textColor = kSPKLabelTextDisableColor;
        label;
    });
    [self addSubview:_stateLabel];
    
    _gifView = ({
        UIImageView *imageView = [[UIImageView alloc] init];
        imageView.image = [UIImage new];
        imageView;
    });
    [self addSubview:_gifView];
    
}

- (void)placeSubviews
{
    [super placeSubviews];
    [_stateLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self);
    }];
    [_gifView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self);
        make.height.width.mas_equalTo(30);
    }];
    
}

- (void)setState:(SPKRefreshState)state
{
    SPKRefreshCheckState;
    
    if (self.refreshingTitleHidden && state == SPKRefreshStateRefreshing) {
        self.stateLabel.text = nil;
        self.gifView.image = [UIImage spk_animatedGIFNamed:@"icon_loading_green"];
    } else {
        self.stateLabel.text = self.stateTitles[@(state)];
        self.gifView.image = nil;
    }
}

- (void)setTitle:(NSString *)title forState:(SPKRefreshState)state
{
    if (title == nil) return;
    self.stateTitles[@(state)] = title;
    self.stateLabel.text = self.stateTitles[@(self.state)];
}

@end
