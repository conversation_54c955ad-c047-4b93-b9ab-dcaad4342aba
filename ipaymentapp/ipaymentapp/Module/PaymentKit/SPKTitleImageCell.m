//
//  SPKTitleImageCell.m
//  Pods
//
//  Created by <PERSON> on 2018/1/3.
//

#import "SPKTitleImageCell.h"
#import "Masonry.h"
#import "SPKUIKitMacros.h"

@implementation SPKTitleImageCell

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        _tipLabel = ({
            UILabel *label = [[UILabel alloc] init];
            label.textColor = kSPKDescriptionTextColor;
            label.font = Font(13);
            label;
        });
        [self addSubview:_tipLabel];
        
        _leftImageView = ({
            UIImageView *imageView = [[UIImageView alloc] init];
            imageView.contentMode = UIViewContentModeScaleAspectFit;
            imageView;
        });
        [self addSubview:_leftImageView];
    }
    return self;
}

+ (BOOL)requiresConstraintBasedLayout
{
    return YES;
}

- (void)updateConstraints
{
    [self.leftImageView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.mas_left).offset(15);
        make.centerY.equalTo(self);
        make.height.width.mas_equalTo(24);
    }];
    
    [self.titleLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.leftImageView.mas_right).with.offset(8).priorityHigh();
    }];
    
    if (self.customAccessoryVisible) {
        [self.tipLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.customAccessoryView.mas_left).offset(-10);
            make.centerY.equalTo(self);
            make.height.equalTo(@15);
        }];
    } else {
        [self.tipLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.mas_right).offset(-15);
            make.centerY.equalTo(self);
            make.height.equalTo(@15);
        }];
    }
    
    [super updateConstraints];
}

@end

