//
//  SPKRefreshComponent.h
//  SAKPaymentKit
//
//  Created by Eric on 2018/1/11.
//

#import <UIKit/UIKit.h>
#import "UIScrollView+SPKRefresh.h"
#import "SPKRefreshConst.h"

#define SuppressPerformSelectorLeakWarning(Stuff) \
do { \
_Pragma("clang diagnostic push") \
_Pragma("clang diagnostic ignored \"-Warc-performSelector-leaks\"") \
Stuff; \
_Pragma("clang diagnostic pop") \
} while (0)

// 刷新控件的状态
typedef NS_ENUM(NSInteger, SPKRefreshState) {
    // 普通闲置状态
    SPKRefreshStateIdle = 1,
    //松开就可以进行刷新的状态
    SPKRefreshStatePulling,
    // 正在刷新中的状态
    SPKRefreshStateRefreshing,
    // 即将刷新的状态
    SPKRefreshStateWillRefresh,
    // 所有数据加载完毕，没有更多的数据了
    SPKRefreshStateNoMoreData
};

// 进入刷新状态的回调
typedef void (^SPKRefreshComponentRefreshingBlock)(void);
// 开始刷新后的回调(进入刷新状态后的回调)
typedef void (^SPKRefreshComponentbeginRefreshingCompletionBlock)(void);
// 结束刷新后的回调
typedef void (^SPKRefreshComponentEndRefreshingCompletionBlock)(void);

@interface SPKRefreshComponent : UIView

@property (nonatomic, readonly, weak) UIScrollView *scrollView;
// 记录scrollView刚开始的inset
@property (nonatomic, assign) UIEdgeInsets scrollViewOriginalInset;
/** 拉拽的百分比 */
@property (nonatomic, assign) CGFloat pullingPercent;
/** 根据拖拽比例自动切换透明度 */
@property (nonatomic, assign, getter=isAutomaticallyChangeAlpha) BOOL automaticallyChangeAlpha;

#pragma mark - 刷新回调
/** 正在刷新的回调 */
@property (nonatomic, copy) SPKRefreshComponentRefreshingBlock refreshingBlock;
/** 回调对象 */
@property (nonatomic, weak) id refreshingTarget;
/** 回调方法 */
@property (nonatomic, assign) SEL refreshingAction;
/** 设置回调对象和回调方法 */
- (void)setRefreshingTarget:(id)target refreshingAction:(SEL)action;
/** 触发回调（交给子类去调用） */
- (void)executeRefreshingCallback;

#pragma mark - 刷新状态控制
/** 进入刷新状态 */
- (void)beginRefreshing;
/** 结束刷新的回调 */
@property (nonatomic, copy) SPKRefreshComponentEndRefreshingCompletionBlock endRefreshingCompletionBlock;
/** 结束刷新状态 */
- (void)endRefreshing;
- (void)endRefreshingWithCompletionBlock:(void (^)(void))completionBlock;
/** 是否正在刷新 */
@property (nonatomic, readonly, assign, getter=isRefreshing) BOOL refreshing;
// 刷新状态
@property (nonatomic, assign) SPKRefreshState state; //kvo


#pragma mark - 交给子类们去实现
// 初始化
- (void)prepare NS_REQUIRES_SUPER;
// 摆放子控件frame
- (void)placeSubviews NS_REQUIRES_SUPER;
// 当scrollView的contentOffset发生改变的时候调用
- (void)scrollViewContentOffsetDidChange:(NSDictionary *)change NS_REQUIRES_SUPER;
// 当scrollView的contentSize发生改变的时候调用
- (void)scrollViewContentSizeDidChange:(NSDictionary *)change NS_REQUIRES_SUPER;
// 当scrollView的拖拽状态发生改变的时候调用
- (void)scrollViewPanStateDidChange:(NSDictionary *)change NS_REQUIRES_SUPER;


#pragma mark - 其他


@end
