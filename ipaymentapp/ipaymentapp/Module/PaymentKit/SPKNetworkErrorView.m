//
//  SPKNetworkErrorView.m
//  Pods
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2017/9/8.
//
//

#import "SPKNetworkErrorView.h"
#import "SPKUIKitMacros.h"
#import <Masonry/Masonry.h>
#import <ReactiveCocoa/ReactiveCocoa.h>
#import "SPKFoundationMacros.h"
#import "UIImage+SPKPayment.h"
#import "UIImage+SAKColor.h"

static const CGFloat kSPKNetworkErrorIconTopMargin = 120.0;
static const CGFloat kSPKNetworkErrorMessageTopMargin = 6.0, kSPKNetworkErrorMessageLeftMargin = 15.0;
static const CGFloat kSPKNetworkErrorMessageHeight = 25.0;
static const CGFloat kSPKNetworkErrorRefreshButtonTopMargin = 17.0;
static const CGFloat kSPKNetworkErrorRefreshButtonHeight = 40.0;
static const CGFloat kSPKNetworkErrorRefreshButtonWidth = 120.0;

@interface SPKNetworkErrorView ()

@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UIImageView *iconImageView;
@property (nonatomic, strong) UIButton *refreshButton;
@property (nonatomic, strong) UIImage *iconImage;

@end

@implementation SPKNetworkErrorView

- (nonnull instancetype)initWithErrorMessage:(nullable NSString *)errorMessage iconImage:(nullable UIImage *)iconImage refreshButtonTitle:(nullable NSString *)refreshButtonTitle
{
    if (self = [super initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT)]) {
        
        self.iconImage = iconImage OR [UIImage imageNamed:@"payment_kit_icon_networkerror"];
        self.backgroundColor = [UIColor whiteColor];
        
        _iconImageView = ({
            UIImageView *imageView = [[UIImageView alloc] init];
            imageView.contentMode = UIViewContentModeScaleAspectFit;
            imageView.image = self.iconImage;
            imageView;
        });
        [self addSubview:_iconImageView];
        
        _titleLabel =({
            UILabel *label = [[UILabel alloc] init];
            label.textAlignment = NSTextAlignmentCenter;
            label.textColor = HEXCOLOR(0x8E8D8E);
            label.font = Font(14);
            label.text = errorMessage OR @"您的网络好像不太给力";
            label;
        });
        [self addSubview:_titleLabel];
        
        _refreshButton = ({
            UIButton *button = [UIButton new];
            button.layer.cornerRadius = 20;
            button.layer.masksToBounds = YES;
            button.titleLabel.font = Font(16);

            [button setBackgroundImage:[UIImage imageWithColor:HEXCOLOR(0x00CECC)] forState:UIControlStateNormal];
            [button setBackgroundImage:[UIImage imageWithColor:HEXCOLOR(0x17AE9E)] forState:UIControlStateHighlighted];
            [button setBackgroundImage:[UIImage imageWithColor:HEXCOLOR(0xA8E5E0)] forState:UIControlStateDisabled];
            
            [button setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
            [button setTitle:(refreshButtonTitle OR @"刷新一下") forState:UIControlStateNormal];
            [button addTarget:self action:@selector(refreshData) forControlEvents:UIControlEventTouchUpInside];
            button;
        });
        [self addSubview:_refreshButton];
    }
    return self;
}

- (instancetype)initWithFrame:(CGRect)frame
{
    return [self initWithErrorMessage:nil iconImage:nil refreshButtonTitle:nil];
}

- (instancetype)initWithCoder:(NSCoder *)coder
{
    return [self initWithErrorMessage:nil iconImage:nil refreshButtonTitle:nil];
}

- (instancetype)init
{
    return [self initWithErrorMessage:nil iconImage:nil refreshButtonTitle:nil];;
}

- (void)refreshData
{
    if (self.refreshCommand) {
        [self.refreshCommand execute:nil];
    }
}

+ (BOOL)requiresConstraintBasedLayout
{
    return YES;
}

- (void)updateConstraints
{
    [self.iconImageView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self).offset(kSPKNetworkErrorIconTopMargin);
        make.centerX.equalTo(self);
        make.height.equalTo(@(self.iconImage.size.height));
        make.width.equalTo(@(self.iconImage.size.width));
    }];
    
    [self.titleLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.iconImageView.mas_bottom).offset(kSPKNetworkErrorMessageTopMargin);
        make.left.equalTo(self.mas_left).offset(kSPKNetworkErrorMessageLeftMargin);
        make.right.equalTo(self.mas_right).offset(-kSPKNetworkErrorMessageLeftMargin);
        make.height.mas_equalTo(kSPKNetworkErrorMessageHeight);
    }];
    
    [self.refreshButton mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.titleLabel.mas_bottom).offset(kSPKNetworkErrorRefreshButtonTopMargin);
        make.height.mas_equalTo(kSPKNetworkErrorRefreshButtonHeight);
        make.width.mas_equalTo(kSPKNetworkErrorRefreshButtonWidth);
        make.centerX.equalTo(self);
    }];
    
    [super updateConstraints];
}


@end
