//
//  METScrollViewRefreshControl.m
//  imeituan
//
//  Created by zjs on 14/9/10.
//  Copyright (c) 2014年 meituan.com. All rights reserved.
//

#import "SPKScrollViewRefreshControl.h"
#import "SAKUIKitMacros.h"
#import "Masonry.h"
#import "UIImage+SAKUI.h"
#import "UIImage+SPKPayment.h"

@interface SPKScrollViewRefreshControl ()

@property (strong, nonatomic) UILabel *statusLabel;
@property (strong, nonatomic) UIImageView *pullImageView;
@property (strong, nonatomic) UIActivityIndicatorView *activityIndicatorView;

@end


@implementation SPKScrollViewRefreshControl

- (id)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame type:SPKScrollViewDragControlTypeRefresh];
    if (self) {
        self.backgroundColor = [UIColor clearColor];
        self.layer.zPosition = 999;
        self.threshold = 60;
        _backgroundView = [[UIImageView alloc] initWithFrame:self.bounds];
        _backgroundView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
        [_backgroundView setImage:[[UIImage imageNamed:@"payment_kit_bg_refresh_header"] sakui_resizableImage]];
        [self addSubview:_backgroundView];
        
        _statusLabel = [[UILabel alloc] init];
        _statusLabel.font = Font(13);
        _statusLabel.textColor = HEXCOLOR(0xa7a5a0);
        _statusLabel.shadowColor = HEXACOLOR(0xffffff, 0.5);
        _statusLabel.shadowOffset = CGSizeMake(0.0f, 1.0f);
        _statusLabel.backgroundColor = [UIColor clearColor];
        _statusLabel.textAlignment = NSTextAlignmentLeft;
        [self addSubview:_statusLabel];
        
        _activityIndicatorView = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleGray];
        _activityIndicatorView.hidesWhenStopped = NO;
        [self addSubview:_activityIndicatorView];
    }
    return self;
}

- (void)updateConstraints
{
    [self.statusLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self).offset(15);
        make.bottom.equalTo(self).offset(-10);
        make.height.equalTo(@14);
    }];
    
    [self.activityIndicatorView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.statusLabel.mas_left).offset(-10);
        make.centerY.equalTo(self.statusLabel);
        make.height.width.equalTo(@20);
    }];
    
    [super updateConstraints];
}

+ (BOOL)requiresConstraintBasedLayout
{
    return YES;
}

- (void)scrollToNormalAnimated:(BOOL)animated
{
    [super scrollToNormalAnimated:animated];
    _pullImageView.hidden = YES;
    _statusLabel.text = NSLocalizedString(@"下拉即可刷新", @"Pull down to refresh status");
    [_activityIndicatorView stopAnimating];
}

- (void)scrollToPullingAnimated:(BOOL)animated
{
    [super scrollToPullingAnimated:animated];
    
    _pullImageView.hidden = NO;
    _statusLabel.text = NSLocalizedString(@"下拉即可刷新", @"Pull down to refresh status");
    [_activityIndicatorView stopAnimating];
}

- (void)scrollToPullingOverAnimated:(BOOL)animated
{
    [super scrollToPullingOverAnimated:animated];
    
    _statusLabel.text = NSLocalizedString(@"释放即可刷新", @"Release to refresh status");
    _pullImageView.hidden = YES;
    [_activityIndicatorView stopAnimating];
}

- (void)scrollToLoadingAnimated:(BOOL)animated
{
    [super scrollToLoadingAnimated:animated];
    
    _statusLabel.text = NSLocalizedString(@"加载中...", @"Loading Status");
    _pullImageView.hidden = YES;
    [_activityIndicatorView startAnimating];
}

@end
