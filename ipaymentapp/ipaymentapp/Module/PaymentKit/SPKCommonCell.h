//
//  SPKCommonCell.h
//  Pods
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 16/1/12.
//
//

#import "SAKCell.h"

typedef NS_OPTIONS(NSUInteger, SPKCommonCellSeparatorLineType) {
    SPKCommonCellSeparatorLineTypeNone                      = 0,        // 无分割线
    SPKCommonCellSeparatorLineTypeOuterBoundaryTop          = 1 << 0,   // 外边界顶部分割线
    SPKCommonCellSeparatorLineTypeOuterBoundaryBottom       = 1 << 1,   // 外边界底部分割线
    SPKCommonCellSeparatorLineTypeInnerBoundaryBottom       = 1 << 2,   // 内边界底部分割线，“外边界底部分割线”如果同时也设置的话，此分割线会被覆盖
};

@interface SPKCommonCell : SAKCell

@property (nonatomic, readonly, strong) UILabel *titleLabel;
@property (nonatomic, assign) SPKCommonCellSeparatorLineType separatorLineType;
@property (nonatomic, readonly, strong) UIImageView *customAccessoryView;
@property (nonatomic, assign) BOOL customAccessoryVisible;  // 右边自定义图标 ">" 是否显示


@end
