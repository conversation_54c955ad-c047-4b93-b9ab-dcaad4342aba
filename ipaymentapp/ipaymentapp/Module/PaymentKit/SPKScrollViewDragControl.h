//
//  METScrollViewDragControl.h
//  imeituan
//
//  Created by zjs on 14/9/10.
//  Copyright (c) 2014年 meituan.com. All rights reserved.
//

#import <UIKit/UIKit.h>

#define SuppressPerformSelectorLeakWarning(Stuff) \
do { \
_Pragma("clang diagnostic push") \
_Pragma("clang diagnostic ignored \"-Warc-performSelector-leaks\"") \
Stuff; \
_Pragma("clang diagnostic pop") \
} while (0)

typedef NS_ENUM (NSInteger, SPKScrollViewDragControlStatus) {
    SPKScrollViewDragControlStatusNormal,
    SPKScrollViewDragControlStatusPulling,
    SPKScrollViewDragControlStatusPullingOver,
    SPKScrollViewDragControlStatusLoading,
    SPKScrollViewDragControlStatusDisabled
};

typedef NS_ENUM (NSInteger, SPKScrollViewDragControlType) {
    SPKScrollViewDragControlTypeRefresh,
    SPKScrollViewDragControlTypeLoadMore
};


@interface SPKScrollViewDragControl : UIView

@property (nonatomic, readonly, weak) UIScrollView *scrollView;
@property (nonatomic, readonly) SPKScrollViewDragControlStatus status; //kvo
@property (nonatomic, readonly) SPKScrollViewDragControlType type;
@property (nonatomic, assign) CGFloat threshold; // 下拉或上拉的触发距离
@property (nonatomic, assign) CGFloat refreshViewTopInset;

- (id)initWithFrame:(CGRect)frame type:(SPKScrollViewDragControlType)type; // 初始化后frame的高度不可以变

- (void)setScrollView:(UIScrollView *)scrollView; // kvo scrollview

- (void)addTarget:(id)target action:(SEL)action; // refresh or loadmore callback

- (void)finishLoading; // you should call this method after refresh or loadmore

- (void)setEnable:(BOOL)enable; // temp disable refresh or loadmore

//subclass can customize UI by overriding method below

- (void)scrollToNormalAnimated:(BOOL)animated;
- (void)scrollToPullingAnimated:(BOOL)animated;
- (void)scrollToPullingOverAnimated:(BOOL)animated;
- (void)scrollToLoadingAnimated:(BOOL)animated;
- (void)scrollToEnableAnimated:(BOOL)animated;
- (void)scrollToDisabledAnimated:(BOOL)animated;

@end
