//
//  SPKTitleTipTableViewCell.m
//  SAKPaymentKit
//
//  Created by <PERSON> on 2018/1/5.
//

#import "SPKTitleTipTableViewCell.h"
#import "Masonry.h"
#import "SPKUIKitMacros.h"

@interface SPKTitleTipTableViewCell ()

@property (nonatomic, strong) UIView *redCircleTag;

@end

@implementation SPKTitleTipTableViewCell

- (id)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        _tipLabel = ({
            UILabel *label = [[UILabel alloc] init];
            label.textColor = kSPKDescriptionTextColor;
            label.font = Font(13);
            label;
        });
        [self.contentView addSubview:_tipLabel];
        
        _redCircleTag = ({
            UIView *view = [[UIView alloc] init];
            view.backgroundColor = HEXCOLOR(0xF84048);
            view.layer.cornerRadius = 2.5;
            view;
        });
        [self.contentView addSubview:_redCircleTag];
        
        _redCircleVisible = NO;
        _redCircleTag.hidden = !_redCircleVisible;
        
    }
    return self;
}

- (void)setRedCircleVisible:(BOOL)redCircleVisible
{
    _redCircleVisible = redCircleVisible;
    self.redCircleTag.hidden = !redCircleVisible;
}

+ (BOOL)requiresConstraintBasedLayout
{
    return YES;
}

- (void)updateConstraints
{
    if (self.customAccessoryVisible) {
        [self.tipLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.customAccessoryView.mas_left).offset(-10);
            make.centerY.equalTo(self);
            make.height.equalTo(@15);
        }];
    } else {
        [self.tipLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.contentView.mas_right).offset(-15);
            make.centerY.equalTo(self.contentView);
            make.height.equalTo(@15);
        }];
    }
    
    [self.redCircleTag mas_updateConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.tipLabel.mas_left).offset(-6);
        make.centerY.equalTo(self);
        make.height.width.mas_equalTo(5);
    }];
    
    [super updateConstraints];
}

@end
