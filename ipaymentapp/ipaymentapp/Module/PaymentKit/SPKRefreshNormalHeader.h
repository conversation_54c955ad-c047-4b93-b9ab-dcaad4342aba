//
//  SPKRefreshNormalHeader.h
//  SAKPaymentKit
//
//  Created by <PERSON> on 2018/1/11.
//

#import "SPKRefreshHeader.h"

@interface SPKRefreshNormalHeader : SPKRefreshHeader

/** 显示刷新状态的label */
@property (nonatomic, strong) UILabel *stateLabel;
/** 设置state状态下的文字 */
- (void)setTitle:(NSString *)title forState:(SPKRefreshState)state;

/** 隐藏刷新状态的文字 */
@property (nonatomic, assign) BOOL refreshingTitleHidden;

@property (nonatomic, strong) UIImageView *gifView;

@end
