//
//  SPKCommonTableView.m
//  Pods
//
//  Created by sunhl on 15/5/13.
//  
//
//

#import "SPKCommonTableView.h"
#import "UIViewAdditions.h"
#import "RACCommand.h"
#import "RACSignal.h"
#import <libextobjc/extobjc.h>

@implementation SPKCommonTableView

@synthesize scrollViewRefreshControl = _scrollViewRefreshControl;
@synthesize scrollViewLoadMoreControl = _scrollViewLoadMoreControl;


#pragma - mark setter

- (void)setLoadMoreCommand:(RACCommand *)loadMoreCommand
{
    _loadMoreCommand = loadMoreCommand;
    if (_loadMoreCommand) {
        @weakify(self);
        [_loadMoreCommand.executing subscribeNext:^(NSNumber *executing) {
            @strongify(self);
            if (![executing boolValue]) {
                [self finishLoadMore];
            }
        }];
    }
}

- (void)setRefreshCommand:(RACCommand *)refreshCommand
{
    _refreshCommand = refreshCommand;
    if (_refreshCommand) {
        @weakify(self);
        [_refreshCommand.executing subscribeNext:^(NSNumber *executing) {
            @strongify(self);
            if (![executing boolValue]) {
                [self finishRefresh];
            }
        }];
    }
}

- (void)setEnableRefresh:(BOOL)enableRefresh
{
    if (_enableRefresh != enableRefresh) {
        _enableRefresh = enableRefresh;
        [_scrollViewRefreshControl setEnable:_enableRefresh];
        if (_enableRefresh) {
            [self.scrollViewRefreshControl setScrollView:self];
        }
    }
}

- (void)setEnableLoadMore:(BOOL)enableLoadMore
{
    if (_enableLoadMore != enableLoadMore) {
        _enableLoadMore = enableLoadMore;
        [_scrollViewLoadMoreControl setEnable:_enableLoadMore];
        if (_enableLoadMore) {
            [self.scrollViewLoadMoreControl setScrollView:self];
        }
    }
}

- (void)setScrollViewRefreshControl:(SPKScrollViewRefreshControl *)scrollViewRefreshControl
{
    if (_scrollViewRefreshControl != scrollViewRefreshControl) {
        _scrollViewRefreshControl = scrollViewRefreshControl;
        if (_scrollViewRefreshControl) {
            [_scrollViewRefreshControl addTarget:self action:@selector(refresh)];
        }
    }
}

- (void)setscrollViewLoadMoreControl:(SPKScrollViewLoadMoreControl *)scrollViewLoadMoreControl
{
    if (_scrollViewLoadMoreControl != scrollViewLoadMoreControl) {
        _scrollViewLoadMoreControl = scrollViewLoadMoreControl;
        if (_scrollViewLoadMoreControl) {
            [_scrollViewLoadMoreControl addTarget:self action:@selector(loadMore)];
        }
    }
}


#pragma - mark getter

- (SPKScrollViewRefreshControl *)scrollViewRefreshControl
{
    if (!_scrollViewRefreshControl) {
        _scrollViewRefreshControl = [[SPKScrollViewRefreshControl alloc] initWithFrame:CGRectMake(0, 0, self.width, 56)];
        [_scrollViewRefreshControl addTarget:self action:@selector(refresh)];
    }
    return _scrollViewRefreshControl;
}



- (SPKScrollViewLoadMoreControl *)scrollViewLoadMoreControl
{
    if (!_scrollViewLoadMoreControl) {
        _scrollViewLoadMoreControl = [[SPKScrollViewLoadMoreControl alloc] initWithFrame:CGRectMake(0, 0, self.width, 44)];
        [_scrollViewLoadMoreControl addTarget:self action:@selector(loadMore)];
    }
    return _scrollViewLoadMoreControl;
}


#pragma - mark private method

- (void)finishRefresh
{
    [self.scrollViewRefreshControl finishLoading];
    if (self.refreshedScrollsToTop) {
        [self setContentOffset:CGPointZero animated:YES];
    }
}

- (void)finishLoadMore
{
    [_scrollViewLoadMoreControl finishLoading];
}

- (void)refresh
{
    if (self.refreshCommand) {
        [self.refreshCommand execute:nil];
    }
}


- (void)loadMore
{
    if (self.loadMoreCommand) {
        [self.loadMoreCommand execute:nil];
    }
}

@end
