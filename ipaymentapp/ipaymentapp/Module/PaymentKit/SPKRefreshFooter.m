//
//  SPKRefreshFooter.m
//  SAKPaymentKit
//
//  Created by <PERSON> on 2018/1/11.
//

#import "SPKRefreshFooter.h"
#import "CIPSwizzle.h"

@interface SPKRefreshFooter ()

@end

@implementation SPKRefreshFooter

#pragma mark - 初始化
- (void)willMoveToSuperview:(UIView *)newSuperview
{
    [super willMoveToSuperview:newSuperview];
    
    if (newSuperview) {
        // 监听scrollView数据的变化
        if ([self.scrollView isKindOfClass:[UITableView class]] || [self.scrollView isKindOfClass:[UICollectionView class]]) {
            [self.scrollView setSpk_reloadDataBlock:^(NSInteger totalDataCount) {
                if (self.isAutomaticallyHidden) {
                    self.hidden = (totalDataCount == 0);
                }
            }];
        }
        if (self.hidden == NO) {
            self.scrollView.spk_insetB += self.height;
        }
        
        // 设置位置
        self.top = self.scrollView.spk_contentH;
    } else {
        if (self.hidden == NO) {
            self.scrollView.spk_insetB -= self.height;
        }
    }
}

#pragma mark - 构造方法
+ (instancetype)footerWithRefreshingBlock:(SPKRefreshComponentRefreshingBlock)refreshingBlock
{
    SPKRefreshFooter *cmp = [[self alloc] init];
    cmp.refreshingBlock = refreshingBlock;
    return cmp;
}
+ (instancetype)footerWithRefreshingTarget:(id)target refreshingAction:(SEL)action
{
    SPKRefreshFooter *cmp = [[self alloc] init];
    [cmp setRefreshingTarget:target refreshingAction:action];
    return cmp;
}

#pragma mark - 重写父类的方法
- (void)prepare
{
    [super prepare];
    
    // 设置自己的高度
    self.height = SPKRefreshFooterHeight;
    // 默认底部控件100%出现时才会自动刷新
    self.triggerAutomaticallyRefreshPercent = 1.0;
    self.automaticallyHidden = YES;
}

- (void)scrollViewContentSizeDidChange:(NSDictionary *)change
{
    [super scrollViewContentSizeDidChange:change];
    
    // 设置位置
    self.top = self.scrollView.spk_contentH;
}

- (void)scrollViewContentOffsetDidChange:(NSDictionary *)change
{
    [super scrollViewContentOffsetDidChange:change];
    
    if (self.state != SPKRefreshStateIdle || self.top == 0) return;
    
    if (self.scrollView.spk_insetT + self.scrollView.spk_contentH > self.scrollView.height) { // 内容超过一个屏幕
        if (self.scrollView.spk_offsetY >= self.scrollView.spk_contentH - self.scrollView.height + self.height * self.triggerAutomaticallyRefreshPercent + self.scrollView.spk_insetB - self.height) {
            // 防止手松开时连续调用
            CGPoint old = [change[@"old"] CGPointValue];
            CGPoint new = [change[@"new"] CGPointValue];
            if (new.y <= old.y) return;
            
            // 当底部刷新控件完全出现时，才刷新
            [self beginRefreshing];
        }
    }
}

- (void)scrollViewPanStateDidChange:(NSDictionary *)change
{
    [super scrollViewPanStateDidChange:change];
    
    if (self.state != SPKRefreshStateIdle) return;
    
    if (self.scrollView.panGestureRecognizer.state == UIGestureRecognizerStateEnded) {// 手松开
        if (self.scrollView.spk_insetT + self.scrollView.spk_contentH <= self.scrollView.height) {  // 不够一个屏幕
            if (self.scrollView.spk_offsetY >= - self.scrollView.spk_insetT) { // 向上拽
                [self beginRefreshing];
            }
        } else { // 超出一个屏幕
            if (self.scrollView.spk_offsetY >= self.scrollView.spk_contentH + self.scrollView.spk_insetB - self.scrollView.height) {
                [self beginRefreshing];
            }
        }
    }
}

- (void)setState:(SPKRefreshState)state
{
    SPKRefreshCheckState;
    
    if (state == SPKRefreshStateRefreshing) {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self executeRefreshingCallback];
        });
    } else if (state == SPKRefreshStateNoMoreData || state == SPKRefreshStateIdle) {
        if (SPKRefreshStateRefreshing == oldState) {
            if (self.endRefreshingCompletionBlock) {
                self.endRefreshingCompletionBlock();
            }
        }
    } else {
        //do nothing
    }
    
}

- (void)setHidden:(BOOL)hidden
{
    BOOL lastHidden = self.isHidden;
    
    [super setHidden:hidden];
    
    if (!lastHidden && hidden) {
        self.state = SPKRefreshStateIdle;
        
        self.scrollView.spk_insetB -= self.height;
    } else if (lastHidden && !hidden) {
        self.scrollView.spk_insetB += self.height;
        
        // 设置位置
        self.top = self.scrollView.spk_contentH;
    } else {
        //do nothing
    }
}

- (void)setAutomaticallyHidden:(BOOL)automaticallyHidden
{
    _automaticallyHidden = automaticallyHidden;
    
    if (automaticallyHidden) {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wundeclared-selector"
        static dispatch_once_t onceToken;
        dispatch_once(&onceToken, ^{
            [UITableView cipf_SwizzleMethod:@selector(reloadData) withMethod:@selector(spk_reloadData) error:nil];
            [UICollectionView cipf_SwizzleMethod:@selector(reloadData) withMethod:@selector(spk_reloadData) error:nil];
        });
#pragma clang diagnostic pop
    }
}

#pragma mark - 公共方法
- (void)endRefreshingWithNoMoreData
{
    dispatch_async(dispatch_get_main_queue(), ^{
        self.state = SPKRefreshStateNoMoreData;
    });
}

- (void)noticeNoMoreData
{
    [self endRefreshingWithNoMoreData];
}

- (void)resetNoMoreData
{
    dispatch_async(dispatch_get_main_queue(), ^{
        self.state = SPKRefreshStateIdle;
    });
}

@end
