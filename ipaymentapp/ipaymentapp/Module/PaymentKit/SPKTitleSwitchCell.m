//
//  MTWTitleSwitchCell.m
//  Pods
//
//  Created by sunhl on 15/7/5.
//
//

#import "SPKTitleSwitchCell.h"
#import <UIKit/UIKit.h>
#import <Masonry/Masonry.h>
#import "UIImage+SPKPayment.h"
#import "SPKUIKitMacros.h"

@interface SPKTitleSwitchCell ()

@property (nonatomic, strong) UIButton *maskButton;

@end

@implementation SPKTitleSwitchCell

- (id)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        _switchButton = ({
            UISwitch *switchButton = [[UISwitch alloc] initWithFrame:CGRectMake(0, 0, 64, 28)];
            switchButton.backgroundColor = [UIColor clearColor];
            switchButton.onTintColor = kSPKBaseThemeColor;
            switchButton;
        });
        [self addSubview:_switchButton];
        
        _maskButton = ({
            UIButton *button = [[UIButton alloc] init];
            [button addTarget:self action:@selector(maskButtonClicked:) forControlEvents:UIControlEventTouchUpInside];
            button;
        });
        [self addSubview:_maskButton];
        
        self.touchable = NO;
    }
    return self;
}

- (void)updateConstraints
{
    [self.switchButton mas_updateConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self);
        make.right.equalTo(self.mas_right).offset(-15);
    }];
    
    [self.maskButton mas_updateConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.switchButton.mas_right).offset(10);
        make.left.equalTo(self.switchButton.mas_left).offset(-10);
        make.top.equalTo(self);
        make.bottom.equalTo(self);
    }];
    
    [super updateConstraints];
}

#pragma mark - event

- (void)maskButtonClicked:(UIButton *)button
{
    if (self.switchButtonClickedBlock) {
        self.switchButtonClickedBlock();
    }
}

- (void)setSwitchButtonEnable:(BOOL)enable
{
    self.maskButton.hidden = !enable;
    self.switchButton.enabled = enable;
}

@end
