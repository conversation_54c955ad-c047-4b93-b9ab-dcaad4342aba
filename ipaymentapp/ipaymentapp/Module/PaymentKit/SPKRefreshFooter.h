//
//  SPKRefreshFooter.h
//  SAKPaymentKit
//
//  Created by <PERSON> on 2018/1/11.
//

#import "SPKRefreshComponent.h"

@interface SPKRefreshFooter : SPKRefreshComponent

/** 当底部控件出现多少时就自动刷新(默认为1.0，也就是底部控件完全出现时，才会自动刷新) */
@property (nonatomic, assign) CGFloat triggerAutomaticallyRefreshPercent;

/** 忽略多少scrollView的contentInset的bottom */
@property (nonatomic, assign) CGFloat ignoredScrollViewContentInsetBottom;

@property (nonatomic, assign, getter=isAutomaticallyHidden) BOOL automaticallyHidden;

+ (instancetype)footerWithRefreshingBlock:(SPKRefreshComponentRefreshingBlock)refreshingBlock;

+ (instancetype)footerWithRefreshingTarget:(id)target refreshingAction:(SEL)action;

// 提示没有更多的数据
- (void)endRefreshingWithNoMoreData;

// 重置没有更多的数据（消除没有更多数据的状态）
- (void)resetNoMoreData;

@end
