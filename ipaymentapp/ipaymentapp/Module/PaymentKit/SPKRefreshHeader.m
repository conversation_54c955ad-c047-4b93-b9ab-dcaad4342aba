//
//  SPKRefreshHeader.m
//  SAKPaymentKit
//
//  Created by <PERSON> on 2018/1/11.
//  参照 SPKRefresh

#import "SPKRefreshHeader.h"

@interface SPKRefreshHeader()

@property (nonatomic, assign) CGFloat insetTDelta;

@end

@implementation SPKRefreshHeader

#pragma mark - 构造方法
+ (instancetype)headerWithRefreshingBlock:(SPKRefreshComponentRefreshingBlock)refreshingBlock
{
    SPKRefreshHeader *cmp = [[self alloc] init];
    cmp.refreshingBlock = refreshingBlock;
    return cmp;
}

+ (instancetype)headerWithRefreshingTarget:(id)target refreshingAction:(SEL)action
{
    SPKRefreshHeader *cmp = [[self alloc] init];
    [cmp setRefreshingTarget:target refreshingAction:action];
    return cmp;
}

#pragma mark - 覆盖父类的方法
- (void)prepare
{
    [super prepare];
    
    // 设置高度
    self.height = SPKRefreshHeaderHeight;
}

- (void)placeSubviews
{
    [super placeSubviews];
    
    // 设置y值(当自己的高度发生改变了，肯定要重新调整Y值，所以放到placeSubviews方法中设置y值)
    self.top = - self.height - self.ignoredScrollViewContentInsetTop;
}

- (void)scrollViewContentOffsetDidChange:(NSDictionary *)change
{
    [super scrollViewContentOffsetDidChange:change];
    
    // 在刷新的refreshing状态
    if (self.state == SPKRefreshStateRefreshing) {

        // sectionheader停留解决
        CGFloat insetT = - self.scrollView.spk_offsetY > self.scrollViewOriginalInset.top ? - self.scrollView.spk_offsetY : self.scrollViewOriginalInset.top;
        insetT = insetT > self.height + self.scrollViewOriginalInset.top ? self.height + self.scrollViewOriginalInset.top : insetT;
        self.scrollView.spk_insetT = insetT;
        
        self.insetTDelta = self.scrollViewOriginalInset.top - insetT;
        return;
    }
    
    // 跳转到下一个控制器时，contentInset可能会变
    self.scrollViewOriginalInset = self.scrollView.spk_inset;
    
    // 当前的contentOffset
    CGFloat offsetY = self.scrollView.spk_offsetY;
    // 头部控件刚好出现的offsetY
    CGFloat happenOffsetY = - self.scrollViewOriginalInset.top;
    
    // 如果是向上滚动到看不见头部控件，直接返回
    if (offsetY > happenOffsetY) return;
    
    // 普通 和 即将刷新 的临界点
    CGFloat normal2pullingOffsetY = happenOffsetY - self.height;
    CGFloat pullingPercent = (happenOffsetY - offsetY) / self.height;
    
    if (self.scrollView.isDragging) { // 如果正在拖拽
        self.pullingPercent = pullingPercent;
        if (self.state == SPKRefreshStateIdle && offsetY < normal2pullingOffsetY) {
            // 转为即将刷新状态
            self.state = SPKRefreshStatePulling;
        } else if (self.state == SPKRefreshStatePulling && offsetY >= normal2pullingOffsetY) {
            // 转为普通状态
            self.state = SPKRefreshStateIdle;
        }
    } else if (self.state == SPKRefreshStatePulling) {// 即将刷新 && 手松开
        // 开始刷新
        [self beginRefreshing];
    } else if (pullingPercent < 1) {
        self.pullingPercent = pullingPercent;
    } else {
        //do nothing
    }
}

- (void)setState:(SPKRefreshState)state
{
    SPKRefreshCheckState;
    
    // 根据状态做事情
    if (state == SPKRefreshStateIdle) {
        if (oldState != SPKRefreshStateRefreshing) return;
        // 恢复inset和offset
        [UIView animateWithDuration:SPKRefreshSlowAnimationDuration animations:^{
            self.scrollView.spk_insetT += self.insetTDelta;
        } completion:^(BOOL finished) {
            self.pullingPercent = 0.0;
            // 自动调整透明度
            if (self.isAutomaticallyChangeAlpha) {
                self.alpha = 0.0;
            }
            if (self.endRefreshingCompletionBlock) {
                self.endRefreshingCompletionBlock();
            }
        }];
    } else if (state == SPKRefreshStateRefreshing) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [UIView animateWithDuration:SPKRefreshFastAnimationDuration animations:^{
                CGFloat top = self.scrollViewOriginalInset.top + self.height;
                // 增加滚动区域top
                self.scrollView.spk_insetT = top;
                // 设置滚动位置
                CGPoint offset = self.scrollView.contentOffset;
                offset.y = -top;
                [self.scrollView setContentOffset:offset animated:NO];
            } completion:^(BOOL finished) {
                [self executeRefreshingCallback];
            }];
        });
    } else {
        //do nothing
    }
}

@end
