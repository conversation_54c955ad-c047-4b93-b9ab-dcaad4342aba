//
//  METScrollViewLoadMoreControl.m
//  imeituan
//
//  Created by zjs on 14/9/10.
//  Copyright (c) 2014年 meituan.com. All rights reserved.
//

#import "SPKScrollViewLoadMoreControl.h"
#import "SAKUIKitMacros.h"
#import "Masonry.h"

@interface SPKScrollViewLoadMoreControl ()

@end

@implementation SPKScrollViewLoadMoreControl

- (id)initWithFrame:(CGRect)rect
{
    self = [super initWithFrame:rect];
    if (self) {
        self.threshold = 40;
        self.moreTextLabel.frame = CGRectZero;
        self.moreTextLabel.font = Font(13);
        self.moreTextLabel.textColor = HEXCOLOR(0xa7a5a0);
        self.moreTextLabel.textAlignment = NSTextAlignmentCenter;
        self.moreTextLabel.backgroundColor = [UIColor clearColor];
        self.moreTextLabel.numberOfLines = 0;
        self.moreTextLabel.highlightedTextColor = [UIColor whiteColor];
        self.moreTextLabel.autoresizingMask = UIViewAutoresizingFlexibleHeight | UIViewAutoresizingFlexibleWidth;
        [self addSubview:self.moreTextLabel];
        
        self.activeView.hidesWhenStopped = NO;
        [self addSubview:self.activeView];
    }
    return self;
}

- (void)sak_addConstraints
{
    [self.moreTextLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self).offset(15);
            make.bottom.equalTo(self).offset(-10);
            make.height.equalTo(@14);
        }];
    [self.activeView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.moreTextLabel.mas_left).offset(-10);
            make.centerY.equalTo(self.moreTextLabel);
            make.height.width.equalTo(@20);
        }];

}

+ (BOOL)requiresConstraintBasedLayout
{
    return YES;
}

@end
