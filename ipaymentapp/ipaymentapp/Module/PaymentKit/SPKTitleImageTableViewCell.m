//
//  SPKTitleImageTableViewCell.m
//  SAKPaymentKit
//
//  Created by <PERSON> on 2018/1/5.
//

#import "SPKTitleImageTableViewCell.h"
#import "Masonry.h"
#import "SPKUIKitMacros.h"

@implementation SPKTitleImageTableViewCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        _tipLabel = ({
            UILabel *label = [[UILabel alloc] init];
            label.textColor = kSPKDescriptionTextColor;
            label.font = Font(13);
            label;
        });
        [self.contentView addSubview:_tipLabel];
        
        _leftImageView = [[UIImageView alloc] init];
        _leftImageView.contentMode = UIViewContentModeScaleAspectFit;
        [self.contentView addSubview:_leftImageView];
    }
    return self;
}

+ (BOOL)requiresConstraintBasedLayout
{
    return YES;
}

- (void)updateConstraints
{
    [self.leftImageView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView.mas_left).offset(15);
        make.centerY.equalTo(self.contentView);
        make.height.width.mas_equalTo(24).priorityHigh();
    }];
    
    [self.titleLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.leftImageView.mas_right).with.offset(8).priorityHigh();
    }];
    
    if (self.customAccessoryVisible) {
        [self.tipLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.customAccessoryView.mas_left).offset(-10);
            make.centerY.equalTo(self.contentView);
            make.height.mas_equalTo(13);
        }];
    } else {
        [self.tipLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.mas_right).offset(-15);
            make.centerY.equalTo(self.contentView);
            make.height.mas_equalTo(13);
        }];
    }
    
    [super updateConstraints];
}

@end


