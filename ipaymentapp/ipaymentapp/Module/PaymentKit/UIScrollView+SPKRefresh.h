//
//  UIScrollView+SPKRefresh.h
//  SAKPaymentKit
//
//  Created by <PERSON> on 2018/1/10.
//

#import <UIKit/UIKit.h>

@class SPKRefreshHeader, SPKRefreshFooter;

@interface UIScrollView (SPKRefresh)

@property (nonatomic, readonly, assign) UIEdgeInsets spk_inset;

@property (nonatomic, assign) CGFloat spk_insetT;
@property (nonatomic, assign) CGFloat spk_insetB;
@property (nonatomic, assign) CGFloat spk_insetL;
@property (nonatomic, assign) CGFloat spk_insetR;

@property (nonatomic, assign) CGFloat spk_offsetX;
@property (nonatomic, assign) CGFloat spk_offsetY;

@property (nonatomic, assign) CGFloat spk_contentW;
@property (nonatomic, assign) CGFloat spk_contentH;

// 下拉刷新控件
@property (nonatomic,strong) SPKRefreshHeader *spk_header;
// 上拉刷新控件
@property (nonatomic,strong) SPKRefreshFooter *spk_footer;

//数据源所有数据
- (NSInteger)spk_totalDataCount;
//在 reload 后会调用这个方法
@property (copy, nonatomic) void (^spk_reloadDataBlock)(NSInteger totalDataCount);

@end
