//
//  PPAppSelectViewController.m
//  ipaymentapp
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 15/9/14.
//  Copyright (c) 2015年 Meituan.com. All rights reserved.
//

#import "PPAppSelectViewController.h"
#import "Masonry.h"
#import "SAKUIKitMacros.h"
#import "PPAppSelectTableViewCell.h"
#import "SPGObjectCacheManager.h"
#import "SAKAppInfo.h"
#import "SAKEnvironment.h"
#import "TKAlertCenter.h"
#import "SAKPaymentUIConfigure.h"
#import "SAKPaymentCommonConfigure.h"
#import "NVEnvironment.h"
#import "SPKUIKitMacros.h"
#import "UIImage+SPKPayment.h"
#import "UIColor+Addition.h"

static const CGFloat kCellHeight = 50;
NSString * const kPPAppSelectedKey = @"PPAppSelectedKey";

@interface PPAppSelectViewController () <UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UITableView *tableview;
@property (nonatomic, strong) NSArray *appArray;
@property (nonatomic, strong) NSArray *appNameArray;

@end

@implementation PPAppSelectViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.title = @"APP选择";
    self.view.backgroundColor = UIColor.generalBackgroundColor;
    _appArray = @[@"团购", @"猫眼", @"外卖", @"独立业务客户端", @"点评", @"小象生鲜",@"打车", @"新品牌色默认状态"];
    _appNameArray = @[@"group", @"movie", @"waimai", @"paymentapp", @"dianping-nova", @"retail_c",@"QCSC", @"newDefaultColor"];
    
    self.tableview = ({
        UITableView *tableview = [[UITableView alloc] init];
        tableview.backgroundColor = [UIColor clearColor];
        tableview.delegate = self;
        tableview.dataSource = self;
        [self.view addSubview:tableview];
        tableview.separatorStyle = UITableViewCellSeparatorStyleNone;
        tableview;
    });
    
    id nb_appName = [[SPGObjectCacheManager sharedCacheManager] objectForKey:kPPAppSelectedKey];
    NSUInteger indexRow = 0;
    if (nb_appName && [nb_appName isKindOfClass:[NSString class]]) {
        indexRow = [_appNameArray indexOfObject:nb_appName];
    }
    [self changeNBAppNameByIndex:indexRow];
    NSIndexPath *selectedPath = [NSIndexPath indexPathForItem:indexRow inSection:0];
    [self.tableview selectRowAtIndexPath:selectedPath animated:YES scrollPosition:UITableViewScrollPositionNone];
    
    [self.tableview registerClass:[PPAppSelectTableViewCell class] forCellReuseIdentifier:NSStringFromClass([PPAppSelectTableViewCell class])];
    
    [self.view setNeedsUpdateConstraints];
}

- (void)updateViewConstraints
{
    [self.tableview mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view);
        make.left.equalTo(self.view);
        make.right.equalTo(self.view);
        make.height.equalTo(@(kCellHeight * [self.appArray count]));
    }];
    
    [super updateViewConstraints];
}

#pragma mark - UITableViewDataSource

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return [self.appArray count];
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    PPAppSelectTableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass([PPAppSelectTableViewCell class])];
    if (!cell) {
        cell = [[PPAppSelectTableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:NSStringFromClass([PPAppSelectTableViewCell class])];
    }
    cell.titleLabel.text = self.appArray[indexPath.row];
    cell.titleLabel.textColor = [UIColor generalLabelTextColor];
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return kCellHeight;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [self changeNBAppNameByIndex:indexPath.row];
}

#pragma mark - private method

- (void)changeNBAppNameByIndex:(NSUInteger)index
{
    if (index <= [self.appNameArray count] - 1 && ![[SAKEnvironment environment].appInfo.type isEqualToString:self.appNameArray[index]]) {
        [SAKEnvironment environment].appInfo.type = self.appNameArray[index];
        [[SPGObjectCacheManager sharedCacheManager] saveObject:self.appNameArray[index] forKey:kPPAppSelectedKey];
        
        //选择接入方时 需要设置一下appName
        [SAKPaymentCommonConfigure shareConfigure].appName = self.appNameArray[index];
                                                              
        [PPAppSelectViewController clearConfigureColor];
        if ([self.appNameArray[index] isEqualToString:@"group"]) {
            [PPAppSelectViewController configureGroupColor];
        } else if ([self.appNameArray[index] isEqualToString:@"waimai"]) {
            [PPAppSelectViewController configureWaimaiColor];
        } else if ([self.appNameArray[index] isEqualToString:@"dianping-nova"]) {
            [PPAppSelectViewController configureDianPingColor];
        } else if ([self.appNameArray[index] isEqualToString:@"retail_c"]) {
            [PPAppSelectViewController configureXiaoXiangShengXianColor];
        } else {
            // notings to do
        }
    }
}

+ (void)configureGroupColor
{
    
}

+ (void)configureDianPingColor
{
    [SAKPaymentCommonConfigure shareConfigure].appName = @"dianping-nova";
    [SAKPaymentCommonConfigure shareConfigure].appChannel = @"AppStore";
    [SAKPaymentCommonConfigure shareConfigure].requestUUID = ^() {
        return [[NVEnvironment defaultEnvironment] dpid];
    };
    [SAKPaymentUIConfigure shareConfigure].uiBarButtonItemNormalTitleColor = kSPKNovaColor;
    [SAKPaymentUIConfigure shareConfigure].uiBarButtonItemHighlightedTitleColor = HEXCOLOR(0xffc2ad);
    [SAKPaymentUIConfigure shareConfigure].submitButtonNormalColor = kSPKNovaColor;
    [SAKPaymentUIConfigure shareConfigure].submitButtonHighlightedColor = kSPKNovaColor;
    [SAKPaymentUIConfigure shareConfigure].submitButtonDisabledColor = HEXCOLOR(0xcccccc);
    [SAKPaymentUIConfigure shareConfigure].submitNormalGradientButtonTitleColor = HEXCOLOR(0xffffff);
    [SAKPaymentUIConfigure shareConfigure].submitHighlightedGradientButtonTitleColor = HEXCOLOR(0xffffff);
    [SAKPaymentUIConfigure shareConfigure].submitDisabledGradientButtonTitleColor = HEXCOLOR(0xffffff);
    [SAKPaymentUIConfigure shareConfigure].defaultWalletBackgroundColor = kSPKNovaColor;
    [SAKPaymentUIConfigure shareConfigure].switchControlONTintColor = kSPKNovaColor;
    [SAKPaymentUIConfigure shareConfigure].radioButtonSelectedImage = [UIImage imageNamed:@"mtfu_icon_radio_on"];
    [SAKPaymentUIConfigure shareConfigure].radioButtonNormalImage = [UIImage imageNamed:@"mtfu_icon_radio"];
    [SAKPaymentUIConfigure shareConfigure].radioButtonDisabledImage = [UIImage imageNamed:@"mtfu_icon_radio_unclickable"];
}

+ (void)configureWaimaiColor
{
    [SAKPaymentUIConfigure shareConfigure].submitButtonNormalColor = HEXCOLOR(0xF5D900);
    [SAKPaymentUIConfigure shareConfigure].submitButtonHighlightedColor = HEXCOLOR(0xF5D900);
    [SAKPaymentUIConfigure shareConfigure].submitButtonDisabledColor = HEXCOLOR(0xF5D900);
    
    [SAKPaymentUIConfigure shareConfigure].submitNormalButtonTitleColor = HEXCOLOR(0x624705);
    [SAKPaymentUIConfigure shareConfigure].submitHighlightedButtonTitleColor = HEXCOLOR(0x624705);
    [SAKPaymentUIConfigure shareConfigure].submitDisabledButtonTitleColor = HEXCOLOR(0x624705);
    
    [SAKPaymentUIConfigure shareConfigure].uiBarButtonItemNormalTitleColor = HEXCOLOR(0xF5D900);
    [SAKPaymentUIConfigure shareConfigure].uiBarButtonItemHighlightedTitleColor = HEXCOLOR(0xF5D900);
}

+ (void)configureXiaoXiangShengXianColor
{
    [SAKPaymentUIConfigure shareConfigure].barcodeScanBackgroundLayerBeginColor = HEXCOLOR(0xDDA364);
    [SAKPaymentUIConfigure shareConfigure].barcodeScanBackgroundLayerEndColor = HEXCOLOR(0xB97542);
    [SAKPaymentUIConfigure shareConfigure].barcodeBrandColor = HEXCOLOR(0xE7B06D);
    [SAKPaymentUIConfigure shareConfigure].barcodeForegroundColor = HEXCOLOR(0xFFFFFF);
}

+ (void)clearConfigureColor
{
    [SAKPaymentUIConfigure shareConfigure].submitButtonNormalColor = nil;
    [SAKPaymentUIConfigure shareConfigure].submitButtonHighlightedColor = nil;
    [SAKPaymentUIConfigure shareConfigure].submitButtonDisabledColor = nil;
    
    [SAKPaymentUIConfigure shareConfigure].submitNormalButtonTitleColor = nil;
    [SAKPaymentUIConfigure shareConfigure].submitHighlightedButtonTitleColor = nil;
    [SAKPaymentUIConfigure shareConfigure].submitDisabledButtonTitleColor = nil;
    
    [SAKPaymentUIConfigure shareConfigure].uiBarButtonItemNormalTitleColor = nil;
    [SAKPaymentUIConfigure shareConfigure].uiBarButtonItemHighlightedTitleColor = nil;
    
    //将付款码的数据都赋值为nil
    [SAKPaymentUIConfigure shareConfigure].barcodeScanBackgroundLayerBeginColor = nil;
    [SAKPaymentUIConfigure shareConfigure].barcodeScanBackgroundLayerEndColor = nil;
    [SAKPaymentUIConfigure shareConfigure].barcodeChangeLabelTitleColor = nil;
    [SAKPaymentUIConfigure shareConfigure].barcodeBackButtonImage = nil;
    [SAKPaymentUIConfigure shareConfigure].barcodeTitleBottomLabelPaymentLabelTextColor = nil;
    [SAKPaymentUIConfigure shareConfigure].barcodeRightButtonImage = nil;
    [SAKPaymentUIConfigure shareConfigure].barcodePaymentIconRefreshImage = nil;
    [SAKPaymentUIConfigure shareConfigure].barcodePaymentIconRefreshUpdatedImage = nil;
    [SAKPaymentUIConfigure shareConfigure].barcodeForegroundColor = nil;
    [SAKPaymentUIConfigure shareConfigure].barcodeBrandColor = nil;
}

/// App 启动初始化主题样式
+ (void)setupAppThemeStyle
{
    NSString *nb_appName = [[SPGObjectCacheManager sharedCacheManager] objectForKey:kPPAppSelectedKey];
   
    [SAKPaymentCommonConfigure shareConfigure].appName = nb_appName;
    [PPAppSelectViewController clearConfigureColor];
    
    if ([nb_appName isEqualToString:@"group"]) {
        [PPAppSelectViewController configureGroupColor];
    } else if ([nb_appName isEqualToString:@"waimai"]) {
        [PPAppSelectViewController configureWaimaiColor];
    } else if ([nb_appName isEqualToString:@"dianping-nova"]) {
        [PPAppSelectViewController configureDianPingColor];
    } else if ([nb_appName isEqualToString:@"retail_c"]) {
        [PPAppSelectViewController configureXiaoXiangShengXianColor];
    } else {
        
    }
}

@end
