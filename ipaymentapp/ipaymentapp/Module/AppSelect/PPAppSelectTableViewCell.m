//
//  PPAppSelectTableViewCell.m
//  ipaymentapp
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 15/9/14.
//  Copyright (c) 2015年 Meituan.com. All rights reserved.
//

#import "PPAppSelectTableViewCell.h"
#import "SAKUIKitMacros.h"
#import "Masonry.h"
#import "UIColor+Addition.h"

@interface PPAppSelectTableViewCell ()

@property (nonatomic, strong) UIImageView *iconImageView;
@property (nonatomic, strong) UIImage *selectedImage;
@property (nonatomic, strong) UIImage *unSelectedImage;

@end

@implementation PPAppSelectTableViewCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.accessoryType = UITableViewCellAccessoryNone;
        _titleLabel = ({
            UILabel *label = [[UILabel alloc] init];
            label.textColor = UIColor.generalBackgroundColor;
            label.font = Font(15);
            [self addSubview:label];
            label;
        });
        
        _selectedImage = [UIImage imageNamed:@"btn_icon_selected"];
        _unSelectedImage = [UIImage imageNamed:@"btn_icon_unselected"];
        
        _iconImageView = ({
            UIImageView *imageView = [[UIImageView alloc] init];
            imageView.image = _unSelectedImage;
            [self addSubview:imageView];
            imageView;
        });
    }
    return self;
}

+ (BOOL)requiresConstraintBasedLayout
{
    return YES;
}

- (void)updateConstraints
{
    [self.iconImageView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self).offset(10);
        make.centerY.equalTo(self);
    }];
    
    [self.titleLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.iconImageView.mas_right).offset(10);
        make.centerY.equalTo(self);
        make.height.equalTo(@16);
    }];
    
    [super updateConstraints];
}

- (void)awakeFromNib {
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];
    
    self.iconImageView.image = selected ? self.selectedImage : self.unSelectedImage;
}

@end
