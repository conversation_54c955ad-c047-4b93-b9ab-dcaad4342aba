//
//  PPOpenPayDebugViewController.m
//  ipaymentapp
//
//  Created by zhangyang on 2018/5/24.
//  Copyright © 2018年 Meituan.com. All rights reserved.
//

#import "PPOpenPayDebugViewController.h"
#import "Masonry.h"
#import "SAKPortal.h"
#import "SPKFoundationMacros.h"
#import "SPKToastCenter.h"
#import "PPUIKitViewController.h"
#import "SPKAlertView.h"
#import "PPConfigTableViewCell.h"
#import "PPQRCodeScanViewController.h"
#import "UIColor+Addition.h"

static const CGFloat kPPOpenPayCellHeight = 50;

@interface PPOpenPayDebugViewController () <UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UITableView *tableview;
@property (nonatomic, strong) NSArray *configArray;

@end

@implementation PPOpenPayDebugViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self.view setBackgroundColor:[UIColor generalBackgroundColor]];
    self.title = @"一键绑卡";
    
    self.configArray = @[@"招行绑卡",@"平安银行绑卡"];
    
    self.tableview = ({
        UITableView *tableview = [[UITableView alloc] init];
        tableview.backgroundColor = [UIColor clearColor];
        tableview.delegate = self;
        tableview.dataSource = self;
        [self.view addSubview:tableview];
        tableview;
    });
#pragma deploymate push "ignored-api-availability"
    if (@available(iOS 11.0, *)) {
        self.tableview.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    }
#pragma deploymate pop
    [self.tableview registerClass:[PPConfigTableViewCell class] forCellReuseIdentifier:NSStringFromClass([PPConfigTableViewCell class])];
    
    [self.view setNeedsUpdateConstraints];
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

- (void)updateViewConstraints
{
    [self.tableview mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view);
        make.left.equalTo(self.view);
        make.right.equalTo(self.view);
        make.bottom.equalTo(self.view);
    }];
    
    [super updateViewConstraints];
}

#pragma mark - UITableViewDataSource

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return [self.configArray count];
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    PPConfigTableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass([PPConfigTableViewCell class])];
    if (!cell) {
        cell = [[PPConfigTableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:NSStringFromClass([PPConfigTableViewCell class])];
    }
    cell.titleLabel.text = self.configArray[indexPath.row];
    cell.switchShow = NO;
    cell.accessoryType = UITableViewCellAccessoryDisclosureIndicator;
    
    return cell;
}

#pragma mark - UITableViewDelegate

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return kPPOpenPayCellHeight;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    if (indexPath.row == 0) {
        // 招行一键绑卡
        PPQRCodeScanViewController* scanVC = [[PPQRCodeScanViewController alloc] init];
        @weakify(scanVC)
        [scanVC setCodeScanSucceed:^(NSURL *resultURL) {
            @strongify(scanVC)
            [SAKPortal transferFromViewController:self toURL:resultURL completion:^(UIViewController<SAKPortalable> * _Nullable viewController, NSError * _Nullable error) {

            }];
            [scanVC dismissViewControllerAnimated:NO completion:nil];
        }];

        [scanVC setCodeScanFailed:^{
            @strongify(scanVC)
            [scanVC dismissViewControllerAnimated:NO completion:nil] ;
        }];

        [self presentViewController:scanVC animated:YES completion:nil];
        
    } else if (indexPath.row == 1) {
        // 平安银行一键绑卡
        [[UIApplication sharedApplication] openURL:[NSURL URLWithString:@"imeituan://payment/launch?page_id=1000001&callback_url=ipaymentapp://"]];
    }
    
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
}

@end


