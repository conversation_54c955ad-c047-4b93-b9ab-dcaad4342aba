

#import "MeituanPaySDK.h"

@interface MeituanPaySDK ()


@end

static MeituanPaySDK *defaultInstance;

@implementation MeituanPaySDK : NSObject

+ (instancetype)sharedInstance
{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        defaultInstance = [[self alloc] init];
    });
    return defaultInstance;
}


+ (void)startMeituanPayWithTradeNumber:(NSString *)tradeNumber
                              payToken:(NSString *)payToken
                            completion:(MeituanPaySDKCompletion)completion
{
    [MeituanPaySDK sharedInstance].completeBlock = completion;
    
    NSString *urlString = [NSString stringWithFormat:@"imeituan://payment/launch?page_id=1000002&callback_url=%@&trans_id=%@&pay_token=%@",[self currentAppMeituanPaySDKScheme],tradeNumber,payToken];
    NSURL* payUrl = [NSURL URLWithString:urlString];
    if ([self isSupportMeituanPay]) {
        [[UIApplication sharedApplication] openURL:payUrl];
    }
}

// 美团支付SDK为App分配的Scheme
+ (NSString *)currentAppMeituanPaySDKScheme
{
    NSString *scheme;
    NSArray *URLTypeArray = [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleURLTypes"];
    for (NSDictionary *anURLType in URLTypeArray) {
        if ([[anURLType objectForKey:@"CFBundleURLName"] isEqualToString:@"meituanpaysdk"]) {
            scheme = [[anURLType objectForKey:@"CFBundleURLSchemes"] objectAtIndex:0];
            break;
        }
    }
    return scheme;
}

// 判断是否从 美团App调起的
+ (BOOL)isFromMeituanPaySDKWithURL:(NSURL *)URL
{
    NSString *scheme = [[URL scheme] lowercaseString];
    return [scheme isEqualToString:[self currentAppMeituanPaySDKScheme]];
}

// 判断是否安装了 美团App
+ (BOOL)isSupportMeituanPay
{
    NSString* fullUrl = [NSString stringWithFormat:@"imeituan://"];
    NSURL* url = [NSURL URLWithString:fullUrl];
    if([[UIApplication sharedApplication] canOpenURL:url]) {
        return YES;
    }
    return NO;
}

+ (BOOL)meituanPaySDKHanldeApplication:(UIApplication *)application
                                openURL:(NSURL *)url
{
    if ([self isFromMeituanPaySDKWithURL:url]) {
        if ([MeituanPaySDK sharedInstance].completeBlock) {
            [MeituanPaySDK sharedInstance].completeBlock(nil, nil);
        }
    } else {
        return NO;
    }
    return YES;
}



@end
