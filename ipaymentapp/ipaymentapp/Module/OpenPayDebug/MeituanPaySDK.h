

#import <Foundation/Foundation.h>
#import "MeituanPaySDKDefine.h"


@interface MeituanPaySDK : NSObject

@property (nonatomic, copy)MeituanPaySDKCompletion completeBlock;

+ (instancetype)sharedInstance;

+ (void)startMeituanPayWithTradeNumber:(NSString *)tradeNumber
                              payToken:(NSString *)payToken
                            completion:(MeituanPaySDKCompletion)completion;

// 判断是否从 美团App调起的
+ (BOOL)isFromMeituanPaySDKWithURL:(NSURL *)URL;

// 判断是否安装了 美团App
+ (BOOL)isSupportMeituanPay;

// 处理回调
+ (BOOL)meituanPaySDKHanldeApplication:(UIApplication *)application
                                openURL:(NSURL *)url;
@end
