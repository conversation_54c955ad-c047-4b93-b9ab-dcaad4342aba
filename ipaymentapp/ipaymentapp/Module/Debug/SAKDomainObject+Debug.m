//
//  SAKDomainObject+Debug.m
//  ipaymentapp
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2017/8/4.
//  Copyright © 2017年 Meituan.com. All rights reserved.
//
# if DEBUG || TEST

#import "SAKDomainObject+Debug.h"
#import "RPJSONValidator.h"
#import "CIPSwizzle.h"


static NSString *const SAKDomainObjectErrorDomain = @"com.meituan.error.domain.object";
typedef NS_ENUM(NSInteger, SAKDomainObjectlErrorCode) {
    SAKDomainObjectAdapterErrorCode = 20,
    SAKDomainObjectValidErrorCode = 21,
};

@implementation SAKDomainObject (Debug)

+ (void)load
{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        SEL originalSelector = @selector(domainWithJSONDictionary:error:);
        SEL swizzledSelector = @selector(debugDomainWithJSONDictionary:error:);
        [self cipf_SwizzleClassMethod:originalSelector withClassMethod:swizzledSelector error:nil];
    });
}

+ (instancetype)debugDomainWithJSONDictionary:(NSDictionary *)dictionary error:(CIPError *__autoreleasing *)error
{
    SAKDomainObject *instance = nil;

    CIPError *localError = nil;
    if (error) {
       instance = [self debugDomainWithJSONDictionary:dictionary error:error];
        NSAssert(*error == nil, @"数据校验失败！请检查下发的数据的类型是否符合预期");
    } else {
        instance = [self debugDomainWithJSONDictionary:dictionary error:&localError];
//        NSAssert(localError == nil, @"数据校验失败！请检查下发的数据的类型是否符合预期");
    }
    
    return instance;
}

@end

# endif
