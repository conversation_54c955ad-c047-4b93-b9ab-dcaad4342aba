//
//  METRiskErrorProcessController.m
//  imeituan
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 7/14/15.
//  Copyright (c) 2015 meituan.com. All rights reserved.
//

#import "PPRiskErrorProcessController.h"
#import "MTSignInViewController.h"

#import <SAKUserService.h>
#import <SAKRiskError.h>
#import <SAKRiskControlDefinition.h>
#import "SAKRiskControlManager.h"
#import "SAKSignInService.h"
#import "SAKEnvironment.h"
#import "DSActivityView.h"
#import "SPKAlertView.h"
#import "SPGDataStorageManager.h"
#import "PPAccountController.h"

@interface PPRiskErrorProcessController ()

@property (nonatomic, strong) UIViewController *navigationController;
@property (nonatomic, strong) SAKRiskError *currentError;
@property (nonatomic, strong) RACDisposable *disposableNotification;

@end

@implementation PPRiskErrorProcessController

#pragma mark - Error handlers

- (BOOL)handleRiskError:(SAKRiskError *)error
       inViewController:(UIViewController *)navigationController
         withCompletion:(PPRiskErrorProcessedBlock)completion
{
    // 进入风控后, 保证所有的loading页面都消失
    [DSActivityView removeView];
    self.navigationController = navigationController;
    self.currentError = error;
    
    // 如果直接用 self.errorType，default 处理就没有意义了
    switch (error.code) {
        case SAKRiskErrorTypeExpired:
            [self handleLoginExpiredWithCompletion:completion];
            break;
            
        case SAKRiskErrorTypeLockedUnion:
            [self handleLockedUnionWithCompletion:completion];
            break;
            
        case SAKRiskErrorTypeLockedMobile:
            [self handleLockedMobileWithCompletion:completion];
            break;
            
        case SAKRiskErrorTypeLockedEmail:
            [self handleLockedEmailWithCompletion:completion];
            break;
            
        case SAKRiskErrorTypeLoginBanned:
            [self handleLoginBannedWithCompletion:completion];
            break;
            
        default:
            NSAssert(false, @"Error code here should be a risk error code!");
            [self logoutUser];
            completion(@(error.code));
            
            break;
    }
    
    return YES;
}

- (void)handleLoginExpiredWithCompletion:(PPRiskErrorProcessedBlock)completion
{
    [self logoutUser];
    @weakify(self);
    [SPKAlertView showAlertViewWithTitle:@"提示"
                                         message:@"登录信息已经失效，请重新登录"
                               cancelButtonTitle:@"取消"
                           completionButtonTitle:@"确定"
                                        canceled:^{
                                            if (completion) {
                                                completion(@(SAKRiskErrorTypeRestartNotRequired));
                                            }
                                        }
                                      completion:^{
                                            @strongify(self);
                                            UIViewController *viewControllerToPresent = self.navigationController.presentedViewController;
                                            if (!viewControllerToPresent) {
                                                viewControllerToPresent = self.navigationController.presentingViewController;
                                            }
                                            
                                            if (viewControllerToPresent) {
                                                [[PPAccountController defaultInstance] loginWith:viewControllerToPresent completedBlock:nil];
                                            } else {
                                                [[PPAccountController defaultInstance] loginWith:self.navigationController completedBlock:nil];
                                            }
                                        }];
}

- (void)handleLockedUnionWithCompletion:(PPRiskErrorProcessedBlock)completion
{
    [self logoutUser];
    [SPKAlertView showAlertViewWithTitle:nil
                                 message:@"发现您的账号异常，请拨打客服电话********咨询解决"
                       cancelButtonTitle:@"取消"
                   completionButtonTitle:@"拨打电话"
                                canceled:^{
                                    if (completion) {
                                        completion(@(SAKRiskErrorTypeRestartNotRequired));
                                    }
                                } completion:^{
                                    [self callCustomerService];
                                    if (completion) {
                                        completion(@(SAKRiskErrorTypeRestartNotRequired));
                                    }
                                }];
}

- (void)handleLockedMobileWithCompletion:(PPRiskErrorProcessedBlock)completion
{
    if (self.navigationController.presentedViewController) {
        [self.navigationController dismissViewControllerAnimated:YES completion:nil];
    }
    [SPKAlertView showAlertViewWithTitle:nil message:@"检测到您的账号存在安全隐患，为了保护您的账号安全，已将您的账号锁定"
                       cancelButtonTitle:@"取消"
                   completionButtonTitle:@"去解锁"
                                canceled:^{
                                    [self logoutUser];
                                    completion(@(SAKRiskErrorTypeRestartNotRequired));
                                } completion:^{
                                    
                                    
                                }];
}

- (void)quitUnlockingProcess
{
    [self.navigationController dismissViewControllerAnimated:YES completion:nil];
    [self logoutUser];
}

- (void)handleLockedEmailWithCompletion:(PPRiskErrorProcessedBlock)completion
{
    [self logoutUser];
    [SPKAlertView showAlertViewWithMessage:@"发现您的账号异常，已自动为您锁定账户，请使用电脑访问www.meituan.com进行解锁"
                             completionButtonTitle:@"知道了"
                                        completion:^{
                                            if (completion) {
                                                completion(@(SAKRiskErrorTypeRestartNotRequired));
                                            }
                                            // 不提供解锁功能
                                        }];
}

- (void)handleLoginBannedWithCompletion:(PPRiskErrorProcessedBlock)completion
{
    [self logoutUser];
    [SPKAlertView showAlertViewWithTitle:nil message:@"您的账号存在安全隐患，请拨打客服电话********咨询解决"
                       cancelButtonTitle:@"取消"
                   completionButtonTitle:@"拨打电话"
                                canceled:^{
                                    if (completion) {
                                        completion(@(SAKRiskErrorTypeRestartNotRequired));
                                    }
                                }
                              completion:^{
                                  [self callCustomerService];
                                  if (completion) {
                                      completion(@(SAKRiskErrorTypeRestartNotRequired));
                                  }
                              }];
}

#pragma mark - Actions

- (void)callCustomerService
{
    [[UIApplication sharedApplication] openURL:[NSURL URLWithString:[NSString stringWithFormat:@"tel://%@", @"********"]]];
}

- (void)logoutUser
{
    [[SAKUserService sharedUserService] userWantLogout];
    [[SAKRiskControlManager sharedManager] resetRiskControlState];
    [self resetCurrentViewHierarchy];
}

- (void)resetCurrentViewHierarchy
{
    [(MTNavigationController *)self.navigationController popToRootViewControllerAnimated:NO];
    
    if ([(MTNavigationController *)self.navigationController presentedViewController]) {
        [(MTNavigationController *)self.navigationController dismissViewControllerAnimated:NO completion:nil];
    }
}


@end
