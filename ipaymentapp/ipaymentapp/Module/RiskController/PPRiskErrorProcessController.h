//
//  METRiskErrorProcessController.h
//  imeituan
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 7/14/15.
//  Copyright (c) 2015 meituan.com. All rights reserved.

//  Copy from imeituan, modify to apply for waimai by hujinzang

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

typedef void (^PPRiskErrorProcessedBlock)(NSNumber *);

@class SAKRiskError;
@interface PPRiskErrorProcessController : NSObject

- (BOOL)handleRiskError:(SAKRiskError *)error
       inViewController:(UIViewController *)homeViewController
         withCompletion:(PPRiskErrorProcessedBlock)completion;

@end
