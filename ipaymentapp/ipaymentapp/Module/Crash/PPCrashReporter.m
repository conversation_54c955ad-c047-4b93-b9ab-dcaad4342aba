//
//  PPCrashReporter.m
//  ipaymentapp
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2018/1/15.
//  Copyright © 2018年 Meituan.com. All rights reserved.
//

#import "PPCrashReporter.h"
#import "MessageUI/MFMailComposeViewController.h"
#import "SPGDataStorageManager.h"
#import "SPKAlertView.h"
#import "SCRCrashReporter.h"

static NSString * const kPPCrashLogFileKey = @"kPPCrashLog"; // crash log 本地存储的 key

@interface PPCrashReporter () <MFMailComposeViewControllerDelegate>

@end

@implementation PPCrashReporter

static void UncaughtExceptionHandler(NSException *exception) {
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
    [dateFormatter setDateFormat:@"yyyy-MM-dd HH:mm:ss"];
    [dateFormatter setTimeZone:[NSTimeZone timeZoneForSecondsFromGMT:8 * 60 * 60]];
    NSString *datetime = [dateFormatter stringFromDate:[NSDate date]];
    
    NSString *crashInfo = [NSString stringWithFormat:
                           @"<b style='color: red;'>程序上次退出是由 Crash 导致的，请告诉我们你在哪个页面、做了什么，这对我们非常重要，谢谢！</b><br />"
                           "<br />"
                           "<br />"
                           "-------- Crash --------<br />"
                           "<b>Device Name:</b> %@<br />"
                           "<b>iOS Version:</b> %@<br />"
                           "<b>Crash Date Time:</b> %@<br />"
                           "================================<br />"
                           "<b>Name:</b> %@<br />"
                           "<b>Reason:</b> %@<br />"
                           "<b>UserInfo:</b> %@<br />"
                           "--------------------------------<br />"
                           "<b>CallStackSymbols:</b> <br />"
                           "%@<br />"
                           "--------------------------------<br />"
                           "<b>CallStackReturnAddresses:</b> <br />"
                           "%@<br />",
                           [UIDevice currentDevice].name,
                           [UIDevice currentDevice].systemVersion,
                           datetime,
                           [exception name],
                           [exception reason],
                           [exception userInfo],
                           [[exception callStackSymbols] componentsJoinedByString:@"<br />"],
                           [[exception callStackReturnAddresses] componentsJoinedByString:@"<br />"]];
    if (crashInfo.length > 0) {
        [[SPGDataStorageManager sharedStorageManager] saveString:crashInfo
                                                          forKey:kPPCrashLogFileKey
                                                 storageLocation:SPGDataStorageLocationUserDefaults
                                                   shouldEncrypt:NO
                                                  updateExisting:NO
                                                           error:nil];
    }
}

+ (void)setupCrashReport {
    NSSetUncaughtExceptionHandler(&UncaughtExceptionHandler); // 设置系统捕获异常 handler
    [[SCRCrashReporter sharedReporter] startWithAppName:@"ipaymentapp" token:@"" onCompletion:^(NSArray *reports, BOOL completed, NSError *error) {
        
    }];
}

+ (NSString *)getCrashLog {
    NSString *crashInfo = [[SPGDataStorageManager sharedStorageManager] stringForKey:kPPCrashLogFileKey
                                                                     storageLocation:SPGDataStorageLocationUserDefaults
                                                                       shouldDecrypt:NO
                                                                               error:nil];
    return crashInfo;
}

+ (void)deleteCrashLog {
    NSString *crashInfo = [[SPGDataStorageManager sharedStorageManager] stringForKey:kPPCrashLogFileKey
                                                                     storageLocation:SPGDataStorageLocationUserDefaults
                                                                       shouldDecrypt:NO
                                                                               error:nil];
    if (crashInfo.length > 0) {
        [[SPGDataStorageManager sharedStorageManager] deleteStringForKey:kPPCrashLogFileKey
                                                         storageLocation:SPGDataStorageLocationUserDefaults];
    }
}

@end
