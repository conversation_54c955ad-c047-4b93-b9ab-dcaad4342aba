//
//  PPHelloPayController.swift
//  ipaymentapp
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2018/3/2.
//  Copyright © 2018年 Meituan.com. All rights reserved.
//

import Foundation

class PPHelloPayController : NSObject, MTPPaymentDelegate {
    
    weak var submitViewController : PPSubmitOrderViewController?
    
    func mtpayment(_ payment: MTPPayment?, didFailWith object: Any?) {
        submitViewController?.updateOutNoCell()
        TKAlertCenter.default().postAlert(withMessage: "ipaymentapp:支付失败")
    }
    
    @objc func mtpayment(_ payment: MTPPayment?, didFinishWith object: Any?) {
        submitViewController?.updateOutNoCell()
        TKAlertCenter.default().postAlert(withMessage: "ipaymentapp:支付成功")
    }
    
    func mtpaymentDidFinish(_ payment: MTPPayment!) {
        submitViewController?.updateOutNoCell()
        TKAlertCenter.default().postAlert(withMessage: "ipaymentapp:支付成功")
    }
    
    func mtpaymentDidCancel(_ payment: MTPPayment?) {
        submitViewController?.updateOutNoCell()
    }
    
    func mtpaymentDidPayed(_ payment: MTPPayment?) {
        submitViewController?.updateOutNoCell()
    }
    
    
}
