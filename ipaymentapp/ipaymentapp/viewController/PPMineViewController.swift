//
//  PPMineViewController.swift
//  ipaymentapp
//
//  Created by qin<PERSON><PERSON> on 2018/3/6.
//  Copyright © 2018年 Meituan.com. All rights reserved.
//

import UIKit

@objcMembers
class PPMineViewController: MTBaseViewController {
    // MARK: - Property
    private let accountView = PPMyAccountView()
    private let contentView = UIScrollView()
    private let orderListCell: PPMyAccountCell = {
        let view = PPMyAccountCell()
        view.titleString = "订单列表"
        view.icon = UIImage(named: "icon_group_order.png")
        return view
    }()
    private let refundedListCell: PPMyAccountCell = {
        let view = PPMyAccountCell()
        view.titleString = "退款列表"
        view.icon = UIImage(named: "icon_group_order.png")
        return view
    }()
    private let myWalletCell: PPMyAccountCell = {
        let view = PPMyAccountCell()
        view.titleString = "美团钱包"
        view.icon = UIImage(named: "icon_mine_mywallet.png")
        return view
    }()
    private let barcodeCashierCell: PPMyAccountCell = {
        let view = PPMyAccountCell()
        view.titleString = "付款码"
        view.icon = UIImage(named: "icon_group_order.png")
        return view
    }()
    private let barcodeCashierHasParametersCell: PPMyAccountCell = {
        let view = PPMyAccountCell()
        view.titleString = "付款码scheme带参数"
        view.icon = UIImage(named: "icon_group_order.png")
        return view
    }()
    private let quickpassBarcodeCashierCell: PPMyAccountCell = {
        let view = PPMyAccountCell()
        view.titleString = "银联付款码"
        view.icon = UIImage(named: "icon_group_order.png")
        return view
    }()
    private let balanceCell: PPMyAccountCell = {
        let view = PPMyAccountCell()
        view.titleString = "余额"
        view.icon = UIImage(named: "icon_group_order.png")
        return view
    }()
    private let paymentKitCell: PPMyAccountCell = {
        let view = PPMyAccountCell()
        view.titleString = "基础平台"
        view.icon = UIImage(named: "icon_group_order.png")
        return view
    }() // 基础平台测试入口
    private let uiKitCell: PPMyAccountCell = {
        let view = PPMyAccountCell()
        view.titleString = "UI 组件"
        view.icon = UIImage(named: "icon_group_order.png")
        return view
    }() // UIKit 测试入口
    private let uiKit2Cell: PPMyAccountCell = {
        let view = PPMyAccountCell()
        view.titleString = "UI 组件 2.0"
        view.icon = UIImage(named: "icon_group_order.png")
        return view
    }() // UIKit 2.0版本 测试入口
    private let financeKitCell: PPMyAccountCell = {
        let view = PPMyAccountCell()
        view.titleString = "身份验证"
        view.icon = UIImage(named: "icon_group_order.png")
        return view
    }() // 金融平台测试入口
    private let bizFacePaymentCell: PPMyAccountCell = {
        let view = PPMyAccountCell()
        view.titleString = "B端刷脸支付"
        view.icon = UIImage(named: "icon_group_order.png")
        return view
    }() // B端刷脸支付
    private let bizFacePaymentPicassoCell: PPMyAccountCell = {
        let view = PPMyAccountCell()
        view.titleString = "B端刷脸Picasso"
        view.icon = UIImage(named: "icon_group_order.png")
        return view
    }() // B端刷脸Picasso
    private let mrnCell: PPMyAccountCell = {
        let view = PPMyAccountCell()
        view.titleString = "MRN 测试"
        view.icon = UIImage(named: "icon_group_order.png")
        return view
    }() // MRN
    private let MWalletMRNCell : PPMyAccountCell = {
        let view = PPMyAccountCell()
        view.titleString = "商家钱包信息化入口 MRN 测试"
        view.icon = UIImage(named: "icon_group_order.png")
        return view
    }() // 商家钱包信息化入口 MRN Debug
    private let meshCell: PPMyAccountCell = {
        let view = PPMyAccountCell()
        view.titleString = "Mesh 测试"
        view.icon = UIImage(named: "icon_group_order.png")
        return view
    }() // Mesh
    private let picassoDebugCell: PPMyAccountCell = {
        let view = PPMyAccountCell()
        view.titleString = "Picasso 调试"
        view.icon = UIImage(named: "icon_group_order.png")
        return view
    }() // 金融平台测试入口
    private let openPayDebugCell: PPMyAccountCell = {
        let view = PPMyAccountCell()
        view.titleString = "通用半页容器"
        view.icon = UIImage(named: "icon_group_order.png")
        return view
    }() // 一键绑卡测试入口
    private let viewModel = PPMineViewModel()
    
    override func viewDidLoad()
    {
        super.viewDidLoad()
        setupUI()
        bindViewModel()
        // Do any additional setup after loading the view.
    }
    
    override func didReceiveMemoryWarning()
    {
        super.didReceiveMemoryWarning()
        // Dispose of any resources that can be recreated.
    }
    
    override func traitCollectionDidChange(_ previousTraitCollection: UITraitCollection?) {
        setupNavBar()
    }
    
    func setupNavBar() {
        if #available(iOS 13.0, *) {
            if traitCollection.userInterfaceStyle == .dark {
                let textAttributes = [NSAttributedString.Key.foregroundColor:UIColor.white]
                self.navigationController?.navigationBar.titleTextAttributes = textAttributes
                self.navigationController?.navigationBar.setBackgroundImage(UIImage(named: "bg_navigationBar_white")?.sakui_image(withTintColor: .black)?.sakui_resizable(), for: .default)
            } else {
                let textAttributes = [NSAttributedString.Key.foregroundColor:UIColor.black]
                self.navigationController?.navigationBar.titleTextAttributes = textAttributes
                self.navigationController?.navigationBar.setBackgroundImage(UIImage(named: "bg_navigationBar_white")?.sakui_resizable(), for: .default)
            }
        }
    }
    
    func setupUI()
    {
        view.backgroundColor = .generalBackground
        setupNavBar()
        title = "我的"
        view.addSubview(contentView)
        activateAutoScrolling(for: contentView)
        if #available(iOS 11.0, *) {
            contentView.contentInsetAdjustmentBehavior = .never
        }
        contentView.addSubview(accountView)
        contentView.addSubview(orderListCell)
        contentView.addSubview(refundedListCell)
        contentView.addSubview(myWalletCell)
        contentView.addSubview(barcodeCashierCell)
        contentView.addSubview(barcodeCashierHasParametersCell)
        contentView.addSubview(quickpassBarcodeCashierCell)
        contentView.addSubview(balanceCell)
        contentView.addSubview(paymentKitCell)
        contentView.addSubview(uiKitCell)
        contentView.addSubview(uiKit2Cell)
        contentView.addSubview(financeKitCell)
        contentView.addSubview(picassoDebugCell)
        contentView.addSubview(openPayDebugCell)
        contentView.addSubview(bizFacePaymentCell)
        contentView.addSubview(bizFacePaymentPicassoCell)
        contentView.addSubview(mrnCell)
        contentView.addSubview(meshCell)
        contentView.addSubview(MWalletMRNCell)
        contentView.setNeedsUpdateConstraints()
        contentView.updateConstraints()
        view.layoutIfNeeded()
    }
    
    func bindViewModel()
    {
        RAC(myWalletCell, #keyPath(PPMyAccountCell.titleString)) <~ RACObserve(target: viewModel, keyPath: #keyPath(PPMineViewModel.walletInfo.name))
        RAC(myWalletCell, #keyPath(PPMyAccountCell.titleDescription)) <~ RACObserve(target: viewModel, keyPath: #keyPath(PPMineViewModel.walletInfo.nameDescription))
        RAC(balanceCell, #keyPath(PPMyAccountCell.titleString)) <~ RACObserve(target: viewModel, keyPath: #keyPath(PPMineViewModel.balanceInfo.name))
        RAC(balanceCell, #keyPath(PPMyAccountCell.titleDescription)) <~ RACObserve(target: viewModel, keyPath: #keyPath(PPMineViewModel.balanceInfo.nameDescription))
        
        RACObserve(target: viewModel, keyPath: #keyPath(PPMineViewModel.walletInfo.isHighlight)).deliverOnMainThread().subscribeNext { [weak self](value) in
            guard let isHighlight = value as? Bool, let strongSelf = self else {
                return
            }
            strongSelf.myWalletCell.isHighlight = isHighlight
        }
        RACObserve(target: SAKEnvironment.environment(), keyPath: #keyPath(SAKEnvironment.user)).deliver(on: RACScheduler.mainThread()).subscribeNext { [weak self](value) in
            guard let strongSelf = self else {
                return
            }
            var isUserValid = SAKUserService.shared().isUserAvailable
            if isUserValid, let user = value as? SAKUser {
                isUserValid = user.isValid()
            } else {
                isUserValid = false
            }
            
            strongSelf.accountView.accountIconView.isHidden = !isUserValid
            strongSelf.accountView.userIDLabel.isHidden = !isUserValid
            strongSelf.accountView.logoutButton.isHidden = !isUserValid
            strongSelf.accountView.loginButton.isHidden = isUserValid
            
            if (isUserValid) {
                strongSelf.accountView.userNameLabel.text = (value as! SAKUser).userName
                strongSelf.accountView.userIDLabel.text = "userId:" + (value as! SAKUser).userID.stringValue
            } else {
                strongSelf.accountView.userNameLabel.text = ""
                strongSelf.accountView.userIDLabel.text = ""
            }
            strongSelf.accountView.setNeedsLayout()
            strongSelf.view.layoutIfNeeded()
        }
        
        orderListCell.rac_signal(for: .touchUpInside).subscribeNext { [weak self](value) in
            guard let _ = value, let strongSelf = self else {
                return
            }
            if SAKUserService.shared().isUserAvailable {
                let orderListViewController = PPOrderListViewController()
                strongSelf.navigationController?.pushViewController(orderListViewController, animated: true)
            } else {
                PPAccountController.defaultInstance().login(with: strongSelf) { [weak self] in
                    guard let strongSelf = self else {
                        return
                    }
                    let orderListViewController = PPOrderListViewController()
                    strongSelf.navigationController?.pushViewController(orderListViewController, animated: true)
                }
            }
        }
        refundedListCell.rac_signal(for: .touchUpInside).subscribeNext { [weak self](value) in
            guard let _ = value, let strongSelf = self else {
                return
            }
            if SAKUserService.shared().isUserAvailable {
                guard let refunderOrderListViewController = PPRefundedOrderListViewController(viewModel: PPRefundedOrderViewModel()) else {
                    return
                }
                strongSelf.navigationController?.pushViewController(refunderOrderListViewController, animated: true)
            } else {
                PPAccountController.defaultInstance().login(with: strongSelf) { [weak self] in
                    guard let strongSelf = self,
                        let refunderOrderListViewController = PPRefundedOrderListViewController(viewModel: PPRefundedOrderViewModel())  else {
                            return
                    }
                    strongSelf.navigationController?.pushViewController(refunderOrderListViewController, animated: true)
                }
            }
        }
        myWalletCell.rac_signal(for: .touchUpInside).subscribeNext { [weak self](value) in
            guard let _ = value,
                let strongSelf = self else {
                    return
            }
            
            // 获得选中的 env
            // 遍历匹配 _prodHttpUrl 为美团支付后台地址  @
            // 得到 effectUrl 判断地址中包含 'st'/'test' 或者 pay.meituan.com
            // 再匹配到相应的地址
            let envId = SAKOneClickDataManager.shared()?.defaultEnvironmentId();
            let moduleArray = SAKOneClickDataManager.shared().getModuleArrayEnvId(envId!);
            
            var portalUrl = "http://stable.pay.test.sankuai.com/resource/conch-hybrid/wallet/index.html?notitlebar=1"
            for module in moduleArray! {
                // 屏蔽 外卖商家服务市场1 干扰，兼容 http&https
                if module.prodHttpUrl.hasSuffix("pay.meituan.com") {
                    if module.effectUrl.contains("pay.meituan.com") {
                        // 线上
                        portalUrl = "https://npay.meituan.com/resource/conch-hybrid/wallet/index.html?notitlebar=1"
                    } else if (module.effectUrl.contains(".st.")) {
                        // st
                        portalUrl = "http://stable.pay.st.sankuai.com/resource/conch-hybrid/wallet/index.html?notitlebar=1"
                    } else {
                        // test
                        portalUrl = "http://stable.pay.test.sankuai.com/resource/conch-hybrid/wallet/index.html?notitlebar=1"
                    }
                }
            }
            
            if SAKUserService.shared().isUserAvailable {
                SAKPortal.transfer(from: strongSelf, to: URL(string: portalUrl)!, completion: nil)
            } else {
                PPAccountController.defaultInstance().login(with: strongSelf) { [weak self] in
                    guard let strongSelf = self else {
                        return
                    }
                    SAKPortal.transfer(from: strongSelf, to: URL(string: portalUrl)!, completion: nil)
                }
            }
        }
        barcodeCashierCell.rac_signal(for: .touchUpInside).subscribeNext { [weak self](value) in
            guard let _ = value, let strongSelf = self else {
                return
            }
            if SAKUserService.shared().isUserAvailable {
                SAKPortal.transfer(from: strongSelf, to: URL(string: "meituanpayment://barcodecashier/launch?sellerId=**************")!, completion: nil)
            } else {
                PPAccountController.defaultInstance().login(with: strongSelf) { [weak self] in
                    guard let strongSelf = self else {
                        return
                    }
                    SAKPortal.transfer(from: strongSelf, to: URL(string: "meituanpayment://barcodecashier/launch")!, completion: nil)
                }
            }
        }
        barcodeCashierHasParametersCell.rac_signal(for: .touchUpInside).subscribeNext { [weak self](value) in
            guard let _ = value, let strongSelf = self else {
                return
            }
            let hasParametersController = PPBarcodeCashierHasParametersViewController()
            strongSelf.navigationController?.pushViewController(hasParametersController, animated: true)
        }
        quickpassBarcodeCashierCell.rac_signal(for: .touchUpInside).subscribeNext { [weak self](value) in
            guard let _ = value, let strongSelf = self else {
                return
            }
            if SAKUserService.shared().isUserAvailable {
                SAKPortal.transfer(from: strongSelf, to: URL(string: "meituanpayment://quickpass/qrcode?entry=home")!, completion: nil)
            } else {
                PPAccountController.defaultInstance().login(with: strongSelf) { [weak self] in
                    guard let strongSelf = self else {
                        return
                    }
                    SAKPortal.transfer(from: strongSelf, to: URL(string: "meituanpayment://quickpass/qrcode?entry=home")!, completion: nil)
                }
            }
        }
        balanceCell.rac_signal(for: .touchUpInside).subscribeNext { [weak self](value) in
            guard let _ = value,
                let strongSelf = self else {
                    return
            }
            var portalUrl: String = (strongSelf.viewModel.balanceInfo.portalUrl != nil) ? strongSelf.viewModel.balanceInfo.portalUrl! : "meituanpayment://wallet/accountbalance"
            
            if !(portalUrl.count > 0) {
                portalUrl = "meituanpayment://wallet/accountbalance"
            }
            if SAKUserService.shared().isUserAvailable {
                SAKPortal.transfer(from: strongSelf, to: URL(string: portalUrl)!, completion: nil)
            } else {
                PPAccountController.defaultInstance().login(with: strongSelf) { [weak self] in
                    guard let strongSelf = self else {
                        return
                    }
                    SAKPortal.transfer(from: strongSelf, to: URL(string: portalUrl)!, completion: nil)
                }
            }
        }
        paymentKitCell.rac_signal(for: .touchUpInside).subscribeNext { [weak self](value) in
            guard let _ = value, let strongSelf = self else {
                return
            }
            let paymentKitController = PPPaymentKitViewController()
            strongSelf.navigationController?.pushViewController(paymentKitController, animated: true)
        }
        uiKitCell.rac_signal(for: .touchUpInside).subscribeNext { [weak self](value) in
            guard let _ = value, let strongSelf = self else {
                return
            }
            let uiKitController = PPUIKitViewController()
            strongSelf.navigationController?.pushViewController(uiKitController, animated: true)
        }
        uiKit2Cell.rac_signal(for: .touchUpInside).subscribeNext { [weak self](value) in
            guard let _ = value, let strongSelf = self else {
                return
            }
            let uiKit2Controller = PPUIKit2ViewController()
            strongSelf.navigationController?.pushViewController(uiKit2Controller, animated: true)
        }
        financeKitCell.rac_signal(for: .touchUpInside).subscribeNext { [weak self](value) in
            guard let _ = value, let strongSelf = self else {
                return
            }
            let financialKitController = PPFinancialKitViewController()
            strongSelf.navigationController?.pushViewController(financialKitController, animated: true)
        }
        picassoDebugCell.rac_signal(for: .touchUpInside).subscribeNext { [weak self](value) in
            guard let _ = value, let strongSelf = self else {
                return
            }
            let picassoDebugViewController = PPPicassoDebugViewController()
            strongSelf.navigationController?.pushViewController(picassoDebugViewController, animated: true)
        }
        bizFacePaymentCell.rac_signal(for: .touchUpInside).subscribeNext { [weak self](value) in
            guard let _ = value,
                let strongSelf = self else {
                    return
            }
            var portalUrl: String = "meituanpayment://facepay/openfacepay"
            
            if !(portalUrl.count > 0) {
                portalUrl = "meituanpayment://facepay/openfacepay"
            }
            SAKPortal.transfer(from: strongSelf, to: URL(string: portalUrl)!, completion: nil)
        }
        bizFacePaymentPicassoCell.rac_signal(for: .touchUpInside).subscribeNext { [weak self](value) in
            guard let _ = value,
                let strongSelf = self else {
                    return
            }
            let debugViewController = PPPicassoVCDebugViewController()
            strongSelf.navigationController?.pushViewController(debugViewController, animated: true)
        }
        mrnCell.rac_signal(for: .touchUpInside).subscribeNext { [weak self](value) in
            guard let _ = value,
                let strongSelf = self else {
                    return
            }
            let mrnDebugViewController = PPDebugPanel()
            strongSelf.navigationController?.pushViewController(mrnDebugViewController, animated: true)
        }
        MWalletMRNCell.rac_signal(for: .touchUpInside).subscribeNext { [weak self](value) in
            guard let _ = value,
                let strongSelf = self else {
                    return
            }
            let mrnDebugViewController = MerchantWalletEntranceViewController()
            strongSelf.navigationController?.pushViewController(mrnDebugViewController, animated: true)
        }
        meshCell.rac_signal(for: .touchUpInside).subscribeNext { [weak self](value) in
            guard let _ = value,
                let strongSelf = self else {
                    return
            }
            let meshDebugViewController = MESHDebugViewController()
            strongSelf.navigationController?.pushViewController(meshDebugViewController, animated: true)
        }
        openPayDebugCell.rac_signal(for: .touchUpInside).subscribeNext { [weak self](value) in
            guard let _ = value, let strongSelf = self else {
                return
            }
            let openPayDebugViewController = PPHalfPageDebugViewController()
            strongSelf.navigationController?.pushViewController(openPayDebugViewController, animated: true)
        }
        accountView.loginButton.rac_signal(for: .touchUpInside).subscribeNext { [weak self](value) in
            guard let _ = value, let strongSelf = self else {
                return
            }
            PPAccountController.defaultInstance().login(with: strongSelf, completedBlock: nil)
        }
        accountView.logoutButton.rac_signal(for: .touchUpInside).subscribeNext { (value) in
            UIAlertView.show(withTitle: "确定退出吗?", message: nil, cancelButtonTitle: "取消", otherButtonTitles: ["确定"], dismissed: { [weak self](buttonIndex) in
                guard let strongSelf = self else {
                    return
                }
                strongSelf.logout()
                }, canceled: {
                    
            })
        }
        
        let _ = SPKNoneMaskBezelActivityView(for: view, withLabel: "", width: 0)
        viewModel.loadCommand.execute(nil).subscribeError({ _ in
            SPKNoneMaskBezelActivityView.remove(animated: true)
        }) {
            SPKNoneMaskBezelActivityView.remove(animated: true)
        }
    }
    
    func logout()
    {
        SAKUserService.shared().userWantLogout()
        SAKRiskControlManager.shared().resetRiskControlState()
    }
    
    override func updateViewConstraints() {
        contentView.mas_updateConstraints { (make) in
            make?.edges.equalTo()(self.view)
        }
        accountView.mas_updateConstraints { (make) in
            make?.top.equalTo()(self.contentView)
            make?.width.equalTo()(self.view)
            make?.left.equalTo()(self.contentView)
            make?.height.equalTo()(76)
        }
        orderListCell.mas_updateConstraints { (make) in
            make?.top.equalTo()(self.accountView.mas_bottom)?.offset()(5)
            make?.left.right().equalTo()(self.accountView)
            make?.height.equalTo()(50)
        }
        refundedListCell.mas_updateConstraints { (make) in
            make?.top.equalTo()(self.orderListCell.mas_bottom)
            make?.left.right().equalTo()(self.accountView)
            make?.height.equalTo()(50)
        }
        myWalletCell.mas_updateConstraints { (make) in
            make?.top.equalTo()(self.refundedListCell.mas_bottom)
            make?.left.right().equalTo()(self.accountView)
            make?.height.equalTo()(50)
        }
        barcodeCashierCell.mas_updateConstraints { (make) in
            make?.top.equalTo()(self.myWalletCell.mas_bottom)
            make?.left.right().equalTo()(self.accountView)
            make?.height.equalTo()(50)
        }
        barcodeCashierHasParametersCell.mas_updateConstraints { (make) in
            make?.top.equalTo()(self.barcodeCashierCell.mas_bottom)
            make?.left.right().equalTo()(self.accountView)
            make?.height.equalTo()(50)
        }
        quickpassBarcodeCashierCell.mas_updateConstraints { (make) in
            make?.top.equalTo()(self.barcodeCashierHasParametersCell.mas_bottom)
            make?.left.right().equalTo()(self.accountView)
            make?.height.equalTo()(50)
        }
        balanceCell.mas_updateConstraints { (make) in
            make?.top.equalTo()(self.quickpassBarcodeCashierCell.mas_bottom)
            make?.left.right().equalTo()(self.accountView)
            make?.height.equalTo()(50)
        }
        paymentKitCell.mas_updateConstraints { (make) in
            make?.top.equalTo()(self.balanceCell.mas_bottom)
            make?.left.right().equalTo()(self.accountView)
            make?.height.equalTo()(50);
        }
        uiKitCell.mas_updateConstraints { (make) in
            make?.top.equalTo()(self.paymentKitCell.mas_bottom)
            make?.left.right().equalTo()(self.accountView)
            make?.height.equalTo()(50)
        }
        uiKit2Cell.mas_updateConstraints { (make) in
            make?.top.equalTo()(self.uiKitCell.mas_bottom)
            make?.left.right().equalTo()(self.accountView)
            make?.height.equalTo()(50)
        }
        financeKitCell.mas_updateConstraints { (make) in
            make?.top.equalTo()(self.uiKit2Cell.mas_bottom)
            make?.left.right().equalTo()(self.accountView)
            make?.height.equalTo()(50)
        }
        picassoDebugCell.mas_updateConstraints { (make) in
            make?.top.equalTo()(self.financeKitCell.mas_bottom)
            make?.left.right().equalTo()(self.accountView)
            make?.height.equalTo()(50)
        }
        bizFacePaymentCell.mas_updateConstraints { (make) in
            make?.top.equalTo()(self.picassoDebugCell.mas_bottom)
            make?.left.right().equalTo()(self.accountView)
            make?.height.equalTo()(50)
        }
        bizFacePaymentPicassoCell.mas_updateConstraints { (make) in
            make?.top.equalTo()(self.bizFacePaymentCell.mas_bottom)
            make?.left.right().equalTo()(self.accountView)
            make?.height.equalTo()(50)
        }
        mrnCell.mas_updateConstraints { (make) in
            make?.top.equalTo()(self.bizFacePaymentPicassoCell.mas_bottom)
            make?.left.right().equalTo()(self.accountView)
            make?.height.equalTo()(50)
        }
        MWalletMRNCell.mas_updateConstraints { (make) in
            make?.top.equalTo()(self.mrnCell.mas_bottom)
            make?.left.right().equalTo()(self.accountView)
            make?.height.equalTo()(50)
        }
        meshCell.mas_updateConstraints { (make) in
            make?.top.equalTo()(self.MWalletMRNCell.mas_bottom)
            make?.left.right().equalTo()(self.accountView)
            make?.height.equalTo()(50)
        }
        openPayDebugCell.mas_updateConstraints { (make) in
            make?.top.equalTo()(self.meshCell.mas_bottom)
            make?.left.right().equalTo()(self.accountView)
            make?.height.equalTo()(50)
            make?.bottom.equalTo()(self.contentView)
        }
        super.updateViewConstraints()
    }
}
