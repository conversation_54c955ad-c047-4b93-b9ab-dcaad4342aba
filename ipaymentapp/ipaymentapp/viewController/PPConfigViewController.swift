//
//  PPConfigViewController.swift
//  ipaymentapp
//
//  Created by qinchengbo on 2018/3/14.
//  Copyright © 2018年 Meituan.com. All rights reserved.
//

import UIKit
import MessageUI

fileprivate extension Selector
{
    static let dismissButtonClick = #selector(PPConfigViewController.tapDismissButton(_:))
}

class PPConfigViewController: MTBaseViewController
{
    
    var tableView: UITableView!
    var configArray = [String]();
    
    override func viewDidLoad()
    {
        super.viewDidLoad()
        setupUI()
        configArray.append(contentsOf: ["频道APP",
                                        "一键配置环境",
                                        "Logan 日志上报",
                                        "扫一扫",
                                        "关闭内存泄漏检查",
                                        "平台Debugkit",
                                        "发送 crash 邮件",
                                        "自动上报测试覆盖率",
                                        "关闭敏感数据加密",
                                        "性能平台悬浮窗开关",
                                        "Picasso HTTP开启",
                                        "强制展示平台推荐位",
                                        "模拟内存警告",
                                        "缓存管理",
                                        "打开长辈版开关"])
        tableView.reloadData()
    }
    
    override func traitCollectionDidChange(_ previousTraitCollection: UITraitCollection?) {
        setupNavBar()
    }
    
    func setupNavBar() {
        if #available(iOS 13.0, *) {
            if traitCollection.userInterfaceStyle == .dark {
                let textAttributes = [NSAttributedString.Key.foregroundColor:UIColor.white]
                self.navigationController?.navigationBar.titleTextAttributes = textAttributes
                self.navigationController?.navigationBar.setBackgroundImage(UIImage(named: "bg_navigationBar_white")?.sakui_image(withTintColor: .black)?.sakui_resizable(), for: .default)
            } else {
                let textAttributes = [NSAttributedString.Key.foregroundColor:UIColor.black]
                self.navigationController?.navigationBar.titleTextAttributes = textAttributes
                self.navigationController?.navigationBar.setBackgroundImage(UIImage(named: "bg_navigationBar_white")?.sakui_resizable(), for: .default)
            }
        }
    }
    
    func setupUI()
    {
        if presentingViewController != nil {
            let rightButton = SAKBarButtonItem.spk_dismissSupportUIConfigure(true, target: self, action: .dismissButtonClick)
            navigationItem.rightBarButtonItems = SAKBarButtonItem.itemsArray(with: .left, array: [rightButton!])
        }
        view.backgroundColor = .generalBackground
        tableView = UITableView(frame: .zero)
        tableView.backgroundColor = .clear
        tableView.delegate = self
        tableView.dataSource = self
        tableView.tableFooterView = UIView(frame: .zero)
        tableView.separatorStyle = .none
        tableView.register(SPKTitleTableViewCell.self, forCellReuseIdentifier: SPKTitleTableViewCell.className)
        tableView.register(SPKTitleSwitchTableViewCell.self, forCellReuseIdentifier: SPKTitleSwitchTableViewCell.className)
        self.view.addSubview(tableView)
        tableView.mas_makeConstraints { (make) in
            make?.edges.equalTo()(self.view)
        }
    }
    
    @objc func tapDismissButton(_ sender: Any?)
    {
        dismiss(animated: true, completion: nil)
    }
    
}

extension PPConfigViewController: UITableViewDelegate, UITableViewDataSource
{
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int
    {
        return configArray.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell
    {
        let identifier =
            (configArray.index(of: "关闭内存泄漏检查") != indexPath.row &&
            configArray.index(of: "自动上报测试覆盖率") != indexPath.row &&
            configArray.index(of: "关闭敏感数据加密") != indexPath.row &&
            configArray.index(of: "性能平台悬浮窗开关") != indexPath.row &&
            configArray.index(of: "Picasso HTTP开启") != indexPath.row &&
            configArray.index(of: "打开长辈版开关") != indexPath.row &&
            configArray.index(of: "强制展示平台推荐位") != indexPath.row) ? SPKTitleTableViewCell.className : SPKTitleSwitchTableViewCell.className
        let cell = tableView.dequeueReusableCell(withIdentifier: identifier, for: indexPath)
        configCell(cell, at: indexPath)
        return cell
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath)
    {
        tableView.deselectRow(at: indexPath, animated: true)
        
        switch indexPath.row {
        case 0: // 频道APP
            let appSelectVC = PPAppSelectViewController()
            navigationController?.pushViewController(appSelectVC, animated: true)
        case 1: // 一键配置环境
            let switchConfigVC = MTModelSwitchViewController()
            switchConfigVC.dismissBlock = {
                SAKUserService.shared().userWantLogout();
            }
            navigationController?.pushViewController(switchConfigVC, animated: true)
        case 2: // Mock
            let unionID = SAKEnvironment.environment().unionID
            Logan.uploadLog(withDate: Logan.todaysDate(), appid: "26", bizid: "1001", unionid: unionID) { success, errCode, errMsg in
                if success {
                    SPKToastCenter().toastSuccess(withMessage: "上传成功")
                } else {
                    SPKToastCenter().toastWarning(withMessage: errMsg)
                }
            }
        case 3: // 扫一扫
            let scanVC = PPQRCodeScanViewController()
            scanVC.codeScanSucceed = { (resultURL: URL?) in
                guard let url = resultURL else {
                    return
                }
                
                self.dismiss(animated: false, completion: nil)
                
                // 扫到结果18位付款码，则发起构造支付的请求
                let pred = NSPredicate(format: "SELF MATCHES %@", "^[0-9]{18}$")
                let isMatch: Bool = pred.evaluate(with: url.absoluteString)
                if isMatch {
                    let service: PPService! = PPService.defaultInstance() as? PPService
                    service.barcodeDemoMockPay(url.absoluteString, finished: { (error) in
                        DispatchQueue.main.async(execute: {
                            if let error = error {
                                SPKToastCenter.default().toastWithError(error)
                            } else {
                                SPKToastCenter.default().toast(withMessage: "接口成功")
                            }
                        })
                    })
                    return
                }
                
                switch url.scheme {
                case "dianping"?:
                    var urlStr = url.absoluteString
                    if urlStr.hasPrefix("dianping://playpicasso") {
                        urlStr = urlStr.replacingOccurrences(of: "dianping://playpicasso", with: "iconch://debug/playpicasso")
                        SAKPortal.transfer(from: self, to: URL(string: urlStr)!, completion: nil)
                    }
                default:
                    SAKPortal.transfer(from: self, to: url, completion: nil)
                }
            }
            scanVC.codeScanFailed = {
                self.dismiss(animated: true, completion: nil)
            }
            present(scanVC, animated: true, completion: nil)
            break
        case 4: // 内存检测
            break
        case 5: // 平台 DebugKit
            let debugKitBundle = Bundle(path: Bundle.main.bundlePath + "/SAKDebugKit.bundle")
            if let debugCenterVC = UIStoryboard(name: "DebugCenter", bundle: debugKitBundle).instantiateInitialViewController() {
                present(debugCenterVC, animated: true, completion: nil)
            }
        case 6:
            if MFMailComposeViewController.canSendMail() {
                if let crashInfo = PPCrashReporter.getCrashLog(), !crashInfo.isEmpty {
                    let mailVC = MFMailComposeViewController()
                    mailVC.mailComposeDelegate = self
                    mailVC.setToRecipients(["<EMAIL>",
                                            "<EMAIL>",
                                            "<EMAIL>",
                                            "<EMAIL>"])
                    mailVC.setCcRecipients(["<EMAIL>"])
                    mailVC.setSubject("[CrashLog] 支付 iOS 独立业务客户端")
                    mailVC.setMessageBody(crashInfo, isHTML: true)
                    present(mailVC, animated: true, completion: nil)
                } else {
                    SPKAlertView.show(withMessage: "本机没有 crash 信息", completionButtonTitle: "知道了", completion: nil)
                }
            } else {
                SPKAlertView.show(withMessage: "请先在邮件 App 登录美团邮件账号(例如 <EMAIL>)，再选择发送 crash 邮件", completionButtonTitle: "知道了", completion: nil)
            }
        case 7:
            break
        case 8: // 敏感数据加密开关
            break
        case 9:
            break
        case 13:
            let sel = Selector(("_performMemoryWarning"))
            if UIApplication.shared.responds(to: sel) {
                UIApplication.shared.perform(sel)
            } else {
                print("Not Responds _performMemoryWarning")
            }
        case 14:
            let cacheManagerVC = PPCacheManagerViewController()
            navigationController?.pushViewController(cacheManagerVC, animated: true)
            
        default:
            break
        }
    }
    
    func configCell(_ cell: UITableViewCell, at index: IndexPath)
    {
        switch cell.className {
        case SPKTitleTableViewCell.className:
            guard let cell = cell as? SPKTitleTableViewCell else {
                return
            }
            cell.titleLabel.text = configArray[index.row]
            if #available(iOS 13.0, *) {
                cell.titleLabel.textColor = .label
                cell.contentView.backgroundColor = .clear
                for subview in cell.contentView.subviews {
                    if let separateline = subview as? SPKSeparatorLine {
                        separateline.backgroundColor = .systemGray2
                    }
                }
            }
            break
        case SPKTitleSwitchTableViewCell.className:
            guard let cell = cell as? SPKTitleSwitchTableViewCell else {
                return
            }
            if configArray.index(of: "关闭内存泄漏检查") == index.row {
                if var leakMonitorState = SPGObjectCacheManager.shared().object(forKey: PPGlobalConstant.kSPKMemoryLeakMonitorTrunOffState) as? Bool {
                    leakMonitorState = leakMonitorState ? leakMonitorState : SAKMemoryLeakMonitor.memoryLeakMonitorIsTurnedOff()
                    cell.switchButton.isOn = leakMonitorState
                    
                    if leakMonitorState {
                        SAKMemoryLeakMonitor.turnOffMemoryLeakMonitor(leakMonitorState)
                    }
                }
                
                cell.titleLabel.text = configArray[index.row]
                cell.switchButtonClickedBlock = { [weak cell] in
                    guard let strongCell = cell else {
                        return
                    }
                    let state: ObjCBool = ObjCBool(!strongCell.switchButton.isOn)
                    SAKMemoryLeakMonitor.turnOffMemoryLeakMonitor(state.boolValue)
                    MLeaksFinderSwitchTurnoff = state
                    strongCell.switchButton.isOn = state.boolValue
                    
                    SPGObjectCacheManager.shared().saveObject(state.boolValue as NSCoding, forKey: PPGlobalConstant.kSPKMemoryLeakMonitorTrunOffState)
                }
            } else if configArray.index(of: "关闭敏感数据加密") == index.row {
                if let sensitiveDataEncryptState = SPGDataStorageManager.shared().string(forKey: PPGlobalConstant.kSPKSensitiveDataEncryptOff, storageLocation: .userDefaults, shouldDecrypt: false, error: nil), sensitiveDataEncryptState == "YES" {
                    cell.switchButton.isOn = true
                } else {
                    cell.switchButton.isOn = false
                }
                cell.titleLabel.text = configArray[index.row];
                cell.switchButtonClickedBlock = { [weak cell] in
                    guard let strongCell = cell else {
                        return
                    }
                    strongCell.switchButton.isOn = !strongCell.switchButton.isOn;
                    SPGDataStorageManager.shared().save(strongCell.switchButton.isOn ? "YES" : "NO", forKey: PPGlobalConstant.kSPKSensitiveDataEncryptOff, storageLocation: .userDefaults, shouldEncrypt: false, updateExisting: true, error: nil)
                }
            } else if configArray.index(of: "性能平台悬浮窗开关") == index.row {
                let metricsSwitch = SAKMetricsDebugConfig.shared().showDashboard
                cell.switchButton.isOn = metricsSwitch
                
                cell.titleLabel.text = configArray[index.row]
                cell.switchButtonClickedBlock = { [weak cell] in
                    guard let strongCell = cell else {
                        return
                    }
                    strongCell.switchButton.isOn = !strongCell.switchButton.isOn
                    SAKMetricsDebugConfig.shared().showDashboard = strongCell.switchButton.isOn
                }
            } else if configArray.index(of: "Picasso HTTP开启") == index.row {
                if let sharkStatus = SPGObjectCacheManager.shared().object(forKey: PPGlobalConstant.kSPKPicassoSharkTrunOffState) as? Bool {
                    cell.switchButton.isOn = sharkStatus
                }
                cell.titleLabel.text = configArray[index.row]
                cell.switchButtonClickedBlock = { [weak cell] in
                    guard let strongCell = cell else {
                        return
                    }
                    strongCell.switchButton.isOn = !strongCell.switchButton.isOn;
                    SPGObjectCacheManager.shared().saveObject(strongCell.switchButton.isOn as NSCoding, forKey: PPGlobalConstant.kSPKPicassoSharkTrunOffState)
                    SPKToastCenter.default().toast(withMessage: "需要重启App")
                }
            } else if configArray.index(of: "强制展示平台推荐位") == index.row {
                if let status = SPGObjectCacheManager.shared().object(forKey: PPGlobalConstant.kSPKShowRecommendationViewForced) as? Bool {
                    cell.switchButton.isOn = status
                }
                cell.titleLabel.text = configArray[index.row]
                cell.switchButtonClickedBlock = { [weak cell] in
                    guard let strongCell = cell else {
                        return
                    }
                    strongCell.switchButton.isOn = !strongCell.switchButton.isOn;
                    UserDefaults.standard.set(strongCell.switchButton.isOn, forKey: PPGlobalConstant.kSPKShowRecommendationViewForced)
                    UserDefaults.standard.synchronize()
                }
            } else if configArray.index(of: "自动上报测试覆盖率") == index.row {
                if let status = SPGObjectCacheManager.shared().object(forKey: PPGlobalConstant.kSPKAutoReportCoverageOn) as? Bool {
                    cell.switchButton.isOn = status
                }
                cell.titleLabel.text = configArray[index.row]
                cell.switchButtonClickedBlock = { [weak cell] in
                    guard let strongCell = cell else {
                        return
                    }
                    strongCell.switchButton.isOn = !strongCell.switchButton.isOn
                    SPGObjectCacheManager.shared().saveObject(strongCell.switchButton.isOn  as NSCoding, forKey: PPGlobalConstant.kSPKAutoReportCoverageOn)
                    COVDataManager.shared()?.startOrStop(toUpload: strongCell.switchButton.isOn)
                }
            } else if configArray.index(of: "打开长辈版开关") == index.row {
                cell.switchButton.isOn = UserDefaults.standard.bool(forKey: "elderVersion")
                cell.titleLabel.text = configArray[index.row]
                cell.switchButtonClickedBlock = { [weak cell] in
                    guard let strongCell = cell else {
                        return
                    }
                    strongCell.switchButton.isOn = !strongCell.switchButton.isOn;
                    UserDefaults.standard.set(strongCell.switchButton.isOn, forKey: "elderVersion")
                    UserDefaults.standard.synchronize()
                }
            } else {
                // do nothing
            }
            if #available(iOS 13.0, *) {
                cell.titleLabel.textColor = .label
                cell.contentView.backgroundColor = .clear
                for subview in cell.contentView.subviews {
                    if let separateline = subview as? SPKSeparatorLine {
                        separateline.backgroundColor = .systemGray2
                    }
                }
            }
            break
        default:
            break
        }
        
    }
}

extension PPConfigViewController: MFMailComposeViewControllerDelegate
{
    func mailComposeController(_ controller: MFMailComposeViewController, didFinishWith result: MFMailComposeResult, error: Error?)
    {
        if result != .failed {
            PPCrashReporter.deleteCrashLog()
        }
        dismiss(animated: true, completion: nil)
    }
}
