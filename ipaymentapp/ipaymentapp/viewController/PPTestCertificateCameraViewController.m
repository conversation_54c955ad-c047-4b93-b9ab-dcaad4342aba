//
//  PPFinancialKitViewController.m
//  ipaymentapp
//
//  Created by xutianxi on 2017/11/27.
//  Copyright © 2017年 Meituan.com. All rights reserved.
//

#import "PPTestCertificateCameraViewController.h"
#import "UIButton+Custom.h"
#import <Masonry.h>
#import "SAKUserService.h"
#import "SAKPortal.h"
#import "PPAccountController.h"
#import "CIPFoundation.h"
#import "NSDictionary+SPKShortcuts.h"
#import "SAKWebViewController.h"
#import "SAKPaymentUIConfigure.h"
#import "SPKTechnologyStatistics.h"
#import "SPKAssert.h"

static NSString * const kSPKURLTestCertificateCamera = @"meituanpayment://takephotos/testcertificate";


@interface PPTestCertificateCameraViewController ()

@property (nonatomic, strong) NSString *path;
@property (nonatomic, assign) NSInteger result;
@property (nonatomic, strong) UILabel *captureTypeTip;
@property (nonatomic, strong) UIImageView *imageView;

@property (nonatomic, strong) UIButton *verify3Btn;

@end

@implementation PPTestCertificateCameraViewController

SAK_PORTAL_REGISTER()
{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        
        [SAKPortal registerPortalWithHandler:^UIViewController<SAKPortalable> * _Nullable(NSURL * _Nonnull URL, BOOL shouldTransfer, UIViewController * _Nonnull sourceViewController) {
            if ([URL hasSameTrunkWithURL:[NSURL URLWithString:kSPKURLTestCertificateCamera]]) {
                return [PPTestCertificateCameraViewController startCertificateCameraWithURL:URL fromViewController:sourceViewController];
            } else {
                return nil;
            }
        }
                                   prefixURL:[NSURL URLWithString:kSPKURLTestCertificateCamera]
                                    pageInfo:[SAKPortalPageInfo pageInfoWithPageName:@"证件拍照页面"
                                                                           className:@"PPCertificateCameraViewController"
                                                                                path:kSPKURLTestCertificateCamera
                                                                  requiredParameters:@"callbackUrl|业务方回调URL"
                                                                  optionalParameters:@""]];
    });
}

+ (UIViewController<SAKPortalable>* _Nullable)startCertificateCameraWithURL:(NSURL * _Nonnull)URL
                                                         fromViewController:(UIViewController * _Nonnull)sourceViewController
{ 
    
    NSDictionary *queryParameterDictionary = [[URL query] cipf_dictionaryByParseInURLParameterFormat];
    
    PPTestCertificateCameraViewController *vc = [[PPTestCertificateCameraViewController alloc] init];
    
    NSString *strPath = [queryParameterDictionary spk_stringForKey:@"fullSavePath"];
    NSInteger result  = [queryParameterDictionary spk_integerForKey:@"result"];
    vc.result = result;
    vc.path = strPath;
    UINavigationController *navi = sourceViewController.navigationController;
    [navi popViewControllerAnimated:NO];
    [navi pushViewController:vc animated:YES];
    
    return vc;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor whiteColor];
    // Do any additional setup after loading the view.
    
    self.verify3Btn = ({
        UIButton *button = [UIButton orangeCustomButton];
        [button setTitle:@"证件拍照服务" forState:UIControlStateNormal];
        [button addTarget:self action:@selector(didClickedverify3Button) forControlEvents:UIControlEventTouchUpInside];
        button;
    });
    [self.view addSubview:self.verify3Btn];
    
    self.captureTypeTip = ({
        UILabel *label = [[UILabel alloc] init];
        label.text = @"captureType";
        label;
    });
    [self.view addSubview:self.captureTypeTip];
    
    self.imageView = ({
        UIImageView *imageView = [[UIImageView alloc] init];
        imageView;
    });
    [self.view addSubview:self.imageView];
    
    self.imageView.contentMode = UIViewContentModeScaleAspectFit;
    if ( self.result) {
        self.captureTypeTip.text = @"成功";
    } else {
        self.captureTypeTip.text = @"失败";
    }
    
    NSData *data = [NSData dataWithContentsOfFile:self.path];
    UIImage *image = [UIImage imageWithData:data];
    self.imageView.image = image;
    
    [self.view updateConstraintsIfNeeded];
}

- (void)didClickedverify3Button
{
    [self.navigationController popViewControllerAnimated:YES];
}


- (void)updateViewConstraints
{
    [self.captureTypeTip mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(50);
        make.left.equalTo(self.view).offset(50);
        make.width.equalTo(@(150));
        make.height.equalTo(@(40));
    }];
    
    [self.imageView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.captureTypeTip.mas_bottom);
        make.left.equalTo(self.view).offset(20);
        make.right.equalTo(self.view).offset(-20);
        make.height.equalTo(@(400));
    }];
    
    [self.verify3Btn mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.imageView.mas_bottom).offset(20);
        make.left.equalTo(self.view).offset(50);
        make.width.equalTo(@(200));
        make.height.equalTo(@(40));
    }];
    [super updateViewConstraints];
}

- (NSString *)pageDescription
{
    return @"证件拍照页面";
}
@end

