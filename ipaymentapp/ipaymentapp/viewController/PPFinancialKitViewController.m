//
//  PPFinancialKitViewController.m
//  ipaymentapp
//
//  Created by xutianxi on 2017/11/27.
//  Copyright © 2017年 Meituan.com. All rights reserved.
//

#import "PPFinancialKitViewController.h"
#import "UIButton+Custom.h"
#import <Masonry.h>
#import "SAKUserService.h"
#import "SAKPortal.h"
#import "PPAccountController.h"
#import "PPCertificateCameraViewController.h"
#import "UIColor+Addition.h"

@interface PPFinancialKitViewController ()

@property (nonatomic, strong) UIButton *verify1Btn;
@property (nonatomic, strong) UIButton *verify2Btn;
@property (nonatomic, strong) UIButton *verify3Btn;
@property (nonatomic, strong) UIButton *h5InvokeUserVerifyBtn;
@property (nonatomic, strong) UIButton *quickpassBtn;
@property (nonatomic, strong) UIButton *verifyCenterBtn;
@property (nonatomic, strong) UIButton *webViewOpenThirdPaymentBtn;

@end

@implementation PPFinancialKitViewController

- (void)dealloc
{
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor generalBackgroundColor];
    // Do any additional setup after loading the view.
    
    self.verify1Btn = ({
        UIButton *button = [UIButton orangeCustomButton];
        [button setTitle:@"身份验证服务" forState:UIControlStateNormal];
        [button addTarget:self action:@selector(didClickedverify1Button) forControlEvents:UIControlEventTouchUpInside];
        button;
    });
    [self.view addSubview:self.verify1Btn];
    
    self.verify2Btn = ({
        UIButton *button = [UIButton orangeCustomButton];
        [button setTitle:@"身份验证服务(不传手持)" forState:UIControlStateNormal];
        [button addTarget:self action:@selector(didClickedverify2Button) forControlEvents:UIControlEventTouchUpInside];
        button;
    });
    [self.view addSubview:self.verify2Btn];
    
    self.verify3Btn = ({
        UIButton *button = [UIButton orangeCustomButton];
        [button setTitle:@"证件拍照识别服务" forState:UIControlStateNormal];
        [button addTarget:self action:@selector(didClickedverify3Button) forControlEvents:UIControlEventTouchUpInside];
        button;
    });
    [self.view addSubview:self.verify3Btn];
    
    self.h5InvokeUserVerifyBtn = ({
        UIButton *button = [UIButton orangeCustomButton];
        [button setTitle:@"h5调起身份验证服务" forState:UIControlStateNormal];
        [button addTarget:self action:@selector(didClickedh5InvokeUserVerifyButton) forControlEvents:UIControlEventTouchUpInside];
        button;
    });
    [self.view addSubview:self.h5InvokeUserVerifyBtn];
    
    self.quickpassBtn = ({
        UIButton *button = [UIButton orangeCustomButton];
        [button setTitle:@"闪付页面打开余额" forState:UIControlStateNormal];
        [button addTarget:self action:@selector(didClickedQuickpassOpenBalance) forControlEvents:UIControlEventTouchUpInside];
        button;
    });
    [self.view addSubview:self.quickpassBtn];
    
    self.verifyCenterBtn = ({
        UIButton *button = [UIButton orangeCustomButton];
        [button setTitle:@"验证能力独立调用" forState:UIControlStateNormal];
        [button addTarget:self action:@selector(verifyCenterBtnClick) forControlEvents:UIControlEventTouchUpInside];
        button;
    });
    [self.view addSubview:self.verifyCenterBtn];
    
    self.webViewOpenThirdPaymentBtn = ({
        UIButton *button = [UIButton orangeCustomButton];
        [button setTitle:@"i版调起三方支付方式" forState:UIControlStateNormal];
        [button addTarget:self action:@selector(webViewOpenThirdPaymentBtnClick) forControlEvents:UIControlEventTouchUpInside];
        button;
    });
    [self.view addSubview:self.webViewOpenThirdPaymentBtn];

    [self.view updateConstraintsIfNeeded];
}

- (void)didClickedQuickpassOpenBalance
{
    NSString *portalUrl = @"meituanpayment://wallet/accountbalance?scene=107&returnToBiz=1";
    
    [SAKPortal transferFromViewController:self
                                    toURL:[NSURL URLWithString:portalUrl]
                               completion:nil];
}

- (void)didClickedverify1Button
{
    NSString *portalUrl = @"meituanpayment://identify/idcard?needHandIdPhoto=1&bizId=1";

    [SAKPortal transferFromViewController:self
                                    toURL:[NSURL URLWithString:portalUrl]
                               completion:nil];
}

- (void)didClickedverify2Button
{
    NSString *portalUrl = @"meituanpayment://identify/idcard?bizId=1";

    [SAKPortal transferFromViewController:self
                                    toURL:[NSURL URLWithString:portalUrl]
                               completion:nil];
}

- (void)didClickedverify3Button
{
    PPCertificateCameraViewController *vc = [[PPCertificateCameraViewController alloc] init];
    [self.navigationController pushViewController:vc animated:YES]; 
}

- (void)didClickedh5InvokeUserVerifyButton
{
    NSString *portalUrl = @"meituanpayment://www.meituan.com/web?url=http://knb.sankuai.com/page/130";
    
    [SAKPortal transferFromViewController:self
                                    toURL:[NSURL URLWithString:portalUrl]
                               completion:nil];
}

- (void)verifyCenterBtnClick
{
    NSString *portalUrl = @"http://pay01-sl-cashier.qa.pay.test.sankuai.com/resource/pass-verify/index.html?merchantNo=11000006520594&orderNo=123&closeWebview=1&scene=12&partnerId=115&partner_support_multi=1&mustVerify=1";
    
    [SAKPortal transferFromViewController:self
                                    toURL:[NSURL URLWithString:portalUrl]
                               completion:nil];
}

- (void)webViewOpenThirdPaymentBtnClick
{
    NSString *portalUrl = @"https://stable-pay.st.meituan.com/resource/submit-order/index.html#/";
    
    [SAKPortal transferFromViewController:self
                                    toURL:[NSURL URLWithString:portalUrl]
                               completion:nil];
}

- (void)updateViewConstraints
{
    [self.verify1Btn mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(30);
        make.left.equalTo(self.view).offset(50);
        make.width.equalTo(@(200));
        make.height.equalTo(@(40));
    }];
    
    [self.verify2Btn mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(100);
        make.left.equalTo(self.view).offset(50);
        make.width.equalTo(@(200));
        make.height.equalTo(@(40));
    }];
    
    [self.h5InvokeUserVerifyBtn mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(180);
        make.left.equalTo(self.view).offset(50);
        make.width.equalTo(@(200));
        make.height.equalTo(@(40));
    }];
    
    [self.quickpassBtn mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(300);
        make.left.equalTo(self.view).offset(50);
        make.width.equalTo(@(200));
        make.height.equalTo(@(40));
    }];
    
    [self.verifyCenterBtn mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(400);
        make.left.equalTo(self.view).offset(50);
        make.width.equalTo(@(200));
        make.height.equalTo(@(40));
    }];
    
    [self.verify3Btn mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(500);
        make.left.equalTo(self.view).offset(50);
        make.width.equalTo(@(200));
        make.height.equalTo(@(40));
    }];
    
    [self.webViewOpenThirdPaymentBtn mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(580);
        make.left.equalTo(self.view).offset(50);
        make.width.equalTo(@(200));
        make.height.equalTo(@(40));
    }];
    
    [super updateViewConstraints];
}

@end

