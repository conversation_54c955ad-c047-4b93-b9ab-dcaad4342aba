//
//  PPRefundedOrderCellTableViewCell.m
//  ipaymentapp
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 17/2/24.
//  Copyright © 2017年 Meituan.com. All rights reserved.
//

#import "PPRefundedOrderCellTableViewCell.h"
#import "SAKUIKit.h"
#import <Masonry.h>
#import "PPRefundedOrderUIObject.h"

@interface PPRefundedOrderCellTableViewCell ()

@property (nonatomic, strong) UILabel *payOrderIDLabel;
@property (nonatomic, strong) UILabel *moneyLabel;
@property (nonatomic, strong) UILabel *methodLabel;
@property (nonatomic, strong) UILabel *statusLabel;
@property (nonatomic, strong) UILabel *addTimeLabel;

@end

@implementation PPRefundedOrderCellTableViewCell

#pragma mark - SPKCellProtocol

+ (CGFloat)willShowHeightWithDataSource:(id)dataSource
{
    return 44;
}

+ (BOOL)requiresConstraintBasedLayout
{
    return YES;
}

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI
{
    self.payOrderIDLabel = ({
        UILabel *label = [[UILabel alloc] init];
        label.font = Font(12);
        label;
    });
    self.moneyLabel = ({
        UILabel *label = [[UILabel alloc] init];
        label.font = Font(12);
        label;
    });
    self.methodLabel = ({
        UILabel *label = [[UILabel alloc] init];
        label.font = Font(12);
        label;
    });
    self.statusLabel = ({
        UILabel *label = [[UILabel alloc] init];
        label.font = Font(12);
        label;
    });
    self.addTimeLabel = ({
        UILabel *label = [[UILabel alloc] init];
        label.font = Font(12);
        label;
    });
    
    [self.contentView setBackgroundColor:[UIColor clearColor]];
    [self.contentView addSubview:self.payOrderIDLabel];
    [self.contentView addSubview:self.moneyLabel];
    [self.contentView addSubview:self.methodLabel];
    [self.contentView addSubview:self.statusLabel];
    [self.contentView addSubview:self.addTimeLabel];
    
}

- (void)updateConstraints
{
    [self.payOrderIDLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(10);
        make.top.equalTo(self.contentView).offset(5);
    }];
    
    [self.moneyLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.contentView).offset(-10);
        make.centerY.equalTo(self.payOrderIDLabel);
    }];
    
    [self.methodLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.payOrderIDLabel.mas_bottom).offset(5);
        make.left.equalTo(self.contentView).offset(10);
    }];
    
    [self.statusLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.methodLabel.mas_right).offset(5);
        make.top.equalTo(self.methodLabel);
    }];
    
    [self.addTimeLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.statusLabel);
        make.left.equalTo(self.statusLabel.mas_right).offset(10);
    }];
    
    [super updateConstraints];
}

- (void)setDataSource:(PPRefundedOrderUIObject *)dataSource
{
    _dataSource = dataSource;
    
    self.payOrderIDLabel.text = dataSource.payOrderId;
    self.moneyLabel.text = dataSource.moneyCent;
    self.methodLabel.text = dataSource.source;
    self.statusLabel.text = dataSource.refundStatus;
    self.addTimeLabel.text = dataSource.acceptTime;
   
}

@end
