//
//  PPFinancialKitViewController.m
//  ipaymentapp
//
//  Created by xutianxi on 2017/11/27.
//  Copyright © 2017年 Meituan.com. All rights reserved.
//

#import "PPCertificateCameraViewController.h"
#import "UIButton+Custom.h"
#import <Masonry.h>
#import "SAKUserService.h"
#import "SAKPortal.h"
#import "PPAccountController.h"

@interface PPCertificateCameraViewController ()

@property (nonatomic, strong) UILabel *captureTypeTip;
@property (nonatomic, strong) UITextField *captureTypeTextField;

@property (nonatomic, strong) UILabel *mechantNoTip;
@property (nonatomic, strong) UITextField *mechantNoTextField;

@property (nonatomic, strong) UILabel *callbackURLTip;
@property (nonatomic, strong) UITextField *callbackURLTextField;

@property (nonatomic, strong) UIButton *takePhotoBtn;
@property (nonatomic, strong) UIButton *recognizeBtn;
@property (nonatomic, strong) UIButton *verify3Btn;

@end

@implementation PPCertificateCameraViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor whiteColor];
    // Do any additional setup after loading the view.
    
//    self.verify3Btn = ({
//        UIButton *button = [UIButton orangeCustomButton];
//        [button setTitle:@"调起认证流程" forState:UIControlStateNormal];
//        [button addTarget:self action:@selector(didClickedverify3Button) forControlEvents:UIControlEventTouchUpInside];
//        button;
//    });
//    [self.view addSubview:self.verify3Btn];
    
    self.takePhotoBtn = ({
        UIButton *button = [UIButton orangeCustomButton];
        [button setTitle:@"调起拍照上传流程" forState:UIControlStateNormal];
        [button addTarget:self action:@selector(didClickedTakePhotoBtn) forControlEvents:UIControlEventTouchUpInside];
        button;
    });
    [self.view addSubview:self.takePhotoBtn];
    
    self.recognizeBtn = ({
        UIButton *button = [UIButton orangeCustomButton];
        [button setTitle:@"调起识别流程" forState:UIControlStateNormal];
        [button addTarget:self action:@selector(didClickedRecognizeBtn) forControlEvents:UIControlEventTouchUpInside];
        button;
    });
    [self.view addSubview:self.recognizeBtn];
    
    self.captureTypeTip = ({
        UILabel *label = [[UILabel alloc] init];
        label.text = @"certificateType";
        label;
    });
    [self.view addSubview:self.captureTypeTip];
    
    self.captureTypeTextField = ({
        UITextField *textField = [[UITextField alloc] init];
        textField.text = @"2";
        textField;
    });
    [self.view addSubview:self.captureTypeTextField];
    
    self.mechantNoTip = ({
        UILabel *label = [[UILabel alloc] init];
        label.text = @"mechantNo";
        label;
    });
    [self.view addSubview:self.mechantNoTip];
    
    self.mechantNoTextField = ({
        UITextField *textField = [[UITextField alloc] init];
        textField.text = @"10";
        textField;
    });
    [self.view addSubview:self.mechantNoTextField];
    
    self.callbackURLTip = ({
        UILabel *label = [[UILabel alloc] init];
        label.text = @"callbackURL";
        label;
    });
    [self.view addSubview:self.callbackURLTip];
    
    NSString *callbackURL = @"";
    self.callbackURLTextField = ({
        UITextField *textField = [[UITextField alloc] init];
        textField.text = callbackURL;
        textField;
    });
    [self.view addSubview:self.callbackURLTextField];
    
    [self.view updateConstraintsIfNeeded];
}

- (void)didClickedTakePhotoBtn
{
    NSString *portalUrl = [NSString stringWithFormat:@"meituanpayment://financeCertificate/takePhoto?certificateType=%@&bizId=%@&callbackURL=%@&mechantNo=%@", self.captureTypeTextField.text, @"1", self.callbackURLTextField.text, self.mechantNoTextField.text];
    [SAKPortal transferFromViewController:self
                                    toURL:[NSURL URLWithString:portalUrl]
                               completion:nil];
}

- (void)didClickedRecognizeBtn
{
    NSString *portalUrl = [NSString stringWithFormat:@"meituanpayment://financeCertificate/recognize?certificateType=%@&bizId=%@&callbackURL=%@&mechantNo=%@", self.captureTypeTextField.text, @"1", self.callbackURLTextField.text, self.mechantNoTextField.text];
    [SAKPortal transferFromViewController:self
                                    toURL:[NSURL URLWithString:portalUrl]
                               completion:nil];
}

- (void)didClickedverify3Button
{
    NSString *portalUrl = [NSString stringWithFormat:@"meituanpayment://financeCertificate/verify?certificateType=%@&bizId=%@&callbackURL=%@&mechantNo=%@", self.captureTypeTextField.text, @"1", self.callbackURLTextField.text, self.mechantNoTextField.text];
    [SAKPortal transferFromViewController:self
                                    toURL:[NSURL URLWithString:portalUrl]
                               completion:nil];
}

- (void)updateViewConstraints
{
    [self.captureTypeTip mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(50);
        make.left.equalTo(self.view).offset(50);
        make.width.equalTo(@(150));
        make.height.equalTo(@(40));
    }];
    
    [self.captureTypeTextField mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.captureTypeTip);
        make.left.equalTo(self.captureTypeTip.mas_right).offset(20);
        make.width.equalTo(@(100));
        make.height.equalTo(@(40));
    }];
    
    
    [self.mechantNoTip mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.captureTypeTip.mas_bottom).offset(20);
        make.left.equalTo(self.view).offset(50);
        make.width.equalTo(@(150));
        make.height.equalTo(@(40));
    }];
    
    [self.mechantNoTextField mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.mechantNoTip);
        make.left.equalTo(self.mechantNoTip.mas_right).offset(20);
        make.width.equalTo(@(100));
        make.height.equalTo(@(40));
    }];
    
    [self.callbackURLTip mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.mechantNoTip.mas_bottom).offset(20);
        make.left.equalTo(self.view).offset(50);
        make.width.equalTo(@(150));
        make.height.equalTo(@(40));
    }];
    
    [self.callbackURLTextField mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.callbackURLTip);
        make.left.equalTo(self.callbackURLTip.mas_right).offset(20);
        make.width.equalTo(@(100));
        make.height.equalTo(@(40));
    }];
    
    [self.takePhotoBtn mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.callbackURLTextField.mas_bottom).offset(20);
        make.left.equalTo(self.view).offset(50);
        make.width.equalTo(@(200));
        make.height.equalTo(@(40));
    }];
    
    [self.recognizeBtn mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.takePhotoBtn.mas_bottom).offset(20);
        make.left.equalTo(self.view).offset(50);
        make.width.equalTo(@(200));
        make.height.equalTo(@(40));
    }];
    
//    [self.verify3Btn mas_updateConstraints:^(MASConstraintMaker *make) {
//        make.top.equalTo(self.recognizeBtn.mas_bottom).offset(20);
//        make.left.equalTo(self.view).offset(50);
//        make.width.equalTo(@(200));
//        make.height.equalTo(@(40));
//    }];
    [super updateViewConstraints];
}

@end

