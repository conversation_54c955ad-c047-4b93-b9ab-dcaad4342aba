//
//  PPOrderDetailViewCustomCell.m
//  ipaymentapp
//
//  Created by xiaoyangyuxuan on 2020/11/5.
//  Copyright © 2020 Meituan.com. All rights reserved.
//

#import "PPOrderDetailViewCustomCell.h"
#import <Masonry.h>

@interface PPOrderDetailViewCustomCell ()

@property (nonatomic, strong) UITextField *keyTextField;
@property (nonatomic, strong) UITextField *valueTextField;

@end

@implementation PPOrderDetailViewCustomCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        _keyTextField = ({
            UITextField *textField = [UITextField new];
            textField.placeholder = @"Key";
            textField.borderStyle = UITextBorderStyleRoundedRect;
            textField;
        });
        [_keyTextField addTarget:self action:@selector(keyTextFieldDidChange) forControlEvents:UIControlEventEditingChanged];

        _valueTextField = ({
            UITextField *textField = [UITextField new];
            textField.placeholder = @"Value";
            textField.borderStyle = UITextBorderStyleRoundedRect;
            textField.textAlignment = NSTextAlignmentRight;
            textField;
        });
        [_valueTextField addTarget:self action:@selector(valueTextFieldDidChange) forControlEvents:UIControlEventEditingChanged];

        
        [self.contentView addSubview:_keyTextField];
        [self.contentView addSubview:_valueTextField];
    }
    return self;
}

+ (BOOL)requiresConstraintBasedLayout
{
    return YES;
}

- (void)updateConstraints
{
    [self.keyTextField mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.left.equalTo(self.contentView).offset(5);
        make.bottom.equalTo(self.contentView.mas_bottom).offset(-5);
        make.width.mas_equalTo(self.contentView.width / 2);
    }];
    
    [self.valueTextField mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView).offset(5);
        make.bottom.equalTo(self.contentView.mas_bottom).offset(-5);
        make.right.equalTo(self.contentView.mas_right).offset(-5);
        make.width.mas_equalTo(self.contentView.width / 2);
    }];
    
    [super updateConstraints];
}

- (void)keyTextFieldDidChange
{
    self.didEditKeyBlock(self.keyTextField.text);
}

- (void)valueTextFieldDidChange
{
    self.didEditValueBlock(self.keyTextField.text, self.valueTextField.text);
}
@end
