//
//  PPOrderDetailViewController.m
//  SAKCashier
//
//  Created by honglianglu on 4/1/15.
//  Copyright (c) 2015 meituan. All rights reserved.
//
#import "SAKPortal.h"
#import "PPOrderDetailViewController.h"
#import "SAKOneClickDataManager.h"
#import "PPRefundedMoneyInputModalView.h"
#import "PPOrderDetailViewCustomCell.h"
#import <CIPFoundation.h>
#import "PPOrder.h"
#import <Masonry.h>
#import "TKAlertCenter.h"
#import "PPService.h"
#import "SPKActivityView.h"
#import <libextobjc/extobjc.h>
#import "PPOrderStatus.h"
#import "UIColor+Addition.h"

static const CGFloat kCellHeight = 40;

@interface PPOrderDetailViewController()

@property (nonatomic, strong) UIButton *refundAllButton;
@property (nonatomic, strong) UIButton *refundPartialButton;
@property (nonatomic, strong) UIButton *addRefundParamButton;
@property (nonatomic, strong) UILabel *orderStatusLabel;
@property (nonatomic, strong) UITableView *tableView;

@property (nonatomic, assign) NSInteger cellCount;
@property (nonatomic, strong) NSMutableDictionary *extraParams;

@end

@implementation PPOrderDetailViewController

- (void)viewDidLoad
{
    [super viewDidLoad];
    
    self.title = self.order.title;
    self.cellCount = 7;
    [self.view setBackgroundColor:[UIColor generalBackgroundColor]];
    
//    UIBarButtonItem *rightButton = [[UIBarButtonItem alloc] initWithTitle:@"刷新" style:UIBarButtonItemStylePlain target:self action:@selector(refreshOrderStatus)];
//    self.navigationItem.rightBarButtonItem = rightButton;
    
    self.tableView = ({
        UITableView *tableview = [[UITableView alloc] init];
        tableview.backgroundColor = [UIColor clearColor];
        tableview.delegate = self;
        tableview.dataSource = self;
        tableview.scrollEnabled = NO;
     //   [tableview registerClass:PPOrderDetailViewCustomCell.class forCellReuseIdentifier:NSStringFromClass([PPOrderDetailViewCustomCell class])];
        [self.view addSubview:tableview];
        tableview;
    });
    
    self.refundAllButton = ({
        UIButton *btn = [[UIButton alloc] init];
        [btn setBackgroundColor:[UIColor orangeColor]];
        [btn setTitle:@"退款" forState:UIControlStateNormal];
        btn.titleLabel.font = [UIFont systemFontOfSize:14];
        [btn addTarget:self action:@selector(refund) forControlEvents:UIControlEventTouchUpInside];
        [self.view addSubview:btn];
        btn;
    });
    
    self.refundPartialButton = ({
        UIButton *btn = [[UIButton alloc] init];
        [btn setBackgroundColor:[UIColor orangeColor]];
        [btn setTitle:@"部分退款" forState:UIControlStateNormal];
        btn.titleLabel.font = [UIFont systemFontOfSize:14];
        [btn addTarget:self action:@selector(refundPartial) forControlEvents:UIControlEventTouchUpInside];
        [self.view addSubview:btn];
        btn;
    });
    
    self.addRefundParamButton = ({
        UIButton *btn = [[UIButton alloc] init];
        [btn setBackgroundColor:[UIColor orangeColor]];
        [btn setTitle:@"添加参数" forState:UIControlStateNormal];
        btn.titleLabel.font = [UIFont systemFontOfSize:14];
        [btn addTarget:self action:@selector(addRefundParam) forControlEvents:UIControlEventTouchUpInside];
        [self.view addSubview:btn];
        btn;
    });
    
    self.orderStatusLabel = ({
        UILabel *label = [[UILabel alloc] init];
        label.font = [UIFont systemFontOfSize:14];
        label.textColor = [UIColor grayLabelTextColor];
        [self.view addSubview:label];
        label;
    });
    
    [self refreshUI];
}

- (void)refund
{
    [SPKNoneMaskBezelActivityView activityViewForView:self.view withLabel:nil];
    [[PPService defaultInstance] refundOrderWithOrder:self.order
                                          extraParams:[self.extraParams copy]
                                             Finished:^(CIPError *error) {
                                                 [SPKNoneMaskBezelActivityView removeView];
                                                 if (error) {
                                                     [[TKAlertCenter defaultCenter] postAlertWithMessage:@"退款失败"];
                                                 } else {
                                                     [[TKAlertCenter defaultCenter] postAlertWithMessage:@"退款成功"];
//                                                     [self refreshOrderStatus];
                                                 }
                                             }];
}

- (void)refundPartial
{
    PPRefundedMoneyInputModalView *modalView = [[PPRefundedMoneyInputModalView alloc] initWithRefundMoney:self.order.money];
    @weakify(self);
    SPKAlertView *alertView = [SPKAlertView showAlertViewWithTitle:nil
                                                        centerView:modalView
                                                 cancelButtonTitle:@"取消"
                                             completionButtonTitle:@"退款"
                                            cancelButtonTitleColor:HEXCOLOR(0x999999)
                                        completionButtonTitleColor:HEXCOLOR(0x222222)
                                                          canceled:^{}
                                                        completion:^{
        @strongify(self);
        if (modalView.moneyInputTextField.text) {
            NSString *previousOrderMoney = self.order.money;
            self.order.money = modalView.moneyInputTextField.text;
            [self refund];
            self.order.money = previousOrderMoney;
        }
    } tapOutsideToDismiss:NO];
    [alertView show];
}

- (void)addRefundParam
{
    self.cellCount += 1;
    [self updateViewConstraints];
    [self.tableView reloadData];
}

- (void)refreshOrderStatus
{
    [[PPService defaultInstance] refreshOrderWithOrder:self.order
                                            finished:^(PPOrder *order, CIPError *error) {
                                                if (error) {
                                                    [[TKAlertCenter defaultCenter] postAlertWithMessage:@"刷新失败"];
                                                } else {
                                                    [self setOrderValue:order];
                                                    [self refreshUI];
                                                }
                                            }];
}

- (void)viewDidAppear:(BOOL)animated
{
    [super viewDidAppear:animated];
    
    [self updateViewConstraints];
}

- (void)setOrderValue:(PPOrder *)order
{
    _order.status = order.status;
    _order.body = order.body;
}

- (void)refreshUI
{
    switch (self.order.status.integerValue) {
        case 0:
            self.refundAllButton.hidden = YES;
            self.refundPartialButton.hidden = YES;
            self.orderStatusLabel.text = @"未支付";
            break;
        case 4:
            self.refundAllButton.hidden = YES;
            self.refundPartialButton.hidden = YES;
            self.orderStatusLabel.text = @"用户已确认";
            break;
        case 6:
            self.refundAllButton.hidden = YES;
            self.refundPartialButton.hidden = YES;
            self.orderStatusLabel.text = @"商户已完结";
            break;
        case 13:
            self.refundAllButton.hidden = YES;
            self.refundPartialButton.hidden = YES;
            self.orderStatusLabel.text = @"支付关闭";
            break;
        case 14:
            self.refundAllButton.hidden = YES;
            self.refundPartialButton.hidden = YES;
            self.orderStatusLabel.text = @"支付失败";
            break;
        case 16:
            self.refundAllButton.hidden = NO;
            self.refundPartialButton.hidden = NO;
            self.orderStatusLabel.text = @"记账成功";
            break;
        case 64:
            self.refundAllButton.hidden = NO;
            self.refundPartialButton.hidden = NO;
            self.orderStatusLabel.text = @"通知业务线成功";
            break;
        case 96:
            self.refundAllButton.hidden = YES;
            self.refundPartialButton.hidden = YES;
            self.orderStatusLabel.text = @"部分退款";
            break;
        case 97:
            self.refundAllButton.hidden = YES;
            self.refundPartialButton.hidden = YES;
            self.orderStatusLabel.text = @"全部退款";
            break;
            
        default:
            self.refundAllButton.hidden = YES;
            self.refundPartialButton.hidden = YES;
            self.orderStatusLabel.text = @"未知状态";
    };
    
    [self.tableView reloadData];
    [self.view setNeedsUpdateConstraints];
}

- (void)updateViewConstraints
{
    [self.orderStatusLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.tableView.mas_bottom).offset(17);
        make.right.equalTo(self.view).offset(-15);
    }];
    
    if (!self.refundAllButton.hidden) {
        [self.refundAllButton mas_updateConstraints:^(MASConstraintMaker *make) {
            make.width.equalTo(@70);
            make.height.equalTo(@25);
            make.left.equalTo(self.view.mas_left).offset(15);
            make.top.equalTo(self.tableView.mas_bottom).with.offset(15);
        }];
        
    }
    
    if (!self.refundPartialButton.hidden) {
        [self.refundPartialButton mas_updateConstraints:^(MASConstraintMaker *make) {
            make.width.equalTo(@70);
            make.height.equalTo(@25);
            make.left.equalTo(self.refundAllButton.mas_right).offset(15);
            make.top.equalTo(self.tableView.mas_bottom).with.offset(15);
        }];
        
    }
    
    if (!self.addRefundParamButton.hidden) {
        [self.addRefundParamButton mas_updateConstraints:^(MASConstraintMaker *make) {
            make.width.equalTo(@70);
            make.height.equalTo(@25);
            make.left.equalTo(self.refundPartialButton.mas_right).offset(15);
            make.top.equalTo(self.tableView.mas_bottom).with.offset(15);
        }];
        
    }
    
    [self.tableView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view);
        make.left.equalTo(self.view);
        make.right.equalTo(self.view);
        make.height.equalTo(@(kCellHeight * self.cellCount));
    }];
    
    [super updateViewConstraints];
}

#pragma mark - UITableViewDataSource

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return self.cellCount > 7 ? 2 : 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    if (section == 0) {
        return 7;
    } else {
        return self.cellCount - 7;
    }
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    if (indexPath.section == 0) {
        UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass([UITableViewCell class])];
        if (!cell) {
            cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleValue1 reuseIdentifier:NSStringFromClass([UITableViewCell class])];
        }
        switch (indexPath.row) {
            case PPOrderDetailTitle:
                cell.textLabel.text = @"订单名称";
                cell.detailTextLabel.text = self.order.title;
                break;
                
            case PPOrderDetailID:
                cell.textLabel.text = @"订单编号";
                cell.detailTextLabel.text = self.order.orderID;
                break;
                
            case PPOrderDetailPayType:
                cell.textLabel.text = @"付款类型";
                cell.detailTextLabel.text = self.order.payType.stringValue;
                break;
            
            case PPOrderDetailMoney:
                cell.textLabel.text = @"金额";
                cell.detailTextLabel.text = [NSString stringWithFormat:@"%.2f", self.order.money.doubleValue];
                break;
            
            case PPOrderDetailOrderTime:
                cell.textLabel.text = @"下单时间";
                cell.detailTextLabel.text = [NSString stringWithFormat:@"%@", self.order.orderTime];
                break;
                
            case PPOrderDetailPayTime:
                cell.textLabel.text = @"支付时间";
                if (self.order.payTime != nil)
                    cell.detailTextLabel.text = [NSString stringWithFormat:@"%@", self.order.payTime];
                break;
                
            case PPOrderDetailStatus:
                cell.textLabel.text = @"状态";
                cell.detailTextLabel.text = [NSString stringWithFormat:@"%@", self.order.status];
                break;
                
            default:
                break;
        }
        
        cell.textLabel.font = [UIFont systemFontOfSize:14];
        cell.detailTextLabel.font = [UIFont systemFontOfSize:13];
        
        return cell;
    } else {
        PPOrderDetailViewCustomCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass([PPOrderDetailViewCustomCell class])];
        if (!cell) {
            cell = [[PPOrderDetailViewCustomCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:NSStringFromClass([PPOrderDetailViewCustomCell class])];
            @weakify(self);
            cell.didEditKeyBlock = ^(NSString *key) {
                @strongify(self);
                if (key) {
                    [self.extraParams removeObjectForKey:key];
                }
            };
            cell.didEditValueBlock = ^(NSString *key, NSString *value) {
                @strongify(self);
                if (key) {
                    [self.extraParams setValue:value forKey:key];
                }
            };
        }
        return cell;
    }
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return kCellHeight;
}

- (NSMutableDictionary *)extraParams
{
    if(!_extraParams) {
        _extraParams = [NSMutableDictionary new];
    }
    return _extraParams;
}
@end
