//
//  PPRefundedOrderUIObject.h
//  ipaymentapp
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 17/2/24.
//  Copyright © 2017年 Meituan.com. All rights reserved.
//

#import <Foundation/Foundation.h>

@class PPRefundedOrder;

@interface PPRefundedOrderUIObject : NSObject

@property (nonatomic, assign) NSInteger merchantNO;
@property (nonatomic, copy) NSString *partnerId;
@property (nonatomic, copy) NSString *acceptTime;
@property (nonatomic, copy) NSString *moneyCent;
@property (nonatomic, copy) NSString *source;
@property (nonatomic, copy) NSString *refundNo;
@property (nonatomic, copy) NSString *payOrderId;
@property (nonatomic, assign) NSInteger payRefundFlow;
@property (nonatomic, copy) NSString *refundStatus;
@property (nonatomic, copy) NSString *tradeNo;

- (instancetype)initWithRefundedOrder:(PPRefundedOrder *)refundedOrder;

@end
