//
//  PPRefundedOrder.h
//  ipaymentapp
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 17/2/24.
//  Copyright © 2017年 Meituan.com. All rights reserved.
//

#import <SAKDomainObject/SAKDomainObject.h>
#import "CIPFoundationMacros.h"

@interface PPRefundedOrder : SAKDomainObject

@property (nonatomic, copy) NSString *partnerID; //业务线id
@property (nonatomic, assign) NSInteger merchantNO; //商户号
@property (nonatomic, assign) NSInteger moneyCent; //退款总金额，单位为分
@property (nonatomic, copy) NSString *status; //退款状态
@property (nonatomic, assign) NSTimeInterval addTime; //退款记录生成时间戳，单位为秒
@property (nonatomic, copy) NSString *payOrderID; //支付单id
@property (nonatomic, assign) NSInteger payRefundFlow; //聚合退款流水号
@property (nonatomic, copy) NSString *refundNo; //业务线退款流水号
@property (nonatomic, assign) NSInteger source; //1代表支付平台自动退款
@property (nonatomic, copy) NSString *tradeNo; //订单的支付交易号


@end

