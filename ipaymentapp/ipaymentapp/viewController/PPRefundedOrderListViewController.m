//
//  PPRefundedOrderListViewController.m
//  ipaymentapp
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 17/2/24.
//  Copyright © 2017年 Meituan.com. All rights reserved.
//

#import "PPRefundedOrderListViewController.h"
#import <extobjc.h>
#import <Masonry.h>
#import "SPKCommonTableView.h"
#import "SAKOneClickDataManager.h"
#import "PPRefundedOrderCellTableViewCell.h"
#import "PPRefundedOrderViewModel.h"
#import "SPKActivityView.h"
#import <ReactiveCocoa/ReactiveCocoa.h>
#import <CIPStringAdditions.h>
#import "PPRefundedOrderUIObject.h"
#import "UIColor+Addition.h"
#import "SAKEnvironment.h"

@interface PPRefundedOrderListViewController ()

@property (nonatomic, strong) SPKCommonTableView *tableView;
@property (nonatomic, strong) PPRefundedOrderViewModel *viewModel;
@property (nonatomic, strong) UIButton *queryBtn;
@property (nonatomic, strong) UITextField *useridTextField;

@end

@implementation PPRefundedOrderListViewController

- (instancetype)initWithViewModel:(PPRefundedOrderViewModel *)viewModel;
{
    self = [super init];
    if (self) {
        _viewModel = viewModel;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.title = @"退款列表";
    [self setupUI];
    [self bindViewModel];
    [self loadCommand];
    
    [self.view setNeedsUpdateConstraints];
}

- (void)setupUI
{
    self.useridTextField = ({
        UITextField *textField = [[UITextField alloc] init];
        if ([SAKEnvironment environment].user.userID) {
            textField.text = [NSString cipf_stringFromNumber:[SAKEnvironment environment].user.userID];
        } else {
            textField.placeholder = @"请输入 UserID";
        }
        textField.borderStyle = UITextBorderStyleRoundedRect;
        textField;
    });
    
    self.queryBtn = ({
        UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
        [button setBackgroundColor:HEXCOLOR(0xFFC300)];
        [button setTitle:@"查询" forState:UIControlStateNormal];
        [button addTarget:self action:@selector(loadCommand) forControlEvents:UIControlEventTouchUpInside];
        button;
    });
    self.queryBtn.layer.cornerRadius = 5.0f;
    
    self.tableView = ({
        SPKCommonTableView *tableView = [[SPKCommonTableView alloc] init];
        tableView.defaultCellTemplate = [PPRefundedOrderCellTableViewCell class];
        tableView.enableRefresh = YES;
        tableView.enableLoadMore = YES;
        tableView.loadMoreCommand = self.viewModel.loadMoreCommand;
        tableView.refreshCommand = self.viewModel.refreshCommand;
        tableView.backgroundColor = [UIColor clearColor];
        tableView;
    });
    
    [self.view setBackgroundColor:[UIColor generalBackgroundColor]];
    [self.view addSubview:self.queryBtn];
    [self.view addSubview:self.useridTextField];
    [self.view addSubview:self.tableView];
}

- (void)bindViewModel
{
    RAC(self, tableView.listDataSource) = RACObserve(self, viewModel.fetchedResultsController);
    RAC(self, tableView.enableLoadMore) = RACObserve(self, viewModel.hasMoreList);
   
    @weakify(self);
    self.tableView.itemSelectedCommand = [[RACCommand alloc] initWithSignalBlock:^RACSignal *(PPRefundedOrderUIObject *input) {
        @strongify(self);
        NSString *host = @"http://stable.pay.test.sankuai.com";
        NSInteger defaultEnvironmentId = [[SAKOneClickDataManager sharedManager] defaultEnvironmentId];
        switch (defaultEnvironmentId) {
            case 1000:
            case 3562:
                host = @"https://mpay.meituan.com";
                break;
            case 1001:
            case 2512:
                host = @"http://stable.pay.st.sankuai.com";
                break;
            case 1003:
                host = @"http://stable.pay.dev.sankuai.com";
                break;
            default:
                host = @"http://stable.pay.test.sankuai.com";
                break;
        }
        
        NSString *detailRefundURL = [[NSString stringWithFormat:@"%@/resource/balance/refund.html?trade_no=%@&status=2&refund_no=%@&reject_time=0&apply_time=0&delay_time=0", host, input.tradeNo,  input.refundNo] cipf_URLEncodedString];
        NSURL *URL = [NSURL URLWithString:[NSString stringWithFormat:@"meituanpayment://www.meituan.com/web?url=%@",detailRefundURL]];
        [SAKPortal transferFromViewController:self toURL:URL completion:^(UIViewController<SAKPortalable> * _Nullable viewController, NSError * _Nullable error) {
        }];
        
        return [RACSignal empty];
    }];
}

- (void)updateViewConstraints
{
    [self.queryBtn mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(10);
        make.right.equalTo(self.view.mas_right).offset(-10);
        make.width.mas_equalTo(75);
        make.height.mas_equalTo(40);
    }];
    
    [self.useridTextField mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.left.equalTo(self.view).offset(10);
        make.top.equalTo(self.view).offset(10);
        make.right.equalTo(self.queryBtn.mas_left).offset(-10);
        make.height.mas_equalTo(40);
    }];
    
    [self.tableView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.useridTextField.mas_bottom);
        make.left.right.bottom.equalTo(self.view);
    }];
    
    [super updateViewConstraints];
}

- (void)loadCommand
{
    [SPKBezelActivityView activityViewForView:self.view];
    self.viewModel.queryUserid = self.useridTextField.text;
    
    [[[self.viewModel loadCommand] execute:nil] subscribeCompleted:^{
        [SPKBezelActivityView removeViewAnimated:YES];
      
    }];
    [self.viewModel.loadCommand.errors subscribeNext:^(id x) {
        [SPKBezelActivityView removeViewAnimated:YES];
    }];
}

@end
