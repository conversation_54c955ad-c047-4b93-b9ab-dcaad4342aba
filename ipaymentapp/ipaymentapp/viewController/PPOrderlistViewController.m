//
//  PPOrderListViewController.m
//  SAKCashier
//
//  Created by sunhl on 15/3/31.
//  Copyright (c) 2015年 meituan. All rights reserved.
//

#import "PPOrderListViewController.h"
#import "PPOrder.h"
#import "PPService.h"

#import "TKAlertCenter.h"
#import "TKAlertCenter+NSError.h"
#import "PPOrderTableViewCell.h"

#import "PPOrderDetailViewController.h"
#import "SPKActivityView.h"

@interface PPOrderListViewController ()

@property (nonatomic, strong) NSArray *orderArray;
@property (nonatomic, strong) PPOrderTableViewCell *sizingCell;

@end

@implementation PPOrderListViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.

    self.title = @"订单列表";
    
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    
    self.sizingCell = [[PPOrderTableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:NSStringFromClass([PPOrderTableViewCell class])];
    
    [SPKNoneMaskBezelActivityView activityViewForView:self.view withLabel:@"加载中..."];
    
    [self refreshOrderList];
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

- (void)removeOrderArrayObserver
{
    for (PPOrder *order in self.orderArray) {
        [order removeObserver:self forKeyPath:@"status"];
    }
}

- (void)addOrderArrayObserver
{
    for (PPOrder *order in self.orderArray) {
        [order addObserver:self forKeyPath:@"status" options:NSKeyValueObservingOptionNew context:nil];
    }
}

-(void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary *)change context:(void *)context
{
    if ([keyPath  isEqualToString: @"status"]) {
        [self refreshOrderList];
    } else {
        [super observeValueForKeyPath:keyPath ofObject:object change:change context:context];
    }
}


#pragma - mark tableview delegate

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return [self.orderArray count];
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    self.sizingCell.order = self.orderArray[indexPath.row];
    
    [self.sizingCell updateConstraintsIfNeeded];
    
    CGFloat height = self.sizingCell.intrinsicContentSize.height;
    
    return height + 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    PPOrderTableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass([PPOrderTableViewCell class])];
    if (!cell) {
        cell = [[PPOrderTableViewCell alloc] initWithStyle:UITableViewCellStyleDefault
                                           reuseIdentifier:NSStringFromClass([PPOrderTableViewCell class])];
    }
    
    cell.order = self.orderArray[indexPath.row];
    
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [self.tableView deselectRowAtIndexPath:indexPath animated:YES];
    
    PPOrder *order = [self.orderArray objectAtIndex:indexPath.row];
    PPOrderDetailViewController *orderDetailViewController = [PPOrderDetailViewController new];
    orderDetailViewController.order = order;
    [self.navigationController pushViewController:orderDetailViewController animated:YES];
}

- (void)refreshOrderList
{
    [[PPService defaultInstance] getOrderListFinished:^(NSArray *orderArray, CIPError *error) {
        [SPKNoneMaskBezelActivityView removeView];
        if (error) {
            [[TKAlertCenter defaultCenter] postAlertWithError:error];
        } else {
            [self removeOrderArrayObserver];
            self.orderArray = [orderArray copy];;
            [self addOrderArrayObserver];
            [self.tableView reloadData];
        }
    }];
}

- (void)dealloc
{
    [self removeOrderArrayObserver];
}

@end
