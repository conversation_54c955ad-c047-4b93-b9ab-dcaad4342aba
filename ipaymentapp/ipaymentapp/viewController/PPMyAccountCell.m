//
//  PPMyAccountCell.m
//  SAKCashier
//
//  Created by wang<PERSON> on 15/6/16.
//  Copyright (c) 2015年 meituan. All rights reserved.
//

#import "PPMyAccountCell.h"
#import "SAKUIKitMacros.h"
#import "Masonry.h"
#import "UIColor+Addition.h"

@interface PPMyAccountCell ()

@property (nonatomic, strong) UIImageView *iconImageView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *rightLabel;
@property (nonatomic, strong) UIImageView *rightArrowView;
@property (nonatomic, strong) UIView *separatorLine;

@end

@implementation PPMyAccountCell

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [UIColor generalBackgroundColor];
        
        _iconImageView = ({
            UIImageView *imageView = [[UIImageView alloc] init];
            imageView.contentMode = UIViewContentModeCenter;
            [self addSubview:imageView];
            imageView;
        });
        
        _titleLabel = ({
            UILabel *label = [[UILabel alloc] init];
            label.textColor = [UIColor generalLabelTextColor];
            label.font = Font(15);
            label.text = self.titleString;
            [self addSubview:label];
            label;
        });
        
        _rightLabel = ({
            UILabel *label = [[UILabel alloc] init];
            label.textColor = [UIColor generalLabelTextColor];
            label.font = Font(13);
            label.text = self.titleDescription;
            [self addSubview:label];
            label;
        });
        
        _rightArrowView = ({
            UIImageView *imageView = [[UIImageView alloc] init];
            imageView.image = [UIImage imageNamed:@"icon_main_cell_rightArrow"];
            [self addSubview:imageView];
            imageView;
        });

        _separatorLine = ({
            UIView *view = [[UIView alloc] init];
            view.backgroundColor = [UIColor generalSeparatorLineColor];
            [self addSubview:view];
            view;
        });
    }
    return self;
}

+ (BOOL)requiresConstraintBasedLayout
{
    return YES;
}

- (void)updateConstraints
{
    [self.iconImageView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self).offset(18);
        make.centerY.equalTo(self);
        make.width.equalTo(@22);
        make.height.equalTo(@22);
    }];
    
    [self.titleLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.iconImageView.mas_right).offset(16);
        make.centerY.equalTo(self);
        make.height.equalTo(@16);
    }];
    
    [self.rightLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.rightArrowView.mas_left).offset(-12);
        make.centerY.equalTo(self);
        make.height.equalTo(@16);
    }];
    
    [self.rightArrowView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self).offset(-18);;
        make.centerY.equalTo(self);
        make.width.equalTo(@7);
        make.height.equalTo(@12);
    }];

    [self.separatorLine mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self);
        make.bottom.equalTo(self);
        make.right.equalTo(self);
        make.height.equalTo(@0.5);
    }];
    
    [super updateConstraints];
}

- (void)setTitleString:(NSString *)titleString
{
    _titleLabel.text = titleString;
    _titleString = titleString;
}

- (void)setTitleDescription:(NSString *)titleDescription
{
    _titleDescription = titleDescription;
    self.rightLabel.text = titleDescription;
}

- (void)setIcon:(UIImage *)icon
{
    _iconImageView.image = icon;
}

- (void)setIsHighlight:(BOOL)isHighlight
{
    _isHighlight = isHighlight;
    if (isHighlight) {
        self.rightLabel.textColor = HEXCOLOR(0x666666);
    } else {
        self.rightLabel.textColor = HEXCOLOR(0xffce7e);
    }
}

@end
