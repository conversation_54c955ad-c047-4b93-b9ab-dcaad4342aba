//
//  MESHH5DebugViewController.m
//  SAKMesh_Example
//
//  Created by l<PERSON><PERSON><PERSON> on 2019/3/8.
//  Copyright © 2019年 jlaix. All rights reserved.
//

#import "MESHH5DebugViewController.h"

#import "MESHCenter.h"
#import <UIKit/UIKit.h>
#import "View+MASAdditions.h"

@interface MESHH5DebugViewController ()

@property (nonatomic, strong) UIButton *btn1;

@end

@implementation MESHH5DebugViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self setupUI];
    
}

- (void)setupUI
{
    [self.view setBackgroundColor:[UIColor whiteColor]];
    
    self.btn1 = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.btn1 setBackgroundColor:HEXCOLOR(0x06c1ae)];
    [self.btn1 setTitle:@"Native调用H5" forState:UIControlStateNormal];
    [self.btn1 addTarget:self action:@selector(h5DebugBtnClicked:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.btn1];
    
    [self makeViewConstraints];
}

- (void)makeViewConstraints
{
    [self.btn1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.view).offset(-150);
        make.left.equalTo(self.view).offset(30);
        make.right.equalTo(self.view).offset(-30);
        make.height.mas_equalTo(44);
    }];
    
    [super updateViewConstraints];
}

- (void)h5DebugBtnClicked:(id)sender
{
    MESHBusResult *t1 = [[MESHBusResult alloc] initWithTargetType:kMESH_PLATFROM_H5];
    NSArray *ada = @[t1];
    
    NSDictionary *param = @{@"content" : @"这是提示信息", @"duration" : @(1)};
    NSURL *url1 = [NSURL meshScheme];
    
    NSURL *url2 = [NSURL meshScheme].mesh_businessId(@"pay");
    
    NSURL *url = [NSURL meshScheme].mesh_businessId(@"pay").mesh_service(@"KNB").mesh_api(@"toast").mesh_parameters(param).mesh_apiAdaptIds(ada);
    NSLog(url1.absoluteString);
    NSLog(url2.absoluteString);
    NSLog(url.absoluteString);
    
    [MESHCenter useAPIInViewController:self withURL:url
                           URLCallback:^(MESHURLWrapper * _Nonnull resultURLWrapper) {
                               NSLog(resultURLWrapper.status);
                           }];
}

@end
