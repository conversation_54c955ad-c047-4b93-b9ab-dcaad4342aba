//
//  SAKPayMeshService.m
//  ipaymentapp
//
//  Created by liq<PERSON><PERSON> on 2019/2/19.
//  Copyright © 2019年 Meituan.com. All rights reserved.
//

#import "SAKPayMeshService.h"
#import "MESHServiceProtocol.h"
#import "MESHRequest.h"

#import "TKAlertCenter.h"

#import "SPKScanCardViewController.h"

@interface SAKPayMeshService () <UIAlertViewDelegate>

@property (nonatomic, strong) MESHRequest *alertCachedRequest;

@property (nonatomic, strong) MESHRequest *scanCardCachedRequest;

@end

@implementation SAKPayMeshService

// 使用宏来注册 ServiceName 及其实现类。
MESHServiceRegister(@"pay", [SAKPayMeshService new]);

- (void)mesh_toast:(MESHRequest *)meshRequest
{
    NSString *content = meshRequest.URLWrapper.parameters[@"content"];
    
    [[TKAlertCenter defaultCenter] postAlertWithMessage:content ?: @"无内容"];
    
    [self.manager successCallbackWithRequest:meshRequest responseData:@{@"result":@"toast api调用完成"}];
}

- (void)mesh_alert:(MESHRequest *)meshRequest
{
    NSString *content = meshRequest.URLWrapper.parameters[@"message"];
    NSString *title = meshRequest.URLWrapper.parameters[@"title"];
    
    self.alertCachedRequest = meshRequest;
    UIAlertView *alert = [[UIAlertView alloc]initWithTitle:@"提示"
                                                   message:content ?: @"无内容"
                                                  delegate:self
                                         cancelButtonTitle:title ?: @"确定"
                                         otherButtonTitles:nil];
    [alert show];
}

-(void)alertView:(UIAlertView *)alertView clickedButtonAtIndex:(NSInteger)buttonIndex
{
    if (self.alertCachedRequest) {
        [self.manager successCallbackWithRequest:self.alertCachedRequest responseData:@{@"result":@"alert api调用完成"}];
        self.alertCachedRequest = nil;
    }
}

- (void)mesh_scanCard:(MESHRequest *)meshRequest
{
    self.scanCardCachedRequest = meshRequest;
    
    SPKScanCardViewController *controller = [[SPKScanCardViewController alloc] init];
    @weakify(self)
    [controller setCompletionBlock:^(NSString *cardNumber){
        @strongify(self)
        [self callbackWithCardNumber:cardNumber];
    }];
    [meshRequest.sourceViewController.navigationController pushViewController:controller animated:YES];
}

-(void)callbackWithCardNumber:(NSString *)cardNumber
{
    if (self.scanCardCachedRequest) {
        [self.manager successCallbackWithRequest:self.scanCardCachedRequest responseData:@{@"cardNumber":cardNumber}];
        self.scanCardCachedRequest = nil;
    }
}

@end
