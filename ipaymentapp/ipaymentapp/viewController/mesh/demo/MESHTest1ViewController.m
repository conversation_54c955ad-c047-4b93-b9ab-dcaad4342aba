//
//  MESHTest1ViewController.m
//  SAKMesh_Example
//
//  Created by l<PERSON><PERSON><PERSON> on 2019/3/11.
//  Copyright © 2019年 jlaix. All rights reserved.
//

#import "MESHTest1ViewController.h"

static NSString * const kMESH_TEST_1_URL = @"pay://test/launch";

@interface MESHTest1ViewController ()

@property (nonatomic, strong) UILabel *label;

@end

@implementation MESHTest1ViewController


SAK_PORTAL_REGISTER()
{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        [SAKPortal registerPortalWithHandler:^UIViewController<SAKPortalable> * _Nullable(NSURL * _Nonnull URL, BOOL shouldTransfer, UIViewController * _Nonnull sourceViewController) {
            if ([URL hasSameTrunkWithURL:[NSURL URLWithString:kMESH_TEST_1_URL]]) {
                MESHTest1ViewController *testVC = [[MESHTest1ViewController alloc] init];
                [SAKPortal showViewController:testVC
                           fromViewController:sourceViewController
                                     viaSegue:SAKSeguePush];
                return testVC;
            } else {
                return nil;
            }
        }
                                   prefixURL:[NSURL URLWithString:kMESH_TEST_1_URL]
                                    pageInfo:[SAKPortalPageInfo pageInfoWithPageName:@"Native测试页1"
                                                                           className:@"MESHTest1ViewController"
                                                                                path:kMESH_TEST_1_URL
                                                                  requiredParameters:nil
                                                                  optionalParameters:@""]];
    });
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self setupUI];
}

- (void)setupUI
{
    [self.view setBackgroundColor:[UIColor whiteColor]];
    
    self.label = [UILabel new];
    [self.label setFont:Font(20)];
    [self.label setText:@"Native 美团首页"];
    [self.view addSubview:self.label];
    
    [self makeViewConstraints];
}


- (void)makeViewConstraints
{
    [self.label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.view);
    }];
    
    [super updateViewConstraints];
}


@end
