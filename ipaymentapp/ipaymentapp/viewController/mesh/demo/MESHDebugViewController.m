//
//  MESHDebugViewController.m
//  ipaymentapp
//
//  Created by liq<PERSON><PERSON> on 2019/2/20.
//  Copyright © 2019年 Meituan.com. All rights reserved.
//

#import "MESHDebugViewController.h"
#import "MESHH5DebugViewController.h"

#import "MESHCenter.h"
#import <UIKit/UIKit.h>
#import "View+MASAdditions.h"
#import "UIColor+Addition.h"

#import "MTFUSubmitButton.h"
#import "TKAlertCenter.h"
#import "NSURL+MESH.h"

@interface MESHDebugViewController ()

@property (nonatomic, strong) MTFUSubmitButton *componentSchemeBtn;
@property (nonatomic, strong) MTFUSubmitButton *vcSchemeBtn;
@property (nonatomic, strong) MTFUSubmitButton *scanCardBtn;

@property (nonatomic, strong) UITextView *schemeTextView;

@property (nonatomic, strong) MTFUSubmitButton *nativeDebugBtn;
@property (nonatomic, strong) MTFUSubmitButton *h5DebugBtn;
@property (nonatomic, strong) MTFUSubmitButton *autoTestBtn;

@end

@implementation MESHDebugViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self setupUI];
 
}

- (void)setupUI
{
    [self.view setBackgroundColor:[UIColor generalBackgroundColor]];
    
    self.componentSchemeBtn = [MTFUSubmitButton strokedButton];
    self.componentSchemeBtn.titleLabel.font = BoldFont(12);
    [self.componentSchemeBtn setTitle:@"模块级Scheme" forState:UIControlStateNormal];
    [self.componentSchemeBtn addTarget:self action:@selector(componentBtnClicked:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.componentSchemeBtn];
    
    self.vcSchemeBtn = [MTFUSubmitButton strokedButton];
    self.vcSchemeBtn.titleLabel.font = BoldFont(12);
    [self.vcSchemeBtn setTitle:@"页面级Scheme" forState:UIControlStateNormal];
    [self.vcSchemeBtn addTarget:self action:@selector(vcBtnClicked:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.vcSchemeBtn];
    
    self.scanCardBtn = [MTFUSubmitButton strokedButton];
    self.scanCardBtn.titleLabel.font = BoldFont(12);
    [self.scanCardBtn setTitle:@"银行卡OCR" forState:UIControlStateNormal];
    [self.scanCardBtn addTarget:self action:@selector(scanCardBtnClicked:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.scanCardBtn];
    
    
    self.schemeTextView = [[UITextView alloc] init];
    if (@available(iOS 13.0, *)) {
        self.schemeTextView.tintColor = HEXCOLOR(0x06c1ae);
        self.schemeTextView.textColor = [UIColor systemGray2Color];
        self.schemeTextView.backgroundColor = [UIColor secondarySystemBackgroundColor];
    } else {
        self.schemeTextView.tintColor = HEXCOLOR(0x06c1ae);
        self.schemeTextView.textColor = HEXCOLOR(0x8e8d8e);
        self.schemeTextView.backgroundColor = HEXCOLOR(0xf4f5f6);
    }
    self.schemeTextView.font = Font(18);
    [self.schemeTextView.layer setCornerRadius:2];
    [self.view addSubview:self.schemeTextView];
    
    
    self.nativeDebugBtn = [MTFUSubmitButton button];
    [self.nativeDebugBtn setTitle:@"Native 调用 Scheme" forState:UIControlStateNormal];
    [self.nativeDebugBtn addTarget:self action:@selector(nativeDebugBtnClicked:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.nativeDebugBtn];
    

    self.h5DebugBtn = [MTFUSubmitButton button];
    [self.h5DebugBtn setTitle:@"H5 能力调试页" forState:UIControlStateNormal];
    [self.h5DebugBtn addTarget:self action:@selector(h5DebugBtnClicked:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.h5DebugBtn];
    
    self.autoTestBtn = [MTFUSubmitButton button];
    [self.autoTestBtn setTitle:@"批量巡检页" forState:UIControlStateNormal];
    [self.autoTestBtn addTarget:self action:@selector(autoTestBtnClicked:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.autoTestBtn];
    
    [self makeViewConstraints];
}

- (void)makeViewConstraints
{
    [self.componentSchemeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(100);
        make.left.equalTo(self.view).offset(30);
        make.width.equalTo(self.view.mas_width).multipliedBy(0.3).offset(-18);
        make.height.mas_equalTo(44);
    }];
    
    [self.vcSchemeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(100);
        make.centerX.equalTo(self.view);
        make.width.equalTo(self.view.mas_width).multipliedBy(0.3).offset(-18);
        make.height.mas_equalTo(44);
    }];
    
    [self.scanCardBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(100);
        make.right.equalTo(self.view).offset(-30);
        make.width.equalTo(self.view.mas_width).multipliedBy(0.3).offset(-18);
        make.height.mas_equalTo(44);
    }];
    
    [self.schemeTextView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.componentSchemeBtn.mas_bottom).offset(30);
        make.left.equalTo(self.view).offset(30);
        make.right.equalTo(self.view).offset(-30);
        make.bottom.equalTo(self.nativeDebugBtn.mas_top).offset(-30);
    }];
    
    [self.nativeDebugBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.h5DebugBtn.mas_top).offset(-20);
        make.left.equalTo(self.view).offset(30);
        make.right.equalTo(self.view).offset(-30);
        make.height.mas_equalTo(44);
    }];
    
    [self.h5DebugBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.autoTestBtn.mas_top).offset(-20);
        make.left.equalTo(self.view).offset(30);
        make.right.equalTo(self.view).offset(-30);
        make.height.mas_equalTo(44);
    }];
    
    [self.autoTestBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.view).offset(-30);
        make.left.equalTo(self.view).offset(30);
        make.right.equalTo(self.view).offset(-30);
        make.height.mas_equalTo(44);
    }];
    
    
    [super updateViewConstraints];
}

- (void)h5DebugBtnClicked:(id)sender
{
    MESHH5DebugViewController *controller = [[MESHH5DebugViewController alloc] init];
    
    controller.leftButtonNormalTextColor = HEXCOLOR(0x01b7a4);
    controller.leftButtonHighlightedTextColor = HEXCOLOR(0x01b7a4);
    
//    controller.URL = [NSURL URLWithString:@"http://s3plus.sankuai.com/v1/mss_8d26ed2d4a0c400a9dd544e646e77a1e/mesh/page/mesh.html"];
    
    [controller loadURL:[NSURL URLWithString:@"http://s3plus.sankuai.com/v1/mss_8d26ed2d4a0c400a9dd544e646e77a1e/mesh/page/case_payment_lanuch.html"]];
    
    
    [self.navigationController pushViewController:controller animated:YES];
}

- (void)autoTestBtnClicked:(id)sender
{
    
//    NSURL *url = [NSURL URLWithString:@"imeituan\://www.meituan.com/web?notitlebar\=1&url\=http%3a%2f%2fecoo.fe.fd.dev.sankuai.com%2fappdevportal%2fknb_test.html"];
    // 美团
//    NSURL *url = [NSURL URLWithString:@"imeituan\://www.meituan.com/web?notitlebar\=1&noquery=1&url\=http://s3plus.sankuai.com/v1/mss_8d26ed2d4a0c400a9dd544e646e77a1e/mesh/page/case_knb_getUA_v0.html"];
    // 点评
//    NSURL *url = [NSURL URLWithString:@"dianping\://web?notitlebar\=1&noquery=1&url\=http://s3plus.sankuai.com/v1/mss_8d26ed2d4a0c400a9dd544e646e77a1e/mesh/page/case_auto_test.html"];
    // 外卖
    NSURL *url = [NSURL URLWithString:@"meituanwaimai\://waimai.meituan.com/browser?inner_url\=http://s3plus.sankuai.com/v1/mss_8d26ed2d4a0c400a9dd544e646e77a1e/mesh/page/case_auto_test.html"];
    
    if ([[UIApplication sharedApplication] canOpenURL:url]) {
        NSLog(@"liqiyu: 可打开页面");
        [[UIApplication sharedApplication] openURL:url];
    } else {
        NSLog(@"liqiyu: 不可打开页面");
    }
}


- (void)nativeDebugBtnClicked:(id)sender
{
    
    NSString *urlString = [self.schemeTextView.text stringByAddingPercentEncodingWithAllowedCharacters:NSCharacterSet.URLQueryAllowedCharacterSet];

    NSURL *url = [NSURL URLWithString:urlString ?: @""];
    
    
    
    
    
    
    
//    MESHApiType *t1 = [[MESHApiType alloc] initWithTargetType:kMESH_PLATFROM_NATIVE];
//    NSArray *ada = @[t];
//
//    NSDictionary *param = @{@"content" : @"Native Toast 模块调用成功！"};
//
//    NSURL *url = [NSURL meshScheme].businessId(@"Pay").service(@"pay").api(@"toast").parameters(param).apiAdaptIds(ada);
    
    
//    self.schemeTextView.text = @"mesh://?sourceType=ios&businessId=Pay&service=pay&api=scanCard&apiAdaptIds=[{\"targetType\":\"native\"}]";
    
//    NSURL *url = [NSURL meshScheme].mesh_service(@"pay").mesh_api(@"scanCard");
    
    
    [MESHCenter useAPIInViewController:self withURL:url
                           URLCallback:^(MESHURLWrapper * _Nonnull resultURLWrapper) {
                               NSLog(@"API调用结束");
                               [[TKAlertCenter defaultCenter] postAlertWithMessage:[NSURL mesh_convertDictToJSONString:resultURLWrapper.data]];
                           }];
}


- (void)componentBtnClicked:(id)sender
{
    // 链式调用生成的 url 是 encode 过的，暂时不演示
    MESHBusResult *t1 = [[MESHBusResult alloc] initWithTargetType:kMESH_PLATFROM_NATIVE];
    MESHBusResult *t2 = [[MESHBusResult alloc] initWithTargetType:kMESH_PLATFROM_H5];
    NSArray *ada = @[t1, t2];

    NSDictionary *param = @{@"content" : @"Native Toast 模块调用成功！"};

    NSURL *url = [NSURL meshScheme].mesh_businessId(@"Pay").mesh_service(@"pay").mesh_api(@"toast").mesh_parameters(param).mesh_apiAdaptIds(ada);
    
    
    
    
//    self.schemeTextView.text = @"mesh://?sourceType=ios&businessId=Pay&service=pay&api=toast&parameters={\"content\":\"Native Toast 模块调用成功！\"}&apiAdaptIds=[{\"targetType\":\"native\"},{\"targetType\":\"h5\"}]";
    
    self.schemeTextView.text = @"mesh://?sourceType=ios&businessId=Pay&service=pay&api=toast&parameters={\"content\":\"Native Toast 模块调用成功！\"}";
}


- (void)vcBtnClicked:(id)sender
{
//    NSDictionary *param = @{@"url" : @"pay://test/launch"};
////    NSDictionary *param = @{@"url" : @"https://i.meituan.com"};
//
//    NSURL *url = [NSURL meshScheme].businessId(@"Pay").service(@"mesh").api(@"openUrl").parameters(param);
//
//    self.schemeTextView.text = url.absoluteString;
    
    self.schemeTextView.text = @"mesh://?sourceType=ios&businessId=Pay&service=mesh&api=openUrl&parameters={\"url\":\"pay://test/launch\"}";
}

- (void)scanCardBtnClicked:(id)sender
{
    //    NSDictionary *param = @{@"url" : @"pay://test/launch"};
    ////    NSDictionary *param = @{@"url" : @"https://i.meituan.com"};
    //
    //    NSURL *url = [NSURL meshScheme].businessId(@"Pay").service(@"mesh").api(@"openUrl").parameters(param);
    //
    //    self.schemeTextView.text = url.absoluteString;
    
    
//    self.schemeTextView.text = @"mesh://?sourceType=ios&businessId=Pay&service=pay&api=scanCard&apiAdaptIds=[{\"targetType\":\"native\"}]";
    
    
    // MOCK
    NSDictionary *d = [[NSProcessInfo processInfo] environment];
    self.schemeTextView.text = d.tt_JSONString;
}

@end
