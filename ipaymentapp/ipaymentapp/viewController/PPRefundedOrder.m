//
//  PPRefundedOrder.m
//  ipaymentapp
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 17/2/24.
//  Copyright © 2017年 Meituan.com. All rights reserved.
//

#import "PPRefundedOrder.h"
#import <extobjc.h>

@implementation PPRefundedOrder

+ (NSDictionary *)predicateDictionary
{
    return @{ @keypath(PPRefundedOrder.new, partnerID): SAKDomainPredicate.wasString.isString.JSONKey(@"partnerId"),
               @keypath(PPRefundedOrder.new, merchantNO): SAKDomainPredicate.wasNumber.isNumber.JSONKey(@"sellerId"),
               @keypath(PPRefundedOrder.new, moneyCent): SAKDomainPredicate.wasNumber.isNumber.JSONKey(@"moneyCent"),
               @keypath(PPRefundedOrder.new, status): SAKDomainPredicate.wasString.isString.JSON<PERSON><PERSON>(@"refundStatus"),
               @keypath(PPRefundedOrder.new, addTime): SAKDomainPredicate.wasNumber.isNumber.JSONKey(@"acceptTime"),
               @keypath(PPRefundedOrder.new, payOrderID): SAKDomainPredicate.wasString.isString.JSONKey(@"payOrderId"),
               @keypath(PPRefundedOrder.new, payRefundFlow): SAKDomainPredicate.wasNumber.isNumber.JSONKey(@"payRefundFlow"),
              @keypath(PPRefundedOrder.new, refundNo): SAKDomainPredicate.wasString.isString.JSONKey(@"refundNo"),
              @keypath(PPRefundedOrder.new, source): SAKDomainPredicate.wasNumber.isNumber.JSONKey(@"source"),
              @keypath(PPRefundedOrder.new, tradeNo): SAKDomainPredicate.wasString.isString.JSONKey(@"tradeNo"),
               };
}

@end
