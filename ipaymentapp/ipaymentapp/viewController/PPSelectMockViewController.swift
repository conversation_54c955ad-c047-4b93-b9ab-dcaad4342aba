//
//  PPSelectMockViewController.swift
//  ipaymentapp
//
//  Created by qinchengbo on 2018/3/14.
//  Copyright © 2018年 Meituan.com. All rights reserved.
//

import UIKit

class PPSelectMockViewController: MTBaseViewController, UITableViewDelegate, UITableViewDataSource {
    
    let tableView = UITableView(frame: .zero)
    var dataSource = [String]()

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        dataSource.append(contentsOf: ["APPMock开启面板",
                                       "PortM MOCK开关",
                                       "模拟器注册APPMOCK（可以按CMD+Z打开）",
                                       "Picasso HTTP 环境"])
        tableView.reloadData()
    }
    
    func setupUI() {
        tableView.backgroundColor = .clear
        tableView.delegate = self
        tableView.dataSource = self
        tableView.tableFooterView = UIView(frame: .zero)
        view.addSubview(tableView)
        view.backgroundColor = .generalBackground
        
        tableView.mas_makeConstraints { (make) in
            make?.edges.equalTo()(self.view)
        }
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return dataSource.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        var cell = tableView.dequeueReusableCell(withIdentifier: "Cell")
        if cell == nil {
            cell = UITableViewCell(style: .subtitle, reuseIdentifier: "Cell")
        }
        cell!.textLabel?.text = dataSource[indexPath.row]
        cell!.detailTextLabel?.text = ""
        if dataSource[indexPath.row] == "Picasso HTTP 环境" {
            let httpStatus = PPGlobalConstant.kSPKPicassoHTTPEnvironment
            cell!.detailTextLabel?.text = httpStatus ? "线上" : "线下"
        }
        return cell!
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        switch indexPath.row {
        case 0:
            let sharkDebugVC = NVSharkDebugPanel()
            navigationController?.pushViewController(sharkDebugVC, animated: true)
        case 1:
            #if DEBUG || TEST
            showPortMDialog()
            #endif
            break
        case 2:
            PPRegisterAPPMock.showRigiestDialog()
            break
        case 3:
            let cell = tableView.cellForRow(at: indexPath)!
//            PPGlobalConstant.kSPKPicassoHTTPEnvironment = !PPGlobalConstant.kSPKPicassoHTTPEnvironment
//            PicassoClient.shared().betaUrl = PPGlobalConstant.kSPKPicassoHTTPEnvironment ? "" : "https://mapi.51ping.com/mapi/picasso/queryjs.bin"
//            cell.detailTextLabel?.text = PPGlobalConstant.kSPKPicassoHTTPEnvironment ? "线上" : "线下"
            break
        default:
            break
        }
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 50
    }
    
    
    func showPortMDialog() {
        let infoDict = UserDefaults.standard
        var groupToken = infoDict.string(forKey: "portm_grouptoken")
        var userToken = infoDict.string(forKey: "portm_usertoken")
        
        let tokenInput = UITextField(frame: CGRect(x: 0, y: 0, width: 230, height: 50))
        tokenInput.placeholder = "组 token"
        tokenInput.borderStyle = .bezel
        tokenInput.text = groupToken
        
        let userInput = UITextField(frame: CGRect(x: 0, y: 60, width: 230, height: 50))
        userInput.placeholder = "user token"
        userInput.borderStyle = .bezel
        userInput.text = userToken
        
        let container = UIView(frame: CGRect(x: 0, y: 0, width: 230, height: 110))
        container.addSubview(tokenInput)
        container.addSubview(userInput)
        
        let alertView = SAKAlertView()
        alertView.title = "模拟器注册PortM Mock\n 真机请扫码注册"
        alertView.message = "当前portM状态：\(VANStateManager.default().isWorking)\n将 Token 粘贴到下方"
        alertView.dismissWithButtonAction = false
        alertView.additionalView = container
        alertView.addButton(withTitle: "注册", type: .right) { (alert: SAKAlertView?) in
            guard let strongAlert = alert else {
                return
            }
            strongAlert.dismiss()
            groupToken = tokenInput.text
            userToken = userInput.text
            infoDict.set(groupToken, forKey: "portm_grouptoken")
            infoDict.set(userToken, forKey: "portm_usertoken")
            
            // @zhaozhiyu vane API 变更
            var dic = Dictionary<String, String>()
            dic["token"] = groupToken
            dic["user"] = userToken
            
            VANStateManager.default()
                .startWorking(withParameter:dic)
        }
        alertView.addButton(withTitle: "关闭 PortM Mock", type: .right) { (alert: SAKAlertView?) in
            guard let strongAlert = alert else {
                return
            }
            VANStateManager.default().stopWorking()
            SPKToastCenter.default().toast(withMessage: "关闭成功")
            strongAlert.dismiss()
        }
        alertView.addButton(withTitle: "退出", type: .right) { (alert: SAKAlertView?) in
            guard let strongAlert = alert else {
                return
            }
            strongAlert.dismiss()
        }
        alertView.show()
    }

}

