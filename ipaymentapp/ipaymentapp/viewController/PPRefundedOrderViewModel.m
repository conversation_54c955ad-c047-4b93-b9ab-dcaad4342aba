//
//  PPRefundedOrderViewModel.m
//  ipaymentapp
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 17/2/24.
//  Copyright © 2017年 Meituan.com. All rights reserved.
//

#import "PPRefundedOrderViewModel.h"
#import "SAKFetchedResultsController.h"
#import "PPService.h"
#import "SAKFetchedResultsSectionInfo.h"
#import "PPRefundedOrder.h"
#import "SAKFetchedResultsController+Modify.h"
#import "PPRefundedOrderUIObject.h"

@interface PPRefundedOrderViewModel ()

@property (nonatomic, strong) PPService *service;

@end

@implementation PPRefundedOrderViewModel

- (RACCommand *)loadCommand
{
    if (!_loadCommand) {
        @weakify(self)
        _loadCommand = [[RACCommand alloc] initWithSignalBlock:^RACSignal *(id input) {
            @strongify(self)
            return [self refreshListSignal];
        }];
    }
    return _loadCommand;
}

#pragma makr - override

- (RACSignal *)loadMoreListSignal
{
    if (!self.hasMoreList) {
        return [RACSignal empty];
    }
    self.offset = self.total;
    return [self fetchRefundedOrderListSignal]; 
}

- (RACSignal *)refreshListSignal
{
    self.offset = 0;
    self.hasMoreList = YES;
    [self.fetchedResultsController removeAllSections];
    return [self fetchRefundedOrderListSignal];
}

- (RACSignal *)fetchRefundedOrderListSignal
{
    @weakify(self);
    return [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        NSDictionary *param = @{@"offset":@(self.offset), @"limit":@(20), @"userId":self.queryUserid ? : @""};
        [[PPService defaultInstance] getRefundOrderListWithParam:param finished:^(NSArray<PPRefundedOrder *> *orderList, CIPError *error) {
            @strongify(self);
            if (error) {
                [subscriber sendError:error];
            } else {
                NSMutableArray *refundorderList = [NSMutableArray array];
                [orderList enumerateObjectsUsingBlock:^(PPRefundedOrder * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
                    PPRefundedOrderUIObject *uiObject = [[PPRefundedOrderUIObject alloc] initWithRefundedOrder:obj];
                    [refundorderList addObject:uiObject];
                }];
                self.total = refundorderList.count;
                if (self.total > 0) {
                    self.hasMoreList = YES;
                } else {
                    self.hasMoreList = NO;
                }
                SAKFetchedResultsSectionInfo *section = [[SAKFetchedResultsSectionInfo alloc] initWithArray:refundorderList];
                [self.fetchedResultsController addSection:section];
                [subscriber sendCompleted];
            }
        }];
        return nil;
    }];
}

- (void)setQueryUserid:(NSString *)queryUserid
{
    _queryUserid = queryUserid;
    
}
@end
