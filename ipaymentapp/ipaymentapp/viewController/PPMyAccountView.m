//
//  PPMyAccountView.m
//  SAKCashier
//
//  Created by wang<PERSON> on 15/6/16.
//  Copyright (c) 2015年 meituan. All rights reserved.
//

#import "PPMyAccountView.h"
#import "SAKUIKitMacros.h"
#import "Masonry.h"
#import "UIColor+Addition.h"

@interface PPMyAccountView ()

@property (nonatomic, strong) UIView *separatorLine;

@end

@implementation PPMyAccountView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        
        self.backgroundColor = [UIColor generalBackgroundColor];
        
        _accountIconView = ({
            UIImageView *imageView = [[UIImageView alloc] init];
            imageView.image = [UIImage imageNamed:@"icon_mine_default_portrait"];
            [self addSubview:imageView];
            imageView;
        });
        
        _userNameLabel = ({
            UILabel *label = [[UILabel alloc] init];
            label.textColor = [UIColor generalLabelTextColor];
            label.font = Font(16);
            [self addSubview:label];
            label;
        });
        
        _userIDLabel = ({
            UILabel *label = [[UILabel alloc] init];
            label.textColor = [UIColor generalLabelTextColor];
            label.font = Font(12);
            [self addSubview:label];
            label;
        });
        
        _separatorLine = ({
            UIView *view = [[UIView alloc] init];
            view.backgroundColor = [UIColor generalSeparatorLineColor];
            [self addSubview:view];
            view;
        });
        
        _logoutButton = ({
            UIButton *button = [[UIButton alloc] init];
            [button setTitle:@"退出" forState:UIControlStateNormal];
            [button setTitleColor:HEXCOLOR(0x06c1ae) forState:UIControlStateNormal];
            button.titleLabel.font = Font(15);
            [self addSubview: button];
            button;
        });
        
        _loginButton = ({
            UIButton *button = [[UIButton alloc] init];
            [button setTitle:@"立即登陆" forState:UIControlStateNormal];
            [button setTitleColor:HEXCOLOR(0x06c1ae) forState:UIControlStateNormal];
            button.titleLabel.font = Font(15);
            [self addSubview: button];
            button;
        });
    }
    return self;
}

+ (BOOL)requiresConstraintBasedLayout
{
    return YES;
}

- (void)updateConstraints
{
    [self.accountIconView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self).offset(30);
        make.centerY.equalTo(self);
        make.width.equalTo(@49);
        make.height.equalTo(@49);
    }];
    
    [self.userNameLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.accountIconView.mas_right).offset(12);
        make.centerY.equalTo(self);
        make.height.equalTo(@18);
    }];
    
    [self.userIDLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.userNameLabel);
        make.bottom.equalTo(self).with.offset(-12);
        make.height.equalTo(@18);
    }];
    
    
    [self.logoutButton mas_updateConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self).offset(-30);
        make.centerY.equalTo(self);
        make.height.equalTo(@18);
    }];
    
    
    [self.separatorLine mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self);
        make.bottom.equalTo(self);
        make.right.equalTo(self);
        make.height.equalTo(@0.5);
    }];
    
    [self.loginButton mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self);
        make.bottom.equalTo(self);
        make.left.equalTo(self);
        make.right.equalTo(self);
    }];
    
    [super updateConstraints];
}


@end
