//
//  PPRefundedOrderUIObject.m
//  ipaymentapp
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 17/2/24.
//  Copyright © 2017年 Meituan.com. All rights reserved.
//

#import "PPRefundedOrderUIObject.h"
#import "PPRefundedOrder.h"
#import "NSString+SPKRMBSymbol.h"

@implementation PPRefundedOrderUIObject

- (instancetype)initWithRefundedOrder:(PPRefundedOrder *)refundedOrder
{
    self = [super init];
    if (self) {
        _merchantNO = refundedOrder.merchantNO;
        _partnerId = refundedOrder.partnerID;
        _refundNo = refundedOrder.refundNo;
        _payOrderId = refundedOrder.payOrderID;
        _refundNo = refundedOrder.refundNo;
        _payRefundFlow = refundedOrder.payRefundFlow;
        _refundStatus = refundedOrder.status;
        _tradeNo = refundedOrder.tradeNo;
        _moneyCent = [NSString spk_RMBSymbolStringWithDouble:(CGFloat)refundedOrder.moneyCent / 100.0f];
        
        if (refundedOrder.source == 0) {
            _source = @"支付平台自动退款";
        } else if (refundedOrder.source == 1) {
            _source = @"业务线申请";
        } else {
            _source = @"未知";
        }

        NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
        dateFormatter.dateFormat = @"yyyy-MM-dd HH:mm:ss";
        _acceptTime = [dateFormatter stringFromDate:[NSDate dateWithTimeIntervalSince1970:refundedOrder.addTime]];
    }
    
    return self;
}

@end
