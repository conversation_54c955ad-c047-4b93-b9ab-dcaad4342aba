//
//  PPOrderDetailViewController.h
//  SAKCashier
//
//  Created by ho<PERSON><PERSON>g<PERSON> on 4/1/15.
//  Copyright (c) 2015 meituan. All rights reserved.
//

#import "MTBaseViewController.h"

typedef NS_ENUM (NSUInteger, PPOrderDetailInfo) {
    PPOrderDetailTitle = 0,
    PPOrderDetailID,
    PPOrderDetailPayType,
    PPOrderDetailMoney,
    PPOrderDetailOrderTime,
    PPOrderDetailPayTime,
    PPOrderDetailStatus
};

@class PPOrder;
@interface PPOrderDetailViewController : MTBaseViewController <UITableViewDataSource, UITableViewDelegate>

@property (nonatomic, strong) PPOrder *order;
@property (nonatomic, strong, readonly) NSMutableDictionary *extraParams;

@end
