//
//  PPRefundedMoneyInputModalView.m
//  ipaymentapp
//
//  Created by xiaoyangyuxuan on 2020/11/4.
//  Copyright © 2020 Meituan.com. All rights reserved.
//

#import "PPRefundedMoneyInputModalView.h"
#import "SAKUIKitMacros.h"
#import <CIPStringAdditions.h>
#import <Masonry.h>
#import <ReactiveCocoa/ReactiveCocoa.h>

@interface PPRefundedMoneyInputModalView ()

@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UITextField *moneyInputTextField;

@end

@implementation PPRefundedMoneyInputModalView

- (instancetype)initWithRefundMoney:(NSString *)refundMoney
{
    self = [super init];
    if (self) {
        self.titleLabel.text = @"请输入退款金额";
        self.moneyInputTextField.placeholder = refundMoney;
        [self setup];
    }
    return self;
}

- (void)awakeFromNib
{
    [super awakeFromNib];
    [self setup];
}

- (void)setup
{
    [self addSubview:self.titleLabel];
    [self addSubview:self.moneyInputTextField];
}

- (void)updateConstraints
{
    [self.titleLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self).offset(30);
        make.left.equalTo(self).offset(20);
        make.right.equalTo(self).offset(-20);
    }];
    
    [self.moneyInputTextField mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self).offset(20);
        make.right.equalTo(self).offset(-20);
        make.top.equalTo(self.titleLabel.mas_bottom).offset(10);
        make.bottom.equalTo(self).offset(-30);
    }];
    
    [super updateConstraints];
}

- (CGFloat)customViewHeighit
{
    return 130.0f;
}

#pragma mark - setter && getter

- (UILabel *)titleLabel
{
    if (!_titleLabel) {
        _titleLabel = [UILabel new];
        _titleLabel.textColor = HEXCOLOR(0x222222);
        _titleLabel.font = BoldFont(16);
        [_titleLabel setTextAlignment:NSTextAlignmentCenter];
        [_titleLabel setLineBreakMode:NSLineBreakByTruncatingTail];
        [_titleLabel setNumberOfLines:1];
    }
    return _titleLabel;
}

- (UITextField *)moneyInputTextField
{
    if (!_moneyInputTextField) {
        _moneyInputTextField = [UITextField new];
        _moneyInputTextField.textAlignment = NSTextAlignmentCenter;
        _moneyInputTextField.borderStyle = UITextBorderStyleRoundedRect;
    }
    return _moneyInputTextField;
}

@end
