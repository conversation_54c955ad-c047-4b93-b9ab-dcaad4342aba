//
//  PPOrderTableViewCell.m
//  SAKCashier
//
//  Created by sunhl on 15/3/31.
//  Copyright (c) 2015年 meituan. All rights reserved.
//

#import "PPOrderTableViewCell.h"
#import "View+MASAdditions.h"
#import "UIImageView+WebCache.h"
#import "SAKUIKitMacros.h"

#import "PPOrder.h"
#import "PPOrderStatus.h"
#import "NSDate+Time.h"
#import "UIColor+Addition.h"


@interface PPOrderTableViewCell ()

@property (nonatomic, strong) UIView *separatorLine;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *orderIDLabel;
@property (nonatomic, strong) UILabel *statusLabel;
@property (nonatomic, strong) UILabel *orderTimeLabel;
@property (nonatomic, strong) UILabel *payTimeLabel;

@end


@implementation PPOrderTableViewCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        [self commonInit];
    }
    return self;
}

- (void)commonInit
{
    _separatorLine = ({
        UIView *view = [[UIView alloc] init];
        view.backgroundColor = [UIColor generalSeparatorLineColor];
        view.frame = CGRectMake(1, CGRectGetHeight(self.bounds) - 0.5,
                                CGRectGetWidth(self.bounds) - 2, 0.5);
        view;
    });
    [self.contentView addSubview:_separatorLine];
    
    self.titleLabel = ({
        UILabel *label = [[UILabel alloc] init];
        label.font = Font(14);
        label;
    });
    
    self.orderIDLabel = ({
        UILabel *label = [[UILabel alloc] init];
        label.font = Font(12);
        label.textColor = [UIColor grayColor];
        label.textAlignment = NSTextAlignmentRight;
        label;
    });
    self.statusLabel = ({
        UILabel *label = [[UILabel alloc] init];
        label.font = Font(12);
        label;
    });
                         
    self.orderTimeLabel = ({
        UILabel *label = [[UILabel alloc] init];
        label.font = Font(12);
        label.textColor = [UIColor grayColor];
        label.textAlignment = NSTextAlignmentRight;
        label;
    });

    self.payTimeLabel = ({
        UILabel *label = [[UILabel alloc] init];
        label.font = Font(12);
        label.textAlignment = NSTextAlignmentRight;
        label.textColor = [UIColor grayColor];
        label;
    });
    
    [self.contentView addSubview:self.titleLabel];
    [self.contentView addSubview:self.orderIDLabel];
    [self.contentView addSubview:self.statusLabel];
    [self.contentView addSubview:self.orderTimeLabel];
    [self.contentView addSubview:self.payTimeLabel];
}

- (void)setOrder:(PPOrder *)order
{
    _order = order;
    
    [self updateUI];
}

- (void)updateUI
{
    self.titleLabel.text = self.order.title;
    self.orderIDLabel.text = self.order.orderID;
    self.orderTimeLabel.text = [self.order.orderTime timeString];
    self.payTimeLabel.text = [self.order.payTime timeString];
    
    switch (self.order.status.integerValue) {
        case 0:
            self.statusLabel.textColor = [UIColor generalLabelTextColor];
            self.statusLabel.text = @"未付款";
            break;
        case 16:
            self.statusLabel.textColor = [UIColor blueLabelTextColor];
            self.statusLabel.text = @"支付成功";
            break;
        case 64:
            self.statusLabel.textColor = [UIColor blueLabelTextColor];
            self.statusLabel.text = @"支付成功并通知业务方成功";
            break;
        case 96:
            self.statusLabel.textColor = [UIColor systemOrangeColor];
            self.statusLabel.text = @"已退款";
            break;
        case 97:
            self.statusLabel.textColor = [UIColor systemOrangeColor];
            self.statusLabel.text = @"已全部退款";
            break;
            
        default:
            self.statusLabel.text = @"未知状态";
            self.statusLabel.textColor = [UIColor grayLabelTextColor];
            break;
    }
    
    [self setNeedsUpdateConstraints];
}


- (void)updateConstraints
{
    [self.contentView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
        make.size.equalTo(self);
        make.height.greaterThanOrEqualTo(@44);
    }];
    
    [self.separatorLine mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.contentView);
        make.height.equalTo(@(1));
        make.bottom.equalTo(self.contentView.mas_bottom).offset(-1);
    }];
    
    [self.titleLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView.mas_top).offset(10);
        make.left.equalTo(self.contentView).offset(10);
        make.width.greaterThanOrEqualTo(@(100));
    }];
    
    [self.orderIDLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.titleLabel);
        make.left.greaterThanOrEqualTo(self.titleLabel.mas_right).offset(10);
        make.right.equalTo(self.contentView.mas_right).offset(-12);
        make.width.equalTo(@(70));
    }];
    
    [self.statusLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.titleLabel.mas_bottom).offset(5);
        make.left.equalTo(self.titleLabel);
        make.width.greaterThanOrEqualTo(@(60));
        make.bottom.equalTo(self.contentView.mas_bottom).offset(-10);
    }];
    
    [self.orderTimeLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.statusLabel);
        make.left.greaterThanOrEqualTo(self.statusLabel.mas_right).offset(10);
        make.width.greaterThanOrEqualTo(@(80));
        make.bottom.equalTo(self.statusLabel.mas_bottom);
        if ([self.payTimeLabel.text length]) {
            make.right.equalTo(self.payTimeLabel.mas_left).offset(-10);
        } else {
            make.right.equalTo(self.contentView).offset(-10);
        }
    }];
    
    [self.payTimeLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.orderTimeLabel);
        make.right.equalTo(self.contentView.mas_right).offset(-10);
        make.bottom.equalTo(self.statusLabel.mas_bottom);
    }];
    
    [super updateConstraints];
}

- (CGSize)intrinsicContentSize
{
    CGFloat screenWidth = [[UIScreen mainScreen] bounds].size.width;
    
    CGFloat height = 20;
    
    height += [self.titleLabel intrinsicContentSize].height;
    height += 5 + [self.orderIDLabel intrinsicContentSize].height;
    
    return CGSizeMake(screenWidth, height);
}

@end
