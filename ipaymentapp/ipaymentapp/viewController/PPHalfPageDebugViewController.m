//
//  PPHalfPageDebugViewController.m
//  ipaymentapp
//
//  Created by zhangyang on 2021/8/13.
//  Copyright © 2021 Meituan.com. All rights reserved.
//


#import "PPHalfPageDebugViewController.h"

#import "View+MASAdditions.h"
#import "UIColor+Addition.h"

#import "MTFUSubmitButton.h"
#import "TKAlertCenter.h"
#import "PPQRCodeScanViewController.h"
#import <SAKPaymentKit/SPKToastCenter.h>
#import <SAKPortal/SAKPortal.h>
#import <CIPFoundation/CIPStringAdditions.h>

@interface PPHalfPageDebugViewController ()

@property (nonatomic, strong) UITextField *urlTextField;
@property (nonatomic, strong) UITextField *sceneTextField;
@property (nonatomic, strong) UITextField *noticeNameTextField;
@property (nonatomic, strong) UITextField *initialDataTextField;
@property (nonatomic, strong) UITextField *backgroundColorTextField;
@property (nonatomic, strong) UITextField *nsfURLTextField;
@property (nonatomic, strong) UITextField *nsfParamTextField;
@property (nonatomic, strong) UITextField *timeoutTextField;
@property (nonatomic, strong) MTFUSubmitButton *nativeDebugBtn;

@property (nonatomic, strong) UITextField *h5DebugTextField;
@property (nonatomic, strong) MTFUSubmitButton *h5DebugBtn;
@property (nonatomic, strong) MTFUSubmitButton *scanBtn;

@end

@implementation PPHalfPageDebugViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self setupUI];
 
}

- (void)setupUI
{
    [self.view setBackgroundColor:[UIColor generalBackgroundColor]];
    
    self.urlTextField = ({
        UITextField *textField = [UITextField new];
        textField.placeholder = @"url";
        textField.borderStyle = UITextBorderStyleLine;
        textField.text = @"http://pay09-sl-cashier.qa.pay.test.sankuai.com/creditpay/creditpayCommonApply?utm_source=creditpay_app-cp-banner6_16112&openSource=newYuefuForCashier";
        textField;
    });
    [self.view addSubview:self.urlTextField];
    
    self.sceneTextField = ({
        UITextField *textField = [UITextField new];
        textField.placeholder = @"target_scene";
        textField.borderStyle = UITextBorderStyleLine;
        textField.text = @"credit_half_page";
        textField;
    });
    [self.view addSubview:self.sceneTextField];
    
    self.noticeNameTextField = ({
        UITextField *textField = [UITextField new];
        textField.placeholder = @"notice_name";
        textField.borderStyle = UITextBorderStyleLine;
        textField.text = @"halfpage_callback_yuefu";
        textField;
    });
    [self.view addSubview:self.noticeNameTextField];
    
    self.initialDataTextField = ({
        UITextField *textField = [UITextField new];
        textField.placeholder = @"initial_data";
        textField.borderStyle = UITextBorderStyleLine;
        textField;
    });
    [self.view addSubview:self.initialDataTextField];
    
    self.nsfURLTextField = ({
        UITextField *textField = [UITextField new];
        textField.placeholder = @"request_url";
        textField.borderStyle = UITextBorderStyleLine;
        textField;
    });
    [self.view addSubview:self.nsfURLTextField];
    
    self.nsfParamTextField = ({
        UITextField *textField = [UITextField new];
        textField.placeholder = @"request_data";
        textField.borderStyle = UITextBorderStyleLine;
        textField;
    });
    [self.view addSubview:self.nsfParamTextField];
    
    self.timeoutTextField = ({
        UITextField *textField = [UITextField new];
        textField.placeholder = @"loading_timeout 例如：6，单位s";
        textField.borderStyle = UITextBorderStyleLine;
        textField;
    });
    [self.view addSubview:self.timeoutTextField];
    
    self.backgroundColorTextField = ({
        UITextField *textField = [UITextField new];
        textField.placeholder = @"background_color 例如：#00FFFFFF";
        textField.borderStyle = UITextBorderStyleLine;
        textField.text = @"";
        textField;
    });
    [self.view addSubview:self.backgroundColorTextField];
    
    self.h5DebugTextField = ({
        UITextField *textField = [UITextField new];
        textField.placeholder = @"h5 测试页";
        textField.text = @"https://pay09-sl-nest-maidan.fin.test.sankuai.com/testApplyStorage";
        textField.borderStyle = UITextBorderStyleLine;
        textField;
    });
    [self.view addSubview:self.h5DebugTextField];
    
    self.nativeDebugBtn = [MTFUSubmitButton button];
    [self.nativeDebugBtn setTitle:@"Native 调用 Scheme" forState:UIControlStateNormal];
    [self.nativeDebugBtn addTarget:self action:@selector(nativeDebugBtnClicked:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.nativeDebugBtn];
    

    self.h5DebugBtn = [MTFUSubmitButton button];
    [self.h5DebugBtn setTitle:@"H5 能力调试页" forState:UIControlStateNormal];
    [self.h5DebugBtn addTarget:self action:@selector(h5DebugBtnClicked:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.h5DebugBtn];
    
    self.scanBtn = [MTFUSubmitButton button];
    [self.scanBtn setTitle:@"H5 扫一扫" forState:UIControlStateNormal];
    [self.scanBtn addTarget:self action:@selector(h5ScanClicked:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.scanBtn];
    
    
    [self makeViewConstraints];
}

- (void)makeViewConstraints
{
    [self.urlTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(100);
        make.left.equalTo(self.view).offset(30);
        make.right.equalTo(self.view).offset(-30);
        make.height.mas_equalTo(30);
    }];
    
    [self.sceneTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.urlTextField.mas_bottom).offset(10);
        make.left.equalTo(self.view).offset(30);
        make.right.equalTo(self.view).offset(-30);
        make.height.mas_equalTo(30);
    }];
    
    [self.noticeNameTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.sceneTextField.mas_bottom).offset(10);
        make.left.equalTo(self.view).offset(30);
        make.right.equalTo(self.view).offset(-30);
        make.height.mas_equalTo(30);
    }];
    
    [self.initialDataTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.noticeNameTextField.mas_bottom).offset(10);
        make.left.equalTo(self.view).offset(30);
        make.right.equalTo(self.view).offset(-30);
        make.height.mas_equalTo(30);
    }];
    
    [self.nsfURLTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.initialDataTextField.mas_bottom).offset(10);
        make.left.equalTo(self.view).offset(30);
        make.right.equalTo(self.view).offset(-30);
        make.height.mas_equalTo(30);
    }];
    
    [self.nsfParamTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.nsfURLTextField.mas_bottom).offset(10);
        make.left.equalTo(self.view).offset(30);
        make.right.equalTo(self.view).offset(-30);
        make.height.mas_equalTo(30);
    }];
    
    [self.timeoutTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.nsfParamTextField.mas_bottom).offset(10);
        make.left.equalTo(self.view).offset(30);
        make.right.equalTo(self.view).offset(-30);
        make.height.mas_equalTo(30);
    }];

    [self.backgroundColorTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.timeoutTextField.mas_bottom).offset(10);
        make.left.equalTo(self.view).offset(30);
        make.right.equalTo(self.view).offset(-30);
        make.height.mas_equalTo(30);
    }];
    
    [self.nativeDebugBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.backgroundColorTextField.mas_bottom).offset(30);
        make.left.equalTo(self.view).offset(30);
        make.right.equalTo(self.view).offset(-30);
        make.height.mas_equalTo(30);
    }];
    
    [self.h5DebugTextField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.nativeDebugBtn.mas_bottom).offset(40);
        make.left.equalTo(self.view).offset(30);
        make.right.equalTo(self.view).offset(-30);
        make.height.mas_equalTo(30);
    }];
    
    [self.h5DebugBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.h5DebugTextField.mas_bottom).offset(10);
        make.left.equalTo(self.view).offset(30);
        make.right.equalTo(self.view).offset(-30);
        make.height.mas_equalTo(30);
    }];
    
    [self.scanBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.h5DebugBtn.mas_bottom).offset(10);
        make.left.equalTo(self.view).offset(30);
        make.right.equalTo(self.view).offset(-30);
        make.height.mas_equalTo(30);
    }];
    
    
    [super updateViewConstraints];
}

- (void)nativeDebugBtnClicked:(id)sender
{
    NSString *url = [self.urlTextField.text cipf_URLEncodedString];
    NSString *scene = [self.sceneTextField.text cipf_URLEncodedString];
    NSString *noticeName = [self.noticeNameTextField.text cipf_URLEncodedString];
    NSString *backColor = [self.backgroundColorTextField.text cipf_URLEncodedString];

    NSString *scheme = [NSString stringWithFormat:@"meituanpayment://halfpage/launch?url=%@&target_scene=%@&notice_name=%@&background_color=%@",url,scene,noticeName,backColor];
    [SAKPortal transferFromViewController:self toURL:[NSURL URLWithString:scheme] completion:nil];
    
    if (noticeName.length) {
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(noticeAction:)
                                                     name:noticeName
                                                   object:nil];
    }
}

- (void)h5DebugBtnClicked:(id)sender
{
    NSURL *url = [NSURL URLWithString:self.h5DebugTextField.text];
    if (url) {
        [SAKPortal transferFromViewController:self toURL:url completion:nil];
    } else {
        [[SPKToastCenter defaultCenter] toastWithMessage:@"未知 URL ！！"];
    }
}

- (void)h5ScanClicked:(id)sender
{
    PPQRCodeScanViewController *vc = [PPQRCodeScanViewController new];
    [self.navigationController pushViewController:vc animated:YES];
    
    [vc setCodeScanSucceed:^(NSURL *resultURL) {
        if (!resultURL) {
            [[SPKToastCenter defaultCenter] toastWithMessage:@"未知结果 ！！"];
            return;
        }
        [self.navigationController popViewControllerAnimated:NO];
        
        [SAKPortal transferFromViewController:self toURL:resultURL completion:nil];
    }];
    
    [vc setCodeScanFailed:^{
        [self.navigationController popViewControllerAnimated:NO];
    }];
    
    [vc setCodeScanCancel:^{
        [self.navigationController popViewControllerAnimated:NO];
    }];
}

- (void)noticeAction:(NSNotification *)sender
{
    NSString *result = [sender.userInfo objectForKey:@"halfpage_status"];
    [[SPKToastCenter defaultCenter] toastWithMessage:[NSString stringWithFormat:@"Native %@",result]];
    
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event
{
    [self.view endEditing:YES];
}

@end
