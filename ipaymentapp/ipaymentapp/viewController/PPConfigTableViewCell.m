//
//  PPConfigTableViewCell.m
//  ipaymentapp
//
//  Created by wang<PERSON> on 15/6/24.
//  Copyright (c) 2015年 Meituan.com. All rights reserved.
//

#import "PPConfigTableViewCell.h"
#import "SAKUIKitMacros.h"
#import "Masonry.h"
#import "UIColor+Addition.h"

@interface PPConfigTableViewCell ()



@end

@implementation PPConfigTableViewCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        _titleLabel = ({
            UILabel *label = [[UILabel alloc] init];
            label.textColor = [UIColor generalLabelTextColor];
            label.font = Font(15);
            label;
        });
        [self.contentView addSubview:_titleLabel];
        
        _switchControl = ({
            UISwitch *switchControl = [UISwitch new];
            [switchControl addTarget:self action:@selector(tapSwitch:) forControlEvents:UIControlEventValueChanged];
            switchControl;
        });
        [self.contentView addSubview:_switchControl];
    }
    return self;
}

- (void)tapSwitch:(id)sender
{
    if (self.switchTapBlock) {
        UISwitch *s = (UISwitch *)sender;
        self.switchTapBlock(s.on);
    }
}

+ (BOOL)requiresConstraintBasedLayout
{
    return YES;
}

- (void)updateConstraints
{
    [self.titleLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self).offset(16);
        make.centerY.equalTo(self);
        make.height.equalTo(@16);
    }];
    
    [self.switchControl mas_updateConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self).offset(-16);
        make.centerY.equalTo(self);
    }];
    
    [super updateConstraints];
}


- (void)awakeFromNib {
    // Initialization code
    [super awakeFromNib];
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];
    
    // Configure the view for the selected state
}

- (void)setSwitchShow:(BOOL)switchShow
{
    self.switchControl.hidden = !switchShow;
}
@end
