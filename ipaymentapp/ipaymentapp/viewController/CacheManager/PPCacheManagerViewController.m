//
//  PPCacheManagerViewController.m
//  ipaymentapp
//
//  Created by qinchengbo on 2019/2/27.
//  Copyright © 2019 Meituan.com. All rights reserved.
//

#import "PPCacheManagerViewController.h"
#import "SPGObjectCacheManager.h"
#import "UIColor+Addition.h"

@interface PPCacheManagerViewController ()

@property (nonatomic, strong) UILabel *cacheSizeLabel;
@property (nonatomic, strong) UIButton *cleanCache;

@end

@implementation PPCacheManagerViewController

- (void)viewDidLoad
{
    [super viewDidLoad];
    self.view.backgroundColor = UIColor.generalBackgroundColor;
    self.title = @"缓存管理";
    [self setupUI];
    // Do any additional setup after loading the view.
}

- (void)setupUI
{
    UILabel *label = [[UILabel alloc] init];
    label.text = @"缓存大小:";
    label.textColor = UIColor.blueLabelTextColor;
    self.cacheSizeLabel = [[UILabel alloc] init];
    self.cacheSizeLabel.text = [NSString stringWithFormat:@"%.2f Kb", [[SPGObjectCacheManager sharedCacheManager] cacheSize] / (1024.0)];
    self.cleanCache = [UIButton buttonWithType:UIButtonTypeSystem];
    [self.cleanCache setTitle:@"清理缓存" forState:UIControlStateNormal];
    @weakify(self);
    self.cleanCache.rac_command = [[RACCommand alloc] initWithSignalBlock:^RACSignal *(id input) {
        @strongify(self);
        [self clean];
        return [RACSignal empty];
    }];
    [self.view addSubview:label];
    [self.view addSubview:self.cacheSizeLabel];
    [self.view addSubview:self.cleanCache];
    
    [label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view.mas_top).with.offset(100);
        make.left.equalTo(self.view.mas_left).with.offset(50);
    }];
    [self.cacheSizeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(label);
        make.left.equalTo(label.mas_right).with.offset(20);
    }];
    [self.cleanCache mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(label.mas_bottom).with.offset(20);
        make.centerX.equalTo(self.view);
    }];
    
    [self.view updateConstraintsIfNeeded];
}

- (void)clean
{
    [[SPGObjectCacheManager sharedCacheManager] cleanCache];
    self.cacheSizeLabel.text = [NSString stringWithFormat:@"%.2f Kb", [[SPGObjectCacheManager sharedCacheManager] cacheSize] / (1024.0)];
}

@end
