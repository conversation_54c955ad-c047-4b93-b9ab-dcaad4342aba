//
//  PPCallbackURLSettingView.swift
//  ipaymentapp
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2018/3/2.
//  Copyright © 2018年 Meituan.com. All rights reserved.
//

import UIKit

class PPPaymentSettingView: UIView {

    let titleLabel : UILabel
    let titleField : UITextView
    let switchView : UISwitch
    let lineView : UIView
    var titleLabelHeight: Float = 0.0
    var titleFieldHeight: Float = 0.0
    
    override init(frame: CGRect) {
        titleLabel = {
            let label = UILabel()
            label.font = UIFont.systemFont(ofSize: 14)
            label.textColor = .grayLabelText
            label.text = "支付设置 key"
            return label
        }()
        
        titleField = {
            let textField = UITextView()
            textField.textColor = UIColor(white: 0.2, alpha: 1)
            textField.font = UIFont.systemFont(ofSize: 14)
            textField.textContainer.lineFragmentPadding = 0
            textField.textContainerInset = .zero
            textField.text = "支付设置 value"
            textField.autoresizingMask = .flexibleWidth
            textField.autocapitalizationType = .none
            textField.returnKeyType = .next
            textField.autocorrectionType = .no
            return textField
        }()
        
        lineView = {
            let lineView = UIView()
            lineView.backgroundColor = .generalSeparatorLine
            return lineView
        }()
        
        switchView = UISwitch()
        
        super.init(frame:frame)

        self.addSubview(titleLabel)
        self.addSubview(titleField)
        self.addSubview(switchView)
        self.addSubview(lineView)
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func updateConstraints() {
        self.titleLabel.mas_updateConstraints { make in
            make?.centerY.equalTo()(self)
            make?.left.equalTo()(self)?.offset()(12)
            make?.width.equalTo()(80)
            if (self.titleLabelHeight > 0) {
                make?.height.equalTo()(self.titleLabelHeight)
            } else {
                make?.height.equalTo()(20)
            }
        }
        
        self.titleField.mas_updateConstraints({ make in
            make?.centerY.equalTo()(self)
            make?.left.equalTo()(self.titleLabel.mas_right)?.offset()(17)
            make?.width.equalTo()(UIScreen.main.bounds.width - 180)
            if (self.titleFieldHeight > 0) {
                make?.height.equalTo()(self.titleLabelHeight)
            } else {
                make?.height.equalTo()(20)
            }
        })
        
        self.switchView.mas_updateConstraints { make in
            make?.centerY.equalTo()(self)
            make?.left.equalTo()(self.titleField.mas_right)?.offset()(12)
        }
        
        self.lineView.mas_updateConstraints { make in
            make?.left.right().bottom().equalTo()(self)
            make?.height.mas_equalTo()(1)
        }
        
        super.updateConstraints()
    }
}
