//
//  MerchantWalletEntranceViewController.m
//  ipaymentapp
//
//  Created by Halo on 2019/10/17.
//  Copyright © 2019 Meituan.com. All rights reserved.
//

#import "MerchantWalletEntranceViewController.h"
//#import "SMEMRNEntranceView.h"
//#import "SMEEntranceConfig.h"
#import "UIButton+Custom.h"

@interface MerchantWalletEntranceViewController ()
//@property (nonatomic, strong) SMEMRNEntranceView *entranceView;
@property (nonatomic, strong) UIButton *refreshBtn;
@end

@implementation MerchantWalletEntranceViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor grayColor];
    
    self.refreshBtn = ({
        UIButton *button = [UIButton orangeCustomButton];
        [button setTitle:@"刷新" forState:UIControlStateNormal];
        [button addTarget:self action:@selector(didClickedRefreshBtn) forControlEvents:UIControlEventTouchUpInside];
        button;
    });
    [self.view addSubview:self.refreshBtn];
    
//    self.entranceView = [[SMEMRNEntranceView alloc] initWithConfiguration:^(SMEEntranceConfig * _Nonnull config) {
//        config.token = @"iXAsRNXErkEqhyg4a7-uR9tYqlcAAAAAjFEAAMEdmpGVfHzViEbO1blXKhyWqMC_QVQaScKKY3XzjKJUB7K4FI4MrkqjzyrxoVET9g";
//        config.source = @"21";
//        config.appName = @"waimai";
//        config.env = @"test";
//    }];
//    self.entranceView.clipsToBounds = YES;
//    [self.view addSubview:self.entranceView];
    [self LayoutSubViewsConstrains];
}

- (void)LayoutSubViewsConstrains
{
//    [self.entranceView mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.size.mas_equalTo(CGSizeMake([UIScreen mainScreen].bounds.size.width - 10, 148));
//        make.left.equalTo(self.view).offset(5);
//        make.top.equalTo(self.view).offset(200);
//    }];
//
//    [self.refreshBtn mas_makeConstraints:^(MASConstraintMaker *make) {
//        make.size.mas_equalTo(CGSizeMake(50, 38));
//        make.top.equalTo(self.entranceView.mas_bottom).offset(10);
//        make.left.equalTo(self.entranceView);
//    }];
//
}

- (void)didClickedRefreshBtn
{
//    [self.entranceView refreshEntranceView];
}

@end
