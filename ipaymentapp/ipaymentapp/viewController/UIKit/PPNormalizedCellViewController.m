//
//  NormalizedCellViewController.m
//  SAKPaymentKit
//
//  Created by <PERSON> on 2018/1/4.
//  Copyright © 2018年 meituan. All rights reserved.
//

#import "PPNormalizedCellViewController.h"
#import "SPKTitleSwitchCell.h"
#import "SPKTitleTipCell.h"
#import "SPKTitleImageCell.h"
#import "SPKTitleInputCell.h"
#import "SPKTitleTableViewCell.h"
#import "SPKTitleTipTableViewCell.h"
#import "SPKTitleImageTableViewCell.h"
#import "SPKTitleInputTableViewCell.h"
#import "SPKTitleSwitchTableViewCell.h"
#import "UIImage+SAKColor.h"
#import "UIImage+SAKUI.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"

@interface PPNormalizedCellSingleViewController ()

@property (nonatomic, strong) SPKCommonCell *cell1;
@property (nonatomic, strong) SPKTitleTipCell *cell2;
@property (nonatomic, strong) SPKTitleImageCell *cell3;
@property (nonatomic, strong) SPKTitleSwitchCell *cell4;
@property (nonatomic, strong) SPKTitleInputCell *cell5;

@property (nonatomic, strong) UISegmentedControl *cell1Manager;
@property (nonatomic, strong) UISegmentedControl *cell2Manager;
@property (nonatomic, strong) UISegmentedControl *cell3Manager;
@property (nonatomic, strong) UISegmentedControl *cell4Manager;
@property (nonatomic, strong) UISegmentedControl *cell5Manager;

@end

@implementation PPNormalizedCellSingleViewController

- (void)viewDidLoad {
    
    [self initUI];
    // Do any additional setup after loading the view.
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

- (void)initUI
{
    self.cell1 = [[SPKCommonCell alloc] init];
    self.cell1.titleLabel.text = @"标题";

    self.cell1Manager = [[UISegmentedControl alloc] initWithItems:@[@"样式1",@"样式2"]];
    [self.cell1Manager addTarget:self
                         action:@selector(tapCell1Manager)
               forControlEvents:UIControlEventValueChanged];
    
    [self.view addSubview:self.cell1];
    [self.view addSubview:self.cell1Manager];
    
    self.cell2 = [[SPKTitleTipCell alloc] init];
    self.cell2.titleLabel.text = @"标题";
    self.cell2.tipLabel.text = @"描述很长很长";
    [self.view addSubview:self.cell2];
    
    self.cell2Manager = [[UISegmentedControl alloc] initWithItems:@[@"样式1",@"样式2",@"样式3",@"样式4"]];
    [self.cell2Manager addTarget:self
                          action:@selector(tapCell2Manager)
                forControlEvents:UIControlEventValueChanged];
    [self.view addSubview:self.cell2Manager];
    
    self.cell3 = [[SPKTitleImageCell alloc] init];
    self.cell3.titleLabel.text = @"标题";
    self.cell3.tipLabel.text = @"描述";
    self.cell3.leftImageView.image = [[UIImage imageWithColor:[UIColor blueColor]] sakui_resizableImage];
    [self.view addSubview:self.cell3];
    
    self.cell3Manager = [[UISegmentedControl alloc] initWithItems:@[@"样式1",@"样式2"]];
    [self.cell3Manager addTarget:self
                          action:@selector(tapCell3Manager)
                forControlEvents:UIControlEventValueChanged];
    [self.view addSubview:self.cell3Manager];
    
    self.cell4 = [[SPKTitleSwitchCell alloc] init];
    self.cell4.titleLabel.text = @"标题";
    @weakify(self);
    [self.cell4 setSwitchButtonClickedBlock:^{
        @strongify(self);
        self.cell4.switchButton.on = !self.cell4.switchButton.on;
    }];
    [self.view addSubview:self.cell4];
    
    self.cell5 = [[SPKTitleInputCell alloc] init];
    self.cell5.titleLabel.text = @"标题";
    self.cell5.textField.placeholder = @"输入前";
    [self.view addSubview:self.cell5];
    
    
}

- (void)tapCell1Manager
{
    switch (self.cell1Manager.selectedSegmentIndex) {
        case 0:
            self.cell1.customAccessoryVisible = NO;
            break;
        case 1:
            self.cell1.customAccessoryVisible = YES;
            break;
            
        default:
            break;
    }
    [self.cell1 setNeedsUpdateConstraints];
    [self.cell1 updateConstraintsIfNeeded];
}

- (void)tapCell2Manager
{
    switch (self.cell2Manager.selectedSegmentIndex) {
        case 0:
            self.cell2.customAccessoryVisible = NO;
            self.cell2.redCircleVisible = NO;
            break;
        case 1:
            self.cell2.customAccessoryVisible = YES;
            self.cell2.tipLabel.text = @"描述";
            self.cell2.redCircleVisible = NO;
            break;
        case 2:
            self.cell2.customAccessoryVisible = YES;
            self.cell2.tipLabel.text = @"描述";
            self.cell2.redCircleVisible = YES;
            break;
        case 3:
            self.cell2.customAccessoryVisible = YES;
            self.cell2.tipLabel.text = @"";
            self.cell2.redCircleVisible = YES;
            break;
            
        default:
            break;
    }
    [self.cell2 setNeedsUpdateConstraints];
    [self.cell2 updateConstraintsIfNeeded];
}

- (void)tapCell3Manager
{
    switch (self.cell3Manager.selectedSegmentIndex) {
        case 0:
            self.cell3.customAccessoryVisible = NO;
            break;
        case 1:
            self.cell3.customAccessoryVisible = YES;
            break;
            
        default:
            break;
    }
    [self.cell3 setNeedsUpdateConstraints];
    [self.cell3 updateConstraintsIfNeeded];
}

- (void)updateViewConstraints
{
    [self.cell1 mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).with.offset(15);
        make.height.mas_equalTo(50);
        make.right.equalTo(self.view.mas_right).with.offset(-15);
        make.top.equalTo(self.view).with.offset(100);
    }];
    [self.cell1Manager mas_updateConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.cell1);
        make.top.equalTo(self.cell1.mas_bottom).with.offset(20);
        make.height.mas_offset(40);
    }];
    [self.cell2 mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.cell1);
        make.height.mas_equalTo(50);
        make.right.equalTo(self.cell1);
        make.top.equalTo(self.cell1Manager.mas_bottom).with.offset(20);
    }];
    [self.cell2Manager mas_updateConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.cell1);
        make.top.equalTo(self.cell2.mas_bottom).with.offset(20);
        make.height.mas_offset(40);
    }];
    [self.cell3 mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.cell1);
        make.height.mas_equalTo(50);
        make.right.equalTo(self.cell1);
        make.top.equalTo(self.cell2Manager.mas_bottom).with.offset(20);
    }];
    [self.cell3Manager mas_updateConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.cell1);
        make.top.equalTo(self.cell3.mas_bottom).with.offset(20);
        make.height.mas_offset(40);
    }];
    [self.cell4 mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.cell1);
        make.height.mas_equalTo(50);
        make.right.equalTo(self.cell1);
        make.top.equalTo(self.cell3Manager.mas_bottom).with.offset(20);
    }];
    [self.cell5 mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.cell1);
        make.height.mas_equalTo(50);
        make.right.equalTo(self.view.mas_right);
        make.top.equalTo(self.cell4.mas_bottom).with.offset(20);
    }];
    
    [super updateViewConstraints];
}

@end

@interface PPNormalizedCellTableViewController () <UITableViewDataSource, UITableViewDelegate>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, copy) NSArray *dataSource;

@end

@implementation PPNormalizedCellTableViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self initUI];
    // Do any additional setup after loading the view.
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

- (void)initUI
{
    self.tableView = [[UITableView alloc] initWithFrame:CGRectZero];
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    [self.view addSubview:self.tableView];
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    self.tableView.tableFooterView = [UIView new];
    self.tableView.tableHeaderView = [UIView new];
    [self.tableView registerClass:[SPKTitleTableViewCell class] forCellReuseIdentifier:@"Cell0"];
    [self.tableView registerClass:[SPKTitleTipTableViewCell class] forCellReuseIdentifier:@"Cell1"];
    [self.tableView registerClass:[SPKTitleImageTableViewCell class] forCellReuseIdentifier:@"Cell2"];
    [self.tableView registerClass:[SPKTitleSwitchTableViewCell class] forCellReuseIdentifier:@"Cell3"];
    [self.tableView registerClass:[SPKTitleInputTableViewCell class] forCellReuseIdentifier:@"Cell4"];
    
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
    
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return 5;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    SPKCommonTableViewCell *cell = (SPKCommonTableViewCell *)[tableView dequeueReusableCellWithIdentifier:[NSString stringWithFormat:@"Cell%@",@(indexPath.row)] forIndexPath:indexPath];
    switch (indexPath.row) {
        case 0: {
            SPKTitleTableViewCell *cell0 = (SPKTitleTableViewCell *)cell;
            cell0.titleLabel.text = @"标题";
        }
            break;
        case 1:{
            SPKTitleTipTableViewCell *cell1 = (SPKTitleTipTableViewCell *)cell;
            cell1.titleLabel.text = @"标题";
            cell1.tipLabel.text = @"描述";
            cell1.redCircleVisible = YES;
            cell1.customAccessoryVisible = YES;
        }
            break;
        case 2:{
            SPKTitleImageTableViewCell *cell2 = (SPKTitleImageTableViewCell *)cell;
            cell2.titleLabel.text = @"标题";
            cell2.customAccessoryVisible = YES;
            cell2.leftImageView.image = [[UIImage imageWithColor:[UIColor blueColor]] sakui_resizableImage];
        }
            break;
        case 3:{
            SPKTitleSwitchTableViewCell *cell3 = (SPKTitleSwitchTableViewCell *)cell;
            cell3.titleLabel.text = @"标题";
            [cell3 setSwitchButtonEnable:YES];
        }
            break;
        case 4:{
            SPKTitleInputTableViewCell *cell4 = (SPKTitleInputTableViewCell *)cell;
            cell4.titleLabel.text = @"标题";
            cell4.textField.placeholder = @"输入前";
        }
            break;
            
        default:
            break;
    }
    return cell;
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView
{
    [self.view becomeFirstResponder];
}

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end
