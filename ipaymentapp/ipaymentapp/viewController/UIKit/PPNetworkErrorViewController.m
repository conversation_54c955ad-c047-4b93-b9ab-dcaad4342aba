//
//  PPNetworkErrorViewController.m
//  ipaymentapp
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2018/2/11.
//  Copyright © 2018年 Meituan.com. All rights reserved.
//

#import "PPNetworkErrorViewController.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"
#import "SPKSubmitButton.h"
#import "SPKToastCenter.h"
#import "SPKUIKitMacros.h"
#import "SPKNetworkErrorView.h"

@interface PPNetworkErrorViewController ()

@property (nonatomic, strong) SPKNetworkErrorView *networkErrorView;

@end

@implementation PPNetworkErrorViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.title = @"错误页面";
    
    self.networkErrorView = ({
        SPKNetworkErrorView *view = [[SPKNetworkErrorView alloc] init];
        view;
    });
    [self.view addSubview:self.networkErrorView];
    
    self.networkErrorView.refreshCommand = [[RACCommand alloc] initWithSignalBlock:^RACSignal *(id input) {
        [[SPKToastCenter defaultCenter] toastWithMessage:@"您点击了刷新"];
        return [RACSignal empty];
    }];
}

@end
