//
//  PPScrollPageViewController.m
//  ipaymentapp
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2018/1/11.
//  Copyright © 2018年 Meituan.com. All rights reserved.
//

#import "PPScrollPageViewController.h"
#import "SPKScrollPageView.h"
#import <Masonry/Masonry.h>
#import "EXTScope.h"

@interface PPScrollPageViewController ()

@property (nonatomic, strong) SPKScrollPageView *pageView;
@property (nonatomic, strong) UILabel *resultLabel;
@property (nonatomic, strong) UILabel *promptLabel;

@end

@implementation PPScrollPageViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.title = @"滑动分段控件";
    self.view.backgroundColor = [UIColor grayColor];
    
    NSArray *titles = @[@"全部", @"美食", @"咖啡甜点", @"加油出行", @"购物", @"生活服务", @"酒店", @"信用卡还款", @"手机充值", @"其他"];
    SPKScrollPageView *pageView = [[SPKScrollPageView alloc] initWithTitles:titles];
    @weakify(self);
    [pageView setDidClickedTitleBlock:^(NSString *title) {
        @strongify(self);
        if (title.length > 0) {
            NSString *message = [NSString stringWithFormat:@"您点击的是:%@", title];
            self.resultLabel.text = message;
        }
    }];
    self.pageView = pageView;
    [self.view addSubview:pageView];
    
    self.promptLabel = ({
        UILabel *label = [UILabel new];
        label.backgroundColor = [UIColor whiteColor];
        label.textAlignment = NSTextAlignmentCenter;
        label.font = [UIFont systemFontOfSize:14];
        label.textColor = [UIColor redColor];
        label.text = @"下面是点击结果";
        label;
    });
    [self.view addSubview:self.promptLabel];
    
    self.resultLabel = ({
        UILabel *label = [UILabel new];
        label.backgroundColor = [UIColor whiteColor];
        label.textAlignment = NSTextAlignmentCenter;
        label.font = [UIFont systemFontOfSize:18];
        label.textColor = [UIColor blueColor];
        label;
    });
    [self.view addSubview:self.resultLabel];
    
    [self setupConstraints];
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

- (void)setupConstraints {
    [self.pageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.view);
        make.top.equalTo(self.view).offset(80);
        make.height.mas_equalTo(40);
    }];
    
    [self.promptLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.view);
        make.top.equalTo(self.pageView).offset(60);
        make.width.equalTo(self.view);
        make.height.mas_equalTo(20);
    }];
    
    [self.resultLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.view);
        make.top.equalTo(self.pageView).offset(80);
        make.width.equalTo(self.view);
        make.height.mas_equalTo(30);
    }];
}

@end
