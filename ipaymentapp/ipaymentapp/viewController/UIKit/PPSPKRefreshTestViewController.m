//
//  PPSPKRefreshTestViewController.m
//  SAKPaymentKit
//
//  Created by <PERSON> on 2018/1/11.
//  Copyright © 2018年 meituan. All rights reserved.
//

#import "PPSPKRefreshTestViewController.h"
#import "SPKRefreshNormalHeader.h"
#import "SPKRefreshNormalFooter.h"
#import "Masonry.h"


@interface PPSPKRefreshTestViewController () <UITableViewDataSource>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, assign) NSInteger sum;

@end

@implementation PPSPKRefreshTestViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.sum = 15;
    self.tableView = [[UITableView alloc] init];
    self.tableView.dataSource = self;
//    __weak typeof(self) wSelf = self;
//    self.tableView.spk_header = [SPKRefreshNormalHeader headerWithRefreshingBlock:^{
//        __strong typeof(self) SSelf = wSelf;
//        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(4 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//            [SSelf.tableView.spk_header endRefreshing];
//        });
//    }];
    SPKRefreshNormalHeader *headerView = [[SPKRefreshNormalHeader alloc] init];
    [headerView setRefreshingTarget:self refreshingAction:@selector(refreshData)];
    headerView.backgroundColor = [UIColor clearColor];
    [headerView setTitle:@"SPKRefreshStateIdle" forState:SPKRefreshStateIdle];
    [headerView setTitle:@"SPKRefreshStatePulling" forState:SPKRefreshStatePulling];
    [headerView setTitle:@"SPKRefreshStateRefreshing" forState:SPKRefreshStateRefreshing];
    [headerView setTitle:@"SPKRefreshStateNoMoreData" forState:SPKRefreshStateNoMoreData];
    self.tableView.spk_header = headerView;
    [self.tableView.spk_header beginRefreshing];
    
    __weak typeof(self) wSelf = self;
    SPKRefreshNormalFooter *footerView = [[SPKRefreshNormalFooter alloc] init];
    [footerView setRefreshingBlock:^{
        __strong typeof(self) SSelf = wSelf;
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            if (SSelf.sum >= 40) {
                [SSelf.tableView reloadData];
                // 拿到当前的上拉刷新控件，变为没有更多数据的状态
                [SSelf.tableView.spk_footer endRefreshingWithNoMoreData];
            } else {
                SSelf.sum += 10;
                [SSelf.tableView reloadData];
                [SSelf.tableView.spk_footer endRefreshing];
            }
            
        });
    }];
    footerView.backgroundColor = [UIColor clearColor];
    [footerView setTitle:@"SPKRefreshStateIdle" forState:SPKRefreshStateIdle];
    [footerView setTitle:@"SPKRefreshStatePulling" forState:SPKRefreshStatePulling];
    [footerView setTitle:@"SPKRefreshStateRefreshing" forState:SPKRefreshStateRefreshing];
    [footerView setTitle:@"SPKRefreshStateNoMoreData" forState:SPKRefreshStateNoMoreData];
    [footerView setEndRefreshingCompletionBlock:^{
        NSLog(@"刷新结束回调");
    }];
    self.tableView.tableHeaderView = [UIView new];
    self.tableView.tableFooterView = [UIView new];
    self.tableView.spk_footer = footerView;
    [self.tableView.spk_footer beginRefreshing];
    [self.tableView registerClass:[UITableViewCell class] forCellReuseIdentifier:@"cell"];

    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
    
    // Do any additional setup after loading the view.
}

- (void)refreshData
{
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self.tableView.spk_header endRefreshing];
    });
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.sum;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"cell" forIndexPath:indexPath];
    cell.textLabel.text = [NSString stringWithFormat:@"index:%@", @(indexPath.row)];
    return cell;
}

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end
