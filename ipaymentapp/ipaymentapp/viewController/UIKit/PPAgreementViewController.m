//
//  PPAgreementViewController.m
//  ipaymentapp
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2017/12/4.
//  Copyright © 2017年 Meituan.com. All rights reserved.
//

#import "PPAgreementViewController.h"
#import "SPKAgreementView.h"
#import "SPKAgreementUIObject.h"
#import <Masonry/Masonry.h>
#import "SPKToastCenter.h"
#import "SAKPortal.h"
#import <ReactiveCocoa/ReactiveCocoa.h>

@interface PPAgreementViewController ()
@property (nonatomic ,strong) SPKAgreementView *agreementView;
@end

@implementation PPAgreementViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.title = @"协议组件测试";
    self.view.backgroundColor = [UIColor whiteColor];
    
    SPKAgreementUIObject *obj = [[SPKAgreementUIObject alloc] init];
    obj.title = @"《支付协议样例》";
    obj.URLString = @"http://www.meituan.com";
    obj.canSelect = YES;
    obj.selected = NO;
    
    self.agreementView = [[SPKAgreementView alloc] initWithAgreement:obj];
    self.agreementView.didClickedCheckBoxButtonBlock = ^(BOOL isSelected){
        [[SPKToastCenter defaultCenter] toastWithMessage:isSelected ? @"勾选协议" : @"取消勾选"];
    };
    
    @weakify(self)
    self.agreementView.didClickedAgreementBlock = ^(NSString *URLString){
        @strongify(self)
        [SAKPortal transferFromViewController:self
                                        toURL:[[NSURL alloc] initWithString:URLString]
                                   completion:nil];
    };
    [self.view addSubview:self.agreementView];
}

- (void)updateViewConstraints {
    
    [self.agreementView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(100);
        make.left.equalTo(self.view).offset(32);
        make.right.equalTo(self.view).offset(-32);
        make.height.equalTo(@20);
    }];
    
    [super updateViewConstraints];
}

@end
