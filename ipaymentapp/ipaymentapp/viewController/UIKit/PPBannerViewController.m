//
//  PPBannerViewController.m
//  ipaymentapp
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2018/2/11.
//  Copyright © 2018年 Meituan.com. All rights reserved.
//

#import "PPBannerViewController.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"
#import "SPKSubmitButton.h"
#import "SPKAlertView.h"
#import "SPKUIKitMacros.h"
#import "SPKNoticeScrollView.h"
#import "SPKBannerUIObject.h"
#import "SAKWebViewController+SPKCustom.h"

@interface PPBannerViewController ()

@property (nonatomic, strong) SPKNoticeScrollView *noticeView; // 不滚动的通知条
@property (nonatomic, strong) SPKNoticeScrollView *noticeScrollView; // 滚动通知条

@property (nonatomic, strong) UILabel *noticeViewLabel;
@property (nonatomic, strong) UILabel *noticeScrollViewLabel;

@end

@implementation PPBannerViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.title = @"运营位";
    [self setupNoticeView];
}

- (void)setupNoticeView {
    self.noticeViewLabel = ({
        UILabel *label = [[UILabel alloc] init];
        label.text = @"通知条(非滚动状态)";
        label.textColor = kSPKTitleLabelTextColor;
        label.font = Font(18);
        label;
    });
    [self.view addSubview:self.noticeViewLabel];
    
    self.noticeView =({
        SPKNoticeScrollView *noticeView = [[SPKNoticeScrollView alloc] init];
        noticeView.content = @"月末充值高峰，到账时间可能延迟";
        noticeView;
    });
    [self.view addSubview:self.noticeView];
    
    self.noticeScrollViewLabel = ({
        UILabel *label = [[UILabel alloc] init];
        label.text = @"通知条(滚动状态)";
        label.textColor = kSPKTitleLabelTextColor;
        label.font = Font(18);
        label;
    });
    [self.view addSubview:self.noticeScrollViewLabel];
    
    self.noticeScrollView =({
        SPKNoticeScrollView *noticeView = [[SPKNoticeScrollView alloc] init];
        noticeView.content = @"尊敬的用户，请您注意：由于生活缴费系统维护中，维护时间为12月8日0：00 - 12月8日23：00，这段时间内将暂时无法使用水煤电充值功能，给您造成的不变我们深表歉意，请您谅解";
        noticeView;
    });
    [self.view addSubview:self.noticeScrollView];
}

- (void)updateViewConstraints {
    [self.noticeViewLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(20);
        make.left.equalTo(self.view).offset(15);
        make.right.equalTo(self.view);
        make.height.mas_equalTo(24);
    }];

    [self.noticeView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.noticeViewLabel.mas_bottom).offset(15);
        make.left.right.equalTo(self.view);
        make.height.mas_equalTo(40);
    }];

    [self.noticeScrollViewLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.noticeView.mas_bottom).offset(25);
        make.left.equalTo(self.view).offset(15);
        make.right.equalTo(self.view);
        make.height.mas_equalTo(24);
    }];

    [self.noticeScrollView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.noticeScrollViewLabel.mas_bottom).offset(15);
        make.left.right.equalTo(self.view);
        make.height.mas_equalTo(40);
    }];
    
    [super updateViewConstraints];
}

@end
