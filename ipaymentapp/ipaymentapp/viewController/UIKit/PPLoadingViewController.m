//
//  PPLoadingViewController.m
//  ipaymentapp
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2018/2/11.
//  Copyright © 2018年 Meituan.com. All rights reserved.
//

#import "PPLoadingViewController.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"
#import "SPKSubmitButton.h"
#import "SPKUIKitMacros.h"
#import "SPKLoadingView.h"
#import "SPKLoadingButton.h"

static const CGFloat kLoadingButtonMargin = 40;
static const CGFloat kLoadingButtonHeight = 44;

@interface PPLoadingViewController ()

@property (nonatomic, strong) UIButton *button1;
@property (nonatomic, strong) UIButton *button2;
@property (nonatomic, strong) SPKLoadingButton *loadingButton;

@end

@implementation PPLoadingViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.title = @"加载动效";
    
    self.button1 = ({
        UIButton *button = [[UIButton alloc] initWithFrame:CGRectZero];
        [button setTitle:@"打开加载动效" forState:UIControlStateNormal];
        button.backgroundColor = [UIColor redColor];
        @weakify(self);
        [[button rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
            @strongify(self);
            [SPKLoadingView spk_loadingViewForView:self.view];
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^(void){
                [SPKLoadingView removeViewAnimated:YES];
            });
        }];
        button;
    });
    [self.view addSubview:self.button1];
    
    self.button2 = ({
        UIButton *button = [[UIButton alloc] initWithFrame:CGRectZero];
        [button setTitle:@"打开带文案的加载动效" forState:UIControlStateNormal];
        button.backgroundColor = [UIColor redColor];
        @weakify(self);
        [[button rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
            @strongify(self);
            [SPKLoadingView spk_loadingViewForView:self.view withLabel:@"您正在使用美团支付"];
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^(void){
                [SPKLoadingView removeViewAnimated:YES];
            });
        }];
        button;
    });
    [self.view addSubview:self.button2];
    
    self.loadingButton = ({
        SPKLoadingButton *button = [[SPKLoadingButton alloc] initWithFrame:CGRectZero];
        [button setTitle:@"加载动效按钮，点击开始加载" forState:UIControlStateNormal];
        button.backgroundColor = kSPKButtonBackgroundColor;
        [[button rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
            [button startAnimation];
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^(void){
                [button stopAnimation];
            });
        }];
        button;
    });
    [self.view addSubview:self.loadingButton];
    [self.view setNeedsUpdateConstraints];
}

- (void)updateViewConstraints {
    [self.button1 mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(kLoadingButtonMargin);
        make.left.equalTo(self.view).offset(kLoadingButtonMargin);
        make.right.equalTo(self.view).offset(-kLoadingButtonMargin);
        make.height.mas_equalTo(kLoadingButtonHeight);
    }];
    
    [self.button2 mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.button1.mas_bottom).offset(kLoadingButtonMargin);
        make.left.equalTo(self.view).offset(kLoadingButtonMargin);
        make.right.equalTo(self.view).offset(-kLoadingButtonMargin);
        make.height.mas_equalTo(kLoadingButtonHeight);
    }];
    
    [self.loadingButton mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.button2.mas_bottom).offset(kLoadingButtonMargin);
        make.left.equalTo(self.view).offset(kLoadingButtonMargin);
        make.right.equalTo(self.view).offset(-kLoadingButtonMargin);
        make.height.mas_equalTo(kLoadingButtonHeight);
    }];
    
    [super updateViewConstraints];
}

@end
