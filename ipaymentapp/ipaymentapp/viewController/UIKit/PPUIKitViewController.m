//
//  PPUIKitViewController.m
//  ipaymentapp
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2017/11/21.
//  Copyright © 2017年 Meituan.com. All rights reserved.
//

#import "PPUIKitViewController.h"
#import "Masonry.h"
#import "PPConfigTableViewCell.h"
#import "SAKPortal.h"
#import "SPKFoundationMacros.h"
#import "NSMutableArray+CIPSafe.h"
#import "NSObject+SPKReturnSelf.h"

#import "SPKToastCenter.h"
#import "SPKAlertView.h"
#import "PPAlertViewController.h"
#import "PPAgreementViewController.h"
#import "PPScrollPageViewController.h"
#import "PPNormalizedCellViewController.h"
#import "PPSPKRefreshTestViewController.h"
#import "PPBannerViewController.h"
#import "PPToastViewController.h"
#import "PPLoadingViewController.h"
#import "PPNetworkErrorViewController.h"
#import "UIColor+Addition.h"

static const CGFloat kPPUIKitCellHeight = 50;

@interface PPUIKitConfigItem : NSObject

@property (nonatomic, copy) NSString *name;
@property (nonatomic, assign) Class class;

- (instancetype)initWithName:(NSString *)name class:(Class)class;

@end

@implementation PPUIKitConfigItem

- (instancetype)initWithName:(NSString *)name class:(Class)class
{
    self = [super init];
    if (self) {
        _name = name;
        _class = class;
    }
    return self;
}

@end

@interface PPUIKitViewController () <UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UITableView *tableview;
@property (nonatomic, strong) NSArray *configArray;

@end

@implementation PPUIKitViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self.view setBackgroundColor:[UIColor generalBackgroundColor]];
    self.title = @"UI 组件库";
    
    [self loadUIKitConfig];
    
    self.tableview = ({
        UITableView *tableview = [[UITableView alloc] init];
        tableview.backgroundColor = [UIColor clearColor];
        tableview.delegate = self;
        tableview.dataSource = self;
        [self.view addSubview:tableview];
        tableview;
    });
#pragma deploymate push "ignored-api-availability"
    if (@available(iOS 11.0, *)) {
        self.tableview.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    }
#pragma deploymate pop
    [self.tableview registerClass:[PPConfigTableViewCell class] forCellReuseIdentifier:NSStringFromClass([PPConfigTableViewCell class])];
    
    [self.view setNeedsUpdateConstraints];
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

- (void)updateViewConstraints
{
    [self.tableview mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view);
        make.left.equalTo(self.view);
        make.right.equalTo(self.view);
        make.bottom.equalTo(self.view);
    }];
    
    [super updateViewConstraints];
}

#pragma mark - UIKitConfig

- (void)loadUIKitConfig {
    NSMutableArray *configs = [NSMutableArray array];
    
    PPUIKitConfigItem *alertItem = [[PPUIKitConfigItem alloc] initWithName:@"alert 弹框" class:[PPAlertViewController class]];
    [configs cipf_safeAddObject:alertItem];
    
    PPUIKitConfigItem *agreementItem = [[PPUIKitConfigItem alloc] initWithName:@"协议组件" class:[PPAgreementViewController class]];
    [configs cipf_safeAddObject:agreementItem];
    
    PPUIKitConfigItem *scrollPageItem = [[PPUIKitConfigItem alloc] initWithName:@"滑动分段组件" class:[PPScrollPageViewController class]];
    [configs cipf_safeAddObject:scrollPageItem];
    
    PPUIKitConfigItem *cellItem = [[PPUIKitConfigItem alloc] initWithName:@"Cell 表单" class:[PPNormalizedCellTableViewController class]];
    [configs cipf_safeAddObject:cellItem];
    
    PPUIKitConfigItem *pullRefreshItem = [[PPUIKitConfigItem alloc] initWithName:@"上下拉刷新" class:[PPSPKRefreshTestViewController class]];
    [configs cipf_safeAddObject:pullRefreshItem];
    
    PPUIKitConfigItem *bannerItem = [[PPUIKitConfigItem alloc] initWithName:@"运营位" class:[PPBannerViewController class]];
    [configs cipf_safeAddObject:bannerItem];
    
    PPUIKitConfigItem *toastItem = [[PPUIKitConfigItem alloc] initWithName:@"toast 提示" class:[PPToastViewController class]];
    [configs cipf_safeAddObject:toastItem];
    
    PPUIKitConfigItem *loadingItem = [[PPUIKitConfigItem alloc] initWithName:@"加载动效" class:[PPLoadingViewController class]];
    [configs cipf_safeAddObject:loadingItem];

    PPUIKitConfigItem *networkErrorItem = [[PPUIKitConfigItem alloc] initWithName:@"错误页面" class:[PPNetworkErrorViewController class]];
    [configs cipf_safeAddObject:networkErrorItem];
    
    self.configArray = [configs copy];
}

#pragma mark - UITableViewDataSource
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return [self.configArray count];
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    PPConfigTableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass([PPConfigTableViewCell class])];
    if (!cell) {
        cell = [[PPConfigTableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:NSStringFromClass([PPConfigTableViewCell class])];
    }
    PPUIKitConfigItem *configItem = [self.configArray objectAtIndex:indexPath.row];
    cell.titleLabel.text = configItem.name;
    cell.switchShow = NO;
    cell.accessoryType = UITableViewCellAccessoryDisclosureIndicator;
    
    return cell;
}

#pragma mark - UITableViewDelegate

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return kPPUIKitCellHeight;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    PPUIKitConfigItem *configItem = [self.configArray objectAtIndex:indexPath.row];
    UIViewController *controller = [[configItem.class new] spk_as:[UIViewController class]];
    if (controller) {
        [self.navigationController pushViewController:controller animated:YES];
    }
    
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
}

@end
