//
//  PPAlertViewController.m
//  ipaymentapp
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2017/11/27.
//  Copyright © 2017年 Meituan.com. All rights reserved.
//

#import "PPAlertViewController.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"
#import "SPKSubmitButton.h"
#import "SPKAlertView.h"
#import "SPKUIKitMacros.h"

static const CGFloat kAlertButtonMargin = 20;
static const CGFloat kAlertButtonHeight = 44;

@interface PPAlertViewController ()

@property (nonatomic, strong) SPKSubmitButton *DL3Button;
@property (nonatomic, strong) SPKSubmitButton *DL4Button;
@property (nonatomic, strong) SPKSubmitButton *DL5Button;
@property (nonatomic, strong) SPKSubmitButton *DL6Button;
@property (nonatomic, strong) SPKSubmitButton *DL8Button;

@end

@implementation PPAlertViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.title = @"弹框测试";
    self.view.backgroundColor = [UIColor whiteColor];
    
    self.DL3Button = ({
        SPKSubmitButton *button = [SPKSubmitButton button];
        [button setTitle:@"DL3" forState:UIControlStateNormal];
        [[button rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
            [SPKAlertView showAlertViewWithTitle:@"标题"
                                         message:@"正文内容展示区域，相对弹窗居中；有折行时，文本左对齐"
                               cancelButtonTitle:@"取消"
                           completionButtonTitle:@"确定"
                                        canceled:^{
                                        }
                                      completion:^{
                                          //
                                      }];
        }];
        button;
    });
    [self.view addSubview:self.DL3Button];
    
    self.DL4Button = ({
        SPKSubmitButton *button = [SPKSubmitButton button];
        [button setTitle:@"DL4" forState:UIControlStateNormal];
        [[button rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
            [SPKAlertView showAlertViewWithTitle:nil
                                         message:@"正文内容居中展示，如果文案过长折行展示（居中对齐）"
                               cancelButtonTitle:@"取消"
                           completionButtonTitle:@"确定"
                                        canceled:^{
                                        }
                                      completion:^{
                                          //
                                      }];
        }];
        button;
    });
    [self.view addSubview:self.DL4Button];
    
    self.DL5Button = ({
        SPKSubmitButton *button = [SPKSubmitButton button];
        [button setTitle:@"DL5" forState:UIControlStateNormal];
        [[button rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
            [SPKAlertView showAlertViewWithTitle:@"标题"
                                         message:@"正文内容展示区域，相对弹窗居中；有折行时，文本左对齐"
                               cancelButtonTitle:nil
                           completionButtonTitle:@"确定"
                                        canceled:^{
                                        }
                                      completion:^{
                                          //
                                      }];
        }];
        button;
    });
    [self.view addSubview:self.DL5Button];
    
    self.DL6Button = ({
        SPKSubmitButton *button = [SPKSubmitButton button];
        [button setTitle:@"DL6" forState:UIControlStateNormal];
        [[button rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
            [SPKAlertView showAlertViewWithMessage:@"内容居中展示" completionButtonTitle:@"知道了" completion:nil];
        }];
        button;
    });
    [self.view addSubview:self.DL6Button];
    
    self.DL8Button = ({
        SPKSubmitButton *button = [SPKSubmitButton button];
        [button setTitle:@"DL8" forState:UIControlStateNormal];
        [[button rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
            [SPKAlertView showErrorAlertViewWithMessage:@"正文内容居中展示，如有错误码改变色值"
                                              errorCode:@"#C6C6C7"
                                      cancelButtonTitle:@"取消" completionButtonTitle:@"确定" canceled:nil completion:nil];
        }];
        button;
    });
    [self.view addSubview:self.DL8Button];
    
    [self setupConstraints];
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

- (void)setupConstraints {
    [self.DL3Button mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(80);
        make.left.equalTo(self.view).offset(kAlertButtonMargin);
        make.right.equalTo(self.view).offset(-kAlertButtonMargin);
        make.height.mas_equalTo(kAlertButtonHeight);
    }];
    
    [self.DL4Button mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.DL3Button.mas_bottom).offset(40);
        make.left.equalTo(self.view).offset(kAlertButtonMargin);
        make.right.equalTo(self.view).offset(-kAlertButtonMargin);
        make.height.mas_equalTo(kAlertButtonHeight);
    }];
    
    [self.DL5Button mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.DL4Button.mas_bottom).offset(40);
        make.left.equalTo(self.view).offset(kAlertButtonMargin);
        make.right.equalTo(self.view).offset(-kAlertButtonMargin);
        make.height.mas_equalTo(kAlertButtonHeight);
    }];
    
    [self.DL6Button mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.DL5Button.mas_bottom).offset(40);
        make.left.equalTo(self.view).offset(kAlertButtonMargin);
        make.right.equalTo(self.view).offset(-kAlertButtonMargin);
        make.height.mas_equalTo(kAlertButtonHeight);
    }];
    
    [self.DL8Button mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.DL6Button.mas_bottom).offset(40);
        make.left.equalTo(self.view).offset(kAlertButtonMargin);
        make.right.equalTo(self.view).offset(-kAlertButtonMargin);
        make.height.mas_equalTo(kAlertButtonHeight);
    }];
}

@end
