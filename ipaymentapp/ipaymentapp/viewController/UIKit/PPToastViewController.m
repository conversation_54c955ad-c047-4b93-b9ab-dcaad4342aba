//
//  PPToastViewController.m
//  ipaymentapp
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2018/2/11.
//  Copyright © 2018年 Meituan.com. All rights reserved.
//

#import "PPToastViewController.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"
#import "SPKSubmitButton.h"
#import "SPKAlertView.h"
#import "SPKUIKitMacros.h"
#import "SPKToastCenter.h"
#import "UIImage+SPKPayment.h"

static const CGFloat kToastButtonMargin = 40;
static const CGFloat kToastButtonHeight = 44;

@interface PPToastViewController ()

@property (nonatomic, strong) UIButton *btn1;
@property (nonatomic, strong) UIButton *btn2;
@property (nonatomic, strong) UIButton *btn3;

@end

@implementation PPToastViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.title = @"toast 提示";
    
    self.btn1 = ({
        UIButton *btn1 = [[UIButton alloc] initWithFrame:CGRectZero];
        [btn1 setTitle:@"普通弱提示" forState:UIControlStateNormal];
        btn1.backgroundColor = [UIColor redColor];
        [[btn1 rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
            dispatch_async(dispatch_get_main_queue(), ^{
                [[SPKToastCenter defaultCenter] toastWithMessage:@"测试测试测试测试测试测试测试测11测试测试测试测试测试测试测试测11"];
            });
        }];
        btn1;
    });
    [self.view addSubview:self.btn1];
    
    self.btn2 = ({
        UIButton *btn2 = [[UIButton alloc] initWithFrame:CGRectZero];
        [btn2 setTitle:@"成功弱提示" forState:UIControlStateNormal];
        btn2.backgroundColor = [UIColor orangeColor];
        [[btn2 rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
            dispatch_async(dispatch_get_main_queue(), ^{
                [[SPKToastCenter defaultCenter] toastWithMessage:@"测试11" image:[UIImage imageNamed:@"payment_kit_icon_success_toast"]];
            });
        }];
        btn2;
    });
    [self.view addSubview:self.btn2];
    
    self.btn3 = ({
        UIButton *btn3 = [[UIButton alloc] initWithFrame:CGRectZero];
        btn3.backgroundColor = [UIColor brownColor];
        [btn3 setTitle:@"错误码弱提示" forState:UIControlStateNormal];
        [[btn3 rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
            dispatch_async(dispatch_get_main_queue(), ^{
                NSError *error = [[NSError alloc] initWithDomain:@"test" code:1234567 userInfo:nil];
                [[SPKToastCenter defaultCenter] toastWithError:error weakenErrorCode:YES];
            });
        }];
        btn3;
    });
    [self.view addSubview:self.btn3];
    [self.view setNeedsUpdateConstraints];
}

- (void)updateViewConstraints {
    [self.btn1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(80);
        make.left.equalTo(self.view).offset(kToastButtonMargin);
        make.right.equalTo(self.view).offset(-kToastButtonMargin);
        make.height.mas_equalTo(kToastButtonHeight);
    }];
    
    [self.btn2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.btn1.mas_bottom).offset(40);
        make.left.equalTo(self.view).offset(kToastButtonMargin);
        make.right.equalTo(self.view).offset(-kToastButtonMargin);
        make.height.mas_equalTo(kToastButtonHeight);
    }];
    
    [self.btn3 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.btn2.mas_bottom).offset(40);
        make.left.equalTo(self.view).offset(kToastButtonMargin);
        make.right.equalTo(self.view).offset(-kToastButtonMargin);
        make.height.mas_equalTo(kToastButtonHeight);
    }];
    
    [super updateViewConstraints];
}

@end
