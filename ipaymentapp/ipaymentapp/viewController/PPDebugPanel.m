//
// Created by <PERSON> on 2018/5/3.
//
#if DEBUG || TEST

#import "PPDebugPanel.h"
#import "View+MASAdditions.h"
#import "METQRCodeScanViewController.h"
#import "TKAlertCenter.h"
#import "SAKUIKit.h"
#import "MRNFullPageViewController.h"
//#import "MRNDebugNetworkInterface.h"
#import "TKAlertCenter+NSError.h"
//#import "MRNDebugConfigModel.h"
#import <ReactiveCocoa.h>
#import "UIButton+Custom.h"
#import "UIColor+Addition.h"

static NSString *const kMRNDefaultServer = @"http://127.0.0.1:8081";

@interface PPDebugPanel () <UITextFieldDelegate>

@property (nonatomic) UITextField *locationTextField;
@property (nonatomic) UIButton *loadMRNButton;
@property (nonatomic) UIButton *scanQRCodeButton;

@property (nonatomic) UIButton *jumpURLButton;

@property (nonatomic, strong) METQRCodeScanViewController *scanViewController;

@end


@implementation PPDebugPanel

- (void)viewDidLoad
{
    [super viewDidLoad];
    self.view.backgroundColor = UIColor.generalBackgroundColor;
    self.title = @"MRN Debug Panel";

    [self setup];
}

- (void)setup
{
    self.scanQRCodeButton = [UIButton greenCustomButton];
    [self.scanQRCodeButton setTitle:@"Scan QRCode" forState:UIControlStateNormal];
    [self.scanQRCodeButton addTarget:self action:@selector(scanQRCode) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.scanQRCodeButton];

    self.locationTextField = [[UITextField alloc] init];
    self.locationTextField.delegate = self;
    self.locationTextField.placeholder = kMRNDefaultServer;

    // Fill saved server
    NSString *savedServer = [[NSUserDefaults standardUserDefaults] objectForKey:@"kMRNSavedServerKey"];
    if (savedServer) {
        self.locationTextField.text = savedServer;
    }
    [self.view addSubview:self.locationTextField];

    // Load bundle button
    self.loadMRNButton = [UIButton greenCustomButton];
    [self.loadMRNButton setTitle:@"Load RN Bundle" forState:UIControlStateNormal];
    [self.view addSubview:self.loadMRNButton];

    @weakify(self);
    [[self.loadMRNButton rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
        @strongify(self);
        [self openMRNPageWithURLString:self.locationTextField.text];
    }];
    
    // URL jump button
    self.jumpURLButton = [UIButton greenCustomButton];
    [self.jumpURLButton setTitle:@"URL Jump" forState:UIControlStateNormal];
    [self.view addSubview:self.jumpURLButton];
    
    [[self.jumpURLButton rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
        @strongify(self);
        [SAKPortal transferFromViewController:self toURL:[NSURL URLWithString:@"meituanpayment://www.meituan.com/mrn?mrn_biz=pay&mrn_entry=payment-mrn&mrn_component=mrnproject"] completion:^(UIViewController<SAKPortalable> * _Nullable viewController, NSError * _Nullable error) {
            
        }];
    }];

    [self layoutSubViews];
}

- (void)layoutSubViews
{
    [self.locationTextField mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@12);
        make.right.equalTo(@(-12));
        make.top.equalTo(@104);
    }];

    [self.loadMRNButton mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.locationTextField);
        make.height.equalTo(@40);
        make.top.equalTo(self.locationTextField.mas_bottom).offset(12);
    }];

    [self.scanQRCodeButton mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.right.height.equalTo(self.loadMRNButton);
        make.top.equalTo(self.loadMRNButton.mas_bottom).offset(12);
    }];
    
    [self.jumpURLButton mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.right.height.equalTo(self.scanQRCodeButton);
        make.top.equalTo(self.scanQRCodeButton.mas_bottom).offset(12);
    }];
}

- (void)scanQRCode
{
    @weakify(self);
    [self.scanViewController setCodeScanSucceed:^(NSURL *resultURL) {
        @strongify(self);
        [self.scanViewController dismissViewControllerAnimated:YES completion:nil];
        [self openMRNPageWithURLString:resultURL.absoluteString];
    }];
    
    [self.scanViewController setCodeScanCancel:^{
        [[TKAlertCenter defaultCenter] postAlertWithMessage:@"Operation canceled"];
    }];
    
    [self.scanViewController setCodeScanFailed:^{
        [[TKAlertCenter defaultCenter] postAlertWithMessage:@"ScanQRCode failed"];
    }];
    
    [self.navigationController presentViewController:self.scanViewController animated:YES completion:nil];
}

- (void)openMRNPageWithURLString:(NSString *)URLString
{
    if (!URLString.length) {
        URLString = kMRNDefaultServer;
    }

//    if (![[URLString lowercaseString] hasPrefix:@"http"]) {
//        NSURLComponents *urlComponents = [NSURLComponents componentsWithString:URLString];
//
//        for (NSURLQueryItem *item in urlComponents.queryItems) {
//            if ([item.name isEqualToString:@"server"]) {
//                URLString = item.value;
//                break;
//            }
//        }
//    }
//    MRNDebugNetworkInterface *networkInterface = [[MRNDebugNetworkInterface alloc] initWithRemoteHostAddr:URLString];
//    @weakify(self);
//    [networkInterface loadDebugConfigWithCompletion:^(NSDictionary *data, NSError *error) {
//        @strongify(self);
//        if (error) {
//            [[TKAlertCenter defaultCenter] postAlertWithError:error];
//        } else {
//            MRNDebugConfigModel *configModel = [MRNDebugConfigModel domainWithJSONDictionary:data[@"data"]];
//            NSURLComponents *urlComponents = [NSURLComponents componentsWithString:URLString];
//            [urlComponents setPath:configModel.bundleURL];
//            [urlComponents setQuery:@"platform=ios"];
//
//            MRNFullPageViewController *fullPageViewController = [[MRNFullPageViewController alloc] initWithURL:[MRNURL debugURLFromURLString:urlComponents.URL.absoluteString
//                                                                                                                               componentName:configModel.moduleName
//                                                                                                                           initialProperties:configModel.initialProperties]];
//            [self.navigationController pushViewController:fullPageViewController animated:YES];
//        }
//    }];
}

#pragma mark - UITextFieldDelegate

- (void)textFieldDidEndEditing:(UITextField *)textField
{
    NSString *text = textField.text;
    if (!text.length) {
        text = kMRNDefaultServer;
    }
    [[NSUserDefaults standardUserDefaults] setObject:text forKey:@"kMRNSavedServerKey"];
}

#pragma mark - UI

- (METQRCodeScanViewController *)scanViewController
{
    if (!_scanViewController) {
        _scanViewController = [[METQRCodeScanViewController alloc] init];
    }
    return _scanViewController;
}

@end

#endif
