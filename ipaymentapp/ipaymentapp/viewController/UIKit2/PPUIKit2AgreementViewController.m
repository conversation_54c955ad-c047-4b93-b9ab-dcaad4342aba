//
//  PPUIKit2AgreementViewController.m
//  ipaymentapp
//
//  Created by wang<PERSON><PERSON><PERSON> on 2018/11/29.
//  Copyright © 2018 Meituan.com. All rights reserved.
//

#import "PPUIKit2AgreementViewController.h"
#import "MTFUAgreementView.h"
#import "SPKToastCenter.h"

@interface PPUIKit2AgreementViewController ()

@end

@implementation PPUIKit2AgreementViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor whiteColor];
    NSURL *URL = [NSURL URLWithString:@"imeituan://www.meituan.com/"];
    MTFUAgreementView *agreementView1 = [[MTFUAgreementView alloc] initWithPreTip:@"查看"
                                                                   agreementTitle:@"《用户协议》"
                                                                     agreementURL:URL];
    agreementView1.didClickedCheckBoxBlock = ^(BOOL isChecked) {
        NSString *str = isChecked ? @" 已勾选" : @"未勾选";
        [[SPKToastCenter defaultCenter] toastWithMessage:str];
    };
    agreementView1.didClickedAgreementBlock = ^(NSURL * _Nullable URLString) {
        [[SPKToastCenter defaultCenter] toastWithMessage:URLString.absoluteString];
    };
    
    [self.view addSubview:agreementView1];
    
    [agreementView1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(self.view.mas_top).offset(100);
        make.centerX.equalTo(self.view);
    }];
    
    MTFUAgreementView *agreementView2 = [[MTFUAgreementView alloc] initWithPreTip:@"查看"
                                                                      preTipColor:[UIColor redColor]
                                                                   agreementTitle:@"《用户协议》"
                                                                   agreementColor:[UIColor blueColor]
                                                                     agreementURL:URL];
    agreementView2.didClickedCheckBoxBlock = ^(BOOL isChecked) {
        NSString *str = isChecked ? @" 已勾选" : @"未勾选";
        [[SPKToastCenter defaultCenter] toastWithMessage:str];
    };
    agreementView2.didClickedAgreementBlock = ^(NSURL * _Nullable URLString) {
        [[SPKToastCenter defaultCenter] toastWithMessage:URLString.absoluteString];
    };
    
    [self.view addSubview:agreementView2];
    
    [agreementView2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(agreementView1.mas_bottom).offset(50);
        make.centerX.equalTo(agreementView1);
    }];
    
}

@end
