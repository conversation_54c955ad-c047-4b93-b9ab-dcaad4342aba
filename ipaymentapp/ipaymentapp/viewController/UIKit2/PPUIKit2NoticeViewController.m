//
//  PPUIKit2NoticeViewController.m
//  ipaymentapp
//
//  Created by wangpeng<PERSON> on 2018/11/29.
//  Copyright © 2018 Meituan.com. All rights reserved.
//

#import "PPUIKit2NoticeViewController.h"
#import "MTFUNoticeScrollView.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"
#import "MTFUSubmitButton.h"
#import "SPKUIKitMacros.h"
#import "MTFUToastCenter.h"

static const CGFloat kNoticeHeight = 36;

@interface PPUIKit2NoticeViewController () <MTFUNoticeAccessoryDelegate>

@property (nonatomic, strong) MTFUNoticeScrollView *notice1;
@property (nonatomic, strong) MTFUNoticeScrollView *notice2;
@property (nonatomic, strong) MTFUNoticeScrollView *notice3;
@property (nonatomic, strong) MTFUNoticeScrollView *notice4;
@property (nonatomic, strong) MTFUNoticeScrollView *notice5;
@property (nonatomic, strong) MTFUNoticeScrollView *notice6;
@property (nonatomic, strong) MTFUNoticeScrollView *notice7;
@property (nonatomic, strong) MTFUNoticeScrollView *notice8;

@property (nonatomic, strong) UISwitch *switch1;

@end

@implementation PPUIKit2NoticeViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.view.backgroundColor = [UIColor whiteColor];
    
    self.title = @"通知栏";
    
    [self configNotices];
    
    [self configSwitch];
    
    [self.view setNeedsUpdateConstraints];
}

- (void)configNotices
{
    self.notice1 = ({
        MTFUNoticeScrollView *notice = [[MTFUNoticeScrollView alloc] init];
        [notice setContent:@"提示信息，不足一行，自动居中"];
        notice;
    });
    [self.view addSubview:self.notice1];
    
    self.notice2 = ({
        MTFUNoticeScrollView *notice = [[MTFUNoticeScrollView alloc] init];
        [notice setContent:@"提示信息，超长滚动显示，提示信息，超长滚动显示，提示信息，超长滚动显示。"];
        notice;
    });
    [self.view addSubview:self.notice2];
    
    self.notice3 = ({
        MTFUNoticeScrollView *notice = [[MTFUNoticeScrollView alloc] init];
        [notice setContent:@"提示信息，可关闭，且超长滚动显示，提示信息，可关闭，且超长滚动显示。"];
        [notice setAccessoryType:MTFUNoticeScrollViewAccessoryTypeClose];
        [notice setDelegate:self];
        notice;
    });
    [self.view addSubview:self.notice3];
    
    self.notice4 = ({
        MTFUNoticeScrollView *notice = [[MTFUNoticeScrollView alloc] init];
        [notice setContent:@"提示信息，可跳转，且超长截断，提示信息，可跳转，且超长截断。"];
        [notice setAccessoryType:MTFUNoticeScrollViewAccessoryTypeDisclosureIndicator];
        [notice setDelegate:self];
        notice;
    });
    [self.view addSubview:self.notice4];
    
    self.notice5 = ({
        MTFUNoticeScrollView *notice = [[MTFUNoticeScrollView alloc] init];
        [notice setContent:@"提示信息，带小喇叭，可关闭，超长滚动，提示信息，带小喇叭，可关闭，超长滚动。"];
        [notice setIconType:MTFUNoticeScrollViewIconTypeHorn];
        [notice setAccessoryType:MTFUNoticeScrollViewAccessoryTypeClose];
        [notice setDelegate:self];
        notice;
    });
    [self.view addSubview:self.notice5];
    
    self.notice6 = ({
        MTFUNoticeScrollView *notice = [[MTFUNoticeScrollView alloc] init];
        [notice setContent:@"提示信息，带小喇叭，可跳转，超长截断，提示信息，带小喇叭，可跳转，超长截断。"];
        [notice setIconType:MTFUNoticeScrollViewIconTypeHorn];
        [notice setAccessoryType:MTFUNoticeScrollViewAccessoryTypeDisclosureIndicator];
        [notice setDelegate:self];
        notice;
    });
    [self.view addSubview:self.notice6];
    
    self.notice7 = ({
        MTFUNoticeScrollView *notice = [[MTFUNoticeScrollView alloc] init];
        [notice setContent:@"很短的信息，右侧有icon"];
        [notice setAccessoryType:MTFUNoticeScrollViewAccessoryTypeClose];
        [notice setDelegate:self];
        notice;
    });
    [self.view addSubview:self.notice7];
    
    self.notice8 = ({
        MTFUNoticeScrollView *notice = [[MTFUNoticeScrollView alloc] init];
        [notice setContent:@"很短的信息，左侧有icon"];
        [notice setIconType:MTFUNoticeScrollViewIconTypeHorn];
        [notice setDelegate:self];
        notice;
    });
    [self.view addSubview:self.notice8];
    
}

- (void)accessoryButtonClicked:(MTFUNoticeScrollViewAccessoryType)accessoryType {
    dispatch_async(dispatch_get_main_queue(), ^{
        if (accessoryType == MTFUNoticeScrollViewAccessoryTypeClose) {
            [[MTFUToastCenter defaultCenter] toastWithMessage:@"关闭！"];
        } else if (accessoryType == MTFUNoticeScrollViewAccessoryTypeDisclosureIndicator) {
            [[MTFUToastCenter defaultCenter] toastWithMessage:@"跳转！"];
        }
    });
}

- (void)configSwitch
{
    self.switch1 = ({
        UISwitch *switch1 = [[UISwitch alloc] initWithFrame:CGRectZero];
        switch1.onTintColor = HEXCOLOR(0xFF411A);
        [switch1 addTarget:self action:@selector(switchAction:) forControlEvents:UIControlEventValueChanged];
        [self.view addSubview:switch1];
        switch1;
    });
}

- (void)switchAction:(id)sender
{
    UISwitch *switchButton = (UISwitch*)sender;
    MTFUNoticeScrollViewType type = MTFUNoticeScrollViewTypeDefault;
    if ([switchButton isOn]) {
        type = MTFUNoticeScrollViewTypeWarning;
    }
    [self.notice1 setNoticeScrollViewType:type];
    [self.notice2 setNoticeScrollViewType:type];
    [self.notice3 setNoticeScrollViewType:type];
    [self.notice4 setNoticeScrollViewType:type];
    [self.notice5 setNoticeScrollViewType:type];
    [self.notice6 setNoticeScrollViewType:type];
    [self.notice7 setNoticeScrollViewType:type];
    [self.notice8 setNoticeScrollViewType:type];
}


- (void)updateViewConstraints {
    
    [self.notice1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(40);
        make.width.equalTo(self.view);
        make.height.mas_equalTo(kNoticeHeight);
    }];
    
    [self.notice2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.notice1.mas_bottom).offset(30);
        make.width.equalTo(self.view);
        make.height.mas_equalTo(kNoticeHeight);
    }];
    
    [self.notice3 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.notice2.mas_bottom).offset(30);
        make.width.equalTo(self.view);
        make.height.mas_equalTo(kNoticeHeight);
    }];
    
    [self.notice4 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.notice3.mas_bottom).offset(30);
        make.width.equalTo(self.view);
        make.height.mas_equalTo(kNoticeHeight);
    }];
    
    [self.notice5 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.notice4.mas_bottom).offset(30);
        make.width.equalTo(self.view);
        make.height.mas_equalTo(kNoticeHeight);
    }];
    
    [self.notice6 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.notice5.mas_bottom).offset(30);
        make.width.equalTo(self.view);
        make.height.mas_equalTo(kNoticeHeight);
    }];
    
    [self.notice7 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.notice6.mas_bottom).offset(30);
        make.width.equalTo(self.view);
        make.height.mas_equalTo(kNoticeHeight);
    }];
    
    [self.notice8 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.notice7.mas_bottom).offset(30);
        make.width.equalTo(self.view);
        make.height.mas_equalTo(kNoticeHeight);
    }];
    
    
    [self.switch1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.notice8.mas_bottom).offset(40);
        make.centerX.equalTo(self.view.mas_centerX);
    }];
    
    
    [super updateViewConstraints];
}

@end
