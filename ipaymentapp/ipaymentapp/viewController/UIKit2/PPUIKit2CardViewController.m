//
//  PPUIKit2CardViewController.m
//  ipaymentapp
//
//  Created by wangpengbo on 2018/11/29.
//  Copyright © 2018 Meituan.com. All rights reserved.
//

#import "PPUIKit2CardViewController.h"
#import "MTFUBankCardView.h"
#import "SAKUIKitMacros.h"
#import <Masonry.h>

@interface PPUIKit2CardViewController ()

@property (nonatomic, strong) MTFUBankCardView *cardView;

@end

@implementation PPUIKit2CardViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.cardView = [MTFUBankCardView new];
    
    /**
     @property (nonatomic, copy) NSString *bankName; // 银行名称
     @property (nonatomic, copy) NSString *tailNO; // 尾号
     @property (nonatomic, copy) NSString *cardTypeString; // 卡类型
     @property (nonatomic, strong) NSURL *iconURL; // icon 地址
     
     @property (nonatomic, strong) NSURL *watermarkURL; // 水印地址
     @property (nonatomic, strong) UIColor *originColor; // 开始颜色
     @property (nonatomic, strong) UIColor *endColor; // 结束颜色
     @property (nonatomic, strong) NSURL *tagImageURL; // tag 图片
     @property (nonatomic, strong) MTFUBankCardTipsUIObject *cardTips; // 信用卡，还款信息
     
     
     bankcardId: *********,
     tailNo: "4048",
     bankName: "招商银行",
     cardType: "储蓄卡",
     maxAmountPerTime: 20000,
     maxAmountPerDay: 20000,
     icon: "https://img.meituan.net/pay/cmb_g2.png",
     bin: "********",
     freeze: 0,
     modtime: **********,
     watermark: "https://img.meituan.net/pay/watermark_cmb_g1.png",
     backgroundColor: "#FF7185:#FF4C4C",
     cardMark: 0,
     background: "https://img.meituan.net/pay/bankcard_bg_red.png",
     foreign:
     */
    MTFUBankCardUIObject *UIObject = [MTFUBankCardUIObject new];
    UIObject.bankName = @"招商银行";
    UIObject.tailNO = @"4048";
    UIObject.cardTypeString = @"储蓄卡";
    UIObject.iconURL = [NSURL URLWithString:@"https://img.meituan.net/pay/cmb_g2.png"];
    UIObject.watermarkURL = [NSURL URLWithString:@"https://img.meituan.net/pay/watermark_cmb_g1.png"];
    UIObject.originColor = HEXCOLOR(0xFF7185);
    UIObject.endColor = HEXCOLOR(0xFF4C4C);
//    UIObject.tagImageURL = [NSURL URLWithString:@"https://img.meituan.net/pay/bankcard_bg_red.png"];
    [self.cardView setUIObject:UIObject];
    
    [self.view addSubview:self.cardView];
    [self.view setNeedsUpdateConstraints];
}

- (void)updateViewConstraints
{
    [self.cardView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(100);
        make.left.equalTo(self.view).offset(15);
        make.right.equalTo(self.view).offset(-15);
        make.height.equalTo(@(133));
    }];
    
    [super updateViewConstraints];
}

@end
