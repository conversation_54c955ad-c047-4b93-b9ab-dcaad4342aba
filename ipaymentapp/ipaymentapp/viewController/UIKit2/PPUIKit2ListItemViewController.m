//
//  PPUIKit2ListItemViewController.m
//  ipaymentapp
//
//  Created by wangpeng<PERSON> on 2018/11/29.
//  Copyright © 2018 Meituan.com. All rights reserved.
//

#define MTFUTableViewCellMacro

#import "PPUIKit2ListItemViewController.h"

#ifdef MTFUTableViewCellMacro
#import "MTFUTableViewCell.h"
#import "MTFUBankTableViewCell.h"
#endif

#ifdef MTFUTableViewCellMacro
#import "MTFUTableViewCell.h"
#import "MTFUBankTableViewCell.h"
#endif

@interface PPUIKit2ListItemViewController ()<UITableViewDelegate, UITableViewDataSource>

#ifdef MTFUTableViewCellMacro
@property (nonatomic, strong) UITableView *tableView;
#endif
@end

@implementation PPUIKit2ListItemViewController

- (void)viewDidLoad {
    [super viewDidLoad];
#ifdef MTFUTableViewCellMacro
    // Do any additional setup after loading the view.
    self.tableView = ({
        UITableView *tableView = [[UITableView alloc] init];
        [self.view addSubview:tableView];
        tableView.delegate = self;
        tableView.dataSource = self;
        [tableView setSeparatorColor:HEXCOLOR(0xE5E5E5)];
        [tableView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(self.view);
        }];
        
        tableView;
    });
#endif
}

#ifdef MTFUTableViewCellMacro
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 10;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    switch ( section) {
        case 0:
            return 4;
            break;
        case 1:
            return 2;
            break;
        case 2:
            return 4;
            break;
        case 3:
            return 4;
            break;
        case 4:
            return 4;
            break;
        case 5:
            return 4;
            break;
        case 6:
            return 4;
            break;
        case 7:
            return 8;
            break;
        case 8:
            return 4;
            break;
        case 9:
            return 1;
            break;
            
        default:
            break;
    }
    return 0;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    switch ( indexPath.section) {
        case 0:
            return [MTFUBankTableViewCell heightForCell];
            break;
        case 1:
            return [MTFUTableViewCell heightForCell:NO];
            break;
        case 2:
            return [MTFUTableViewCell heightForCell:NO];
            break;
        case 3:
            return [MTFUTableViewCell heightForCell:NO];
            break;
        case 4:
            return [MTFUTableViewCell heightForCell:NO];
            break;
        case 5:
            return [MTFUTableViewCell heightForCell:NO];
            break;
        case 6:
            return [MTFUTableViewCell heightForCell:YES];
            break;
        case 7:
            return [MTFUTableViewCell heightForCell:YES];
            break;
        case 8:
            return [MTFUTableViewCell heightForCell:YES];
            break;
        case 9:
            return [MTFUTableViewCell heightForAgreementCell];
            break;
            
        default:
            break;
    }
    return 0;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section
{
    return 50;
}

- (NSString *)tableView:(UITableView *)tableView titleForHeaderInSection:(NSInteger)section
{
    switch ( section) {
        case 0:
            return @"银行卡样式";
            break;
        case 1:
            return @"标题+箭头";
            break;
        case 2:
            return @"标题+内容";
            break;
        case 3:
            return @"标题+箭头+内容";
            break;
        case 4:
            return @"标题+箭头+内容+红点";
            break;
        case 5:
            return @"标题+开关";
            break;
        case 6:
            return @"标题+副标题+箭头";
            break;
        case 7:
            return @"标题+副标题+内容+箭头";
            break;
        case 8:
            return @"标题+副标题+开关";
            break;
        case 9:
            return @"用户协议";
            break;
            
        default:
            break;
    }
    return @"";

}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    NSString *str = NSStringFromClass([MTFUTableViewCell class]);
    if ( indexPath.section == 0) {
        str = NSStringFromClass([MTFUBankTableViewCell class]);
    }
    
    MTFUTableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:str];
    if (!cell) {
        if ( indexPath.section == 0) {
            cell = [[MTFUBankTableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:str];
        } else {
            cell = [[MTFUTableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:str];
        }
    }
    
    switch ( indexPath.section) {
            //银行卡样式
        case 0:
        {
            switch ( indexPath.row) {
                case 0:
                    [cell setTitle:@"招商银行信用卡（9079）"];
                    [(MTFUBankTableViewCell*)cell setIconURL:@"https://img.meituan.net/pay/cmb_g2.png"];
                    break;
                case 1:
                    [cell setTitle:@"招商银行信用卡（9079）"];
                    break;
                case 2:
                    [cell setTitle:@"招商银行信用卡（9079）"];
                    [(MTFUBankTableViewCell*)cell setIsChecked:YES];
                    [(MTFUBankTableViewCell*)cell setIconURL:@"https://img.meituan.net/pay/cmb_g2.png"];
                    break;
                case 3:
                    [cell setTitle:@"招商银行信用卡（9079）"];
                    [(MTFUBankTableViewCell*)cell setIsChecked:YES];
                    break;
                    
                default:
                    break;
            }
        }
            break;
            //标题+箭头
        case 1:
        {
            switch ( indexPath.row) {
                case 0:
                    [cell setTitle:@"标题+箭头"];
                    break;
                case 1:
                    [cell setTitle:@"标题+箭头+标题很长很长很长很长很长很长很长很长很长很长很长很长"];
                    break;
                default:
                    break;
            }
        }
            break;
            //标题+内容
        case 2:
        {
            switch ( indexPath.row) {
                case 0:
                    [cell setTitle:@"标题+内容"];
                    [cell setContent:@"内容"];
                    [cell setAccessoryViewHidden:YES];
                    break;
                case 1:
                    [cell setTitle:@"标题+内容+标题很长很长很长很长很长很长很长很长很长很长很长很长"];
                    [cell setContent:@"内容"];
                    [cell setAccessoryViewHidden:YES];
                    break;
                case 2:
                    [cell setTitle:@"标题+内容"];
                    [cell setContent:@"内容很长很长很长很长很长很长很长很长很长很长很长很长很长很长"];
                    [cell setAccessoryViewHidden:YES];
                    break;
                case 3:
                    [cell setTitle:@"标题+内容+标题很长很长很长很长很长很长很长很长很长很长很长很长"];
                    [cell setContent:@"内容很长很长很长很长很长很长很长很长很长很长很长很长很长很长"];
                    [cell setAccessoryViewHidden:YES];
                    break;
                default:
                    break;
            }
        }
            break;
            //标题+箭头+内容
        case 3:
        {
            switch ( indexPath.row) {
                case 0:
                    [cell setTitle:@"标题+内容+箭头"];
                    [cell setContent:@"内容"];
                    break;
                case 1:
                    [cell setTitle:@"标题+内容+箭头+标题很长很长很长很长很长很长很长很长很长很长很长很长"];
                    [cell setContent:@"内容"];
                    break;
                case 2:
                    [cell setTitle:@"标题+内容+箭头"];
                    [cell setContent:@"内容很长很长很长很长很长很长很长很长很长很长很长很长很长很长"];
                    break;
                case 3:
                    [cell setTitle:@"标题+内容+箭头+标题很长很长很长很长很长很长很长很长很长很长很长很长"];
                    [cell setContent:@"内容很长很长很长很长很长很长很长很长很长很长很长很长很长很长"];
                    break;
                    
                default:
                    break;
            }
        }
            break;
            //标题+箭头+内容+红点
        case 4:
        {
            switch ( indexPath.row) {
                case 0:
                    [cell setTitle:@"标题+内容+箭头"];
                    [cell setContent:@"内容"];
                    [cell setRedCircleVisible:YES];
                    break;
                case 1:
                    [cell setTitle:@"标题+内容+箭头+标题很长很长很长很长很长很长很长很长很长很长很长很长"];
                    [cell setContent:@"内容"];
                    [cell setRedCircleVisible:YES];
                    break;
                case 2:
                    [cell setTitle:@"标题+内容+箭头"];
                    [cell setContent:@"内容很长很长很长很长很长很长很长很长很长很长很长很长很长很长"];
                    [cell setRedCircleVisible:YES];
                    break;
                case 3:
                    [cell setTitle:@"标题+内容+箭头+标题很长很长很长很长很长很长很长很长很长很长很长很长"];
                    [cell setContent:@"内容很长很长很长很长很长很长很长很长很长很长很长很长很长很长"];
                    [cell setRedCircleVisible:YES];
                    break;
                    
                default:
                    break;
            }
        }
            break;
            //标题+开关
        case 5:
        {
            switch ( indexPath.row) {
                case 0:
                    [cell setCheckBoxHidden:NO];
                    [cell setCheckState:NO];
                    [cell setTitle:@"标题+开关关状态"];
                    cell.didChangeSwitchStateBlock = ^(BOOL isChecked) {
                        NSLog(@"%d", isChecked);
                    };
                    break;
                case 1:
                    [cell setCheckBoxHidden:NO];
                    [cell setCheckState:YES];
                    [cell setTitle:@"标题+开关开状态"];
                    cell.didChangeSwitchStateBlock = ^(BOOL isChecked) {
                        NSLog(@"%d", isChecked);
                    };
                    break;
                case 2:
                    [cell setCheckBoxHidden:NO];
                    [cell setCheckState:NO];
                    [cell setTitle:@"标题+开关关状态+标题很长很长很长很长很长很长很长很长很长很长很长很长"];
                    cell.didChangeSwitchStateBlock = ^(BOOL isChecked) {
                        NSLog(@"%d", isChecked);
                    };
                    break;
                case 3:
                    [cell setCheckBoxHidden:NO];
                    [cell setCheckState:YES];
                    [cell setTitle:@"标题+开关开状态+标题很长很长很长很长很长很长很长很长很长很长很长很长"];
                    cell.didChangeSwitchStateBlock = ^(BOOL isChecked) {
                        NSLog(@"%d", isChecked);
                    };
                    break;
                default:
                    break;
            }
        }
            break;
            //标题+副标题+箭头
        case 6:
        {
            switch ( indexPath.row) {
                case 0:
                    [cell setTitle:@"标题+副标题+箭头"];
                    [cell setSubTitle:@"副标题"];
                    break;
                case 1:
                    [cell setTitle:@"标题+副标题+箭头+标题很长很长很长很长很长很长很长很长很长很长"];
                    [cell setSubTitle:@"副标题"];
                    break;
                case 2:
                    [cell setTitle:@"标题+副标题+箭头"];
                    [cell setSubTitle:@"副标题+副标题很长很长很长很长很长很长很长很长很长很长很长很长"];
                    break;
                case 3:
                    [cell setTitle:@"标题+副标题+箭头+标题很长很长很长很长很长很长很长很长很长很长"];
                    [cell setSubTitle:@"副标题+副标题很长很长很长很长很长很长很长很长很长很长很长很长"];
                    break;
                default:
                    break;
            }
        }
            break;
            //标题+副标题+内容+箭头
        case 7:
        {
            switch ( indexPath.row) {
                case 0:
                    [cell setTitle:@"标题+副标题+内容+箭头"];
                    [cell setSubTitle:@"副标题"];
                    [cell setContent:@"内容"];
                    break;
                case 1:
                    [cell setTitle:@"标题+副标题+内容+箭头+标题很长很长很长很长很长很长很长很长很长很长很长很长很长很长"];
                    [cell setSubTitle:@"副标题"];
                    [cell setContent:@"内容"];
                    break;
                case 2:
                    [cell setTitle:@"标题+副标题+内容+箭头"];
                    [cell setSubTitle:@"副标题+副标题很长很长很长很长很长很长很长很长很长很长很长很长很长很长"];
                    [cell setContent:@"内容"];
                    break;
                case 3:
                    [cell setTitle:@"标题+副标题+内容+箭头"];
                    [cell setSubTitle:@"副标题"];
                    [cell setContent:@"内容+内容很长很长很长很长很长很长很长很长很长很长很长很长"];
                    break;
                case 4:
                    [cell setTitle:@"标题+副标题+内容+箭头"];
                    [cell setSubTitle:@"副标题+副标题很长很长很长很长很长很长很长很长很长很长很长很长很长很长"];
                    [cell setContent:@"内容+内容很长很长很长很长很长很长很长很长很长很长很长很长"];
                    break;
                case 5:
                    [cell setTitle:@"标题+副标题+内容+箭头+标题很长很长很长很长很长很长很长很长很长很长很长很长很长很长"];
                    [cell setSubTitle:@"副标题"];
                    [cell setContent:@"内容+内容很长很长很长很长很长很长很长很长很长很长很长很长"];
                    break;
                case 6:
                    [cell setTitle:@"标题+副标题+内容+箭头+标题很长很长很长很长很长很长很长很长很长很长很长很长很长很长"];
                    [cell setSubTitle:@"副标题+副标题很长很长很长很长很长很长很长很长很长很长很长很长很长很长"];
                    [cell setContent:@"内容"];
                    break;
                case 7:
                    [cell setTitle:@"标题+副标题+内容+箭头+标题很长很长很长很长很长很长很长很长很长很长很长很长很长很长"];
                    [cell setSubTitle:@"副标题+副标题很长很长很长很长很长很长很长很长很长很长很长很长很长很长"];
                    [cell setContent:@"内容+内容很长很长很长很长很长很长很长很长很长很长很长很长"];
                    break;
                default:
                    break;
            }
        }
            break;
            //标题+开关+副标题
        case 8:
        {
            switch ( indexPath.row) {
                case 0:
                    [cell setCheckBoxHidden:NO];
                    [cell setCheckState:NO];
                    [cell setTitle:@"标题+开关关状态"];
                    [cell setSubTitle:@"副标题"];
                    cell.didChangeSwitchStateBlock = ^(BOOL isChecked) {
                        NSLog(@"%d", isChecked);
                    };
                    break;
                case 1:
                    [cell setCheckBoxHidden:NO];
                    [cell setCheckState:YES];
                    [cell setTitle:@"标题+开关开状态"];
                    [cell setSubTitle:@"副标题+副标题很长很长很长很长很长很长很长很长很长很长很长很长"];
                    cell.didChangeSwitchStateBlock = ^(BOOL isChecked) {
                        NSLog(@"%d", isChecked);
                    };
                    break;
                case 2:
                    [cell setCheckBoxHidden:NO];
                    [cell setCheckState:NO];
                    [cell setTitle:@"标题+开关关状态+标题很长很长很长很长很长很长很长很长很长很长很长很长"];
                    [cell setSubTitle:@"副标题"];
                    cell.didChangeSwitchStateBlock = ^(BOOL isChecked) {
                        NSLog(@"%d", isChecked);
                    };
                    break;
                case 3:
                    [cell setCheckBoxHidden:NO];
                    [cell setCheckState:YES];
                    [cell setTitle:@"标题+开关开状态+标题很长很长很长很长很长很长很长很长很长很长很长很长"];
                    [cell setSubTitle:@"副标题+副标题很长很长很长很长很长很长很长很长很长很长很长很长"];
                    cell.didChangeSwitchStateBlock = ^(BOOL isChecked) {
                        NSLog(@"%d", isChecked);
                    };
                    break;
                default:
                    break;
            }
        }
            break;
            //标题+开关+副标题+用户协议
        case 9:
        {
            switch ( indexPath.row) {
                case 0:
                    [cell setCheckBoxHidden:NO];
                    [cell setCheckState:NO];
                    [cell setTitle:@"标题+开关关状态"];
                    [cell setSubTitle:@"副标题"];
                    [cell showAgreement:YES];
                    cell.didChangeSwitchStateBlock = ^(BOOL isChecked) {
                        NSLog(@"%d", isChecked);
                    };
                    break;
                default:
                    break;
            }
        }
            break;
        default:
            break;
    }
    
    return cell;
}
#endif
@end
