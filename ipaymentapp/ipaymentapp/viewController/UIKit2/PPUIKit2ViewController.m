//
//  PPUIKit2ViewController.m
//  ipaymentapp
//
//  Created by wangpengbo on 2018/11/29.
//  Copyright © 2018 Meituan.com. All rights reserved.
//

#import "PPUIKit2ViewController.h"
#import "PPUIKit2TabSwitchViewController.h"
#import "PPUIKit2ButtonViewController.h"
#import "PPUIKit2AgreementViewController.h"
#import "PPUIKit2PickerViewController.h"
#import "PPUIKit2NoticeViewController.h"
#import "PPUIKit2BannerViewController.h"
#import "PPUIKit2CardViewController.h"
#import "PPUIKit2ListItemViewController.h"
#import "PPUIKit2ActivityViewController.h"
#import "PPUIKit2ToastViewController.h"
#import "PPUIKit2AlertViewController.h"
#import "PPUIKitActionSheetViewController.h"
#import "Masonry.h"
#import "PPConfigTableViewCell.h"
#import "NSMutableArray+CIPSafe.h"
#import "NSObject+SPKReturnSelf.h"
#import "SAKWebViewController+SPKCustom.h"
#import "UIColor+Addition.h"


static const CGFloat kPPUIKitCellHeight = 50;

typedef void(^PPUIKit2ConfigBlock)();

@interface PPUIKit2ConfigItem : NSObject

@property (nonatomic, copy) NSString *name;
@property (nonatomic, assign) Class class;
@property (nonatomic, copy) PPUIKit2ConfigBlock block;

- (instancetype)initWithName:(NSString *)name class:(Class)class;
- (instancetype)initWithName:(NSString *)name block:(PPUIKit2ConfigBlock)block;

@end

@implementation PPUIKit2ConfigItem

- (instancetype)initWithName:(NSString *)name class:(Class)class
{
    self = [super init];
    if (self) {
        _name = name;
        _class = class;
    }
    return self;
}

- (instancetype)initWithName:(NSString *)name block:(PPUIKit2ConfigBlock)block
{
    self = [super init];
    if (self) {
        _name = name;
        _block = block;
    }
    return self;
}

@end

@interface PPUIKit2ViewController () <UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UITableView *tableview;
@property (nonatomic, strong) NSArray *configArray;

@end


@implementation PPUIKit2ViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self.view setBackgroundColor:[UIColor generalBackgroundColor]];
    self.title = @"UI 组件库 2.0版本";
    
    [self loadUIKitConfig];
    
    self.tableview = ({
        UITableView *tableview = [[UITableView alloc] init];
        tableview.backgroundColor = [UIColor clearColor];
        tableview.delegate = self;
        tableview.dataSource = self;
        [self.view addSubview:tableview];
        tableview;
    });
#pragma deploymate push "ignored-api-availability"
    if (@available(iOS 11.0, *)) {
        self.tableview.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    }
#pragma deploymate pop
    [self.tableview registerClass:[PPConfigTableViewCell class] forCellReuseIdentifier:NSStringFromClass([PPConfigTableViewCell class])];
    
    [self.view setNeedsUpdateConstraints];
}

- (void)updateViewConstraints
{
    [self.tableview mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view);
        make.left.equalTo(self.view);
        make.right.equalTo(self.view);
        make.bottom.equalTo(self.view);
    }];
    
    [super updateViewConstraints];
}

#pragma mark - UIKitConfig

- (void)loadUIKitConfig {
    NSMutableArray *configs = [NSMutableArray array];
    
    @weakify(self)
    PPUIKit2ConfigItem *h5Item = [[PPUIKit2ConfigItem alloc] initWithName:@"H5 VIX" block:^{
        @strongify(self)
        SAKWebViewController *controller = [SAKWebViewController spk_webViewController];
        [controller loadURL:[NSURL URLWithString:@"http://vix.sankuai.com/demo.html?ehwebview=1"]];
        
        [self.navigationController pushViewController:controller animated:YES];
    }];
    [configs cipf_safeAddObject:h5Item];
    
    PPUIKit2ConfigItem *tabItem = [[PPUIKit2ConfigItem alloc] initWithName:@"标签栏" class:[PPUIKit2TabSwitchViewController class]];
    [configs cipf_safeAddObject:tabItem];
    
    PPUIKit2ConfigItem *buttonItem = [[PPUIKit2ConfigItem alloc] initWithName:@"按钮 & 单复选框 & 滑动开关 & 气泡" class:[PPUIKit2ButtonViewController class]];
    [configs cipf_safeAddObject:buttonItem];
    
    PPUIKit2ConfigItem *agreementItem = [[PPUIKit2ConfigItem alloc] initWithName:@"协议" class:[PPUIKit2AgreementViewController class]];
    [configs cipf_safeAddObject:agreementItem];
    
    PPUIKit2ConfigItem *pickerItem = [[PPUIKit2ConfigItem alloc] initWithName:@"滑动选择器" class:[PPUIKit2PickerViewController class]];
    [configs cipf_safeAddObject:pickerItem];
    
    PPUIKit2ConfigItem *noticeItem = [[PPUIKit2ConfigItem alloc] initWithName:@"通知" class:[PPUIKit2NoticeViewController class]];
    [configs cipf_safeAddObject:noticeItem];
    
    PPUIKit2ConfigItem *bannerItem = [[PPUIKit2ConfigItem alloc] initWithName:@"Banner" class:[PPUIKit2BannerViewController class]];
    [configs cipf_safeAddObject:bannerItem];
    
    PPUIKit2ConfigItem *cardItem = [[PPUIKit2ConfigItem alloc] initWithName:@"银行卡" class:[PPUIKit2CardViewController class]];
    [configs cipf_safeAddObject:cardItem];
    
    PPUIKit2ConfigItem *listItemItem = [[PPUIKit2ConfigItem alloc] initWithName:@"列表项" class:[PPUIKit2ListItemViewController class]];
    [configs cipf_safeAddObject:listItemItem];
    
    PPUIKit2ConfigItem *activityItem = [[PPUIKit2ConfigItem alloc] initWithName:@"活动指示器" class:[PPUIKit2ActivityViewController class]];
    [configs cipf_safeAddObject:activityItem];
    
    PPUIKit2ConfigItem *toastItem = [[PPUIKit2ConfigItem alloc] initWithName:@"Toast" class:[PPUIKit2ToastViewController class]];
    [configs cipf_safeAddObject:toastItem];
    
    PPUIKit2ConfigItem *alertItem = [[PPUIKit2ConfigItem alloc] initWithName:@"对话框" class:[PPUIKit2AlertViewController class]];
    [configs cipf_safeAddObject:alertItem];
    
    PPUIKit2ConfigItem *actionItem = [[PPUIKit2ConfigItem alloc] initWithName:@"动作面板" class:[PPUIKitActionSheetViewController class]];
    [configs cipf_safeAddObject:actionItem];
    self.configArray = [configs copy];
}

#pragma mark - UITableViewDataSource
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return [self.configArray count];
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    PPConfigTableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass([PPConfigTableViewCell class])];
    if (!cell) {
        cell = [[PPConfigTableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:NSStringFromClass([PPConfigTableViewCell class])];
    }
    PPUIKit2ConfigItem *configItem = [self.configArray objectAtIndex:indexPath.row];
    cell.titleLabel.text = configItem.name;
    cell.switchShow = NO;
    cell.accessoryType = UITableViewCellAccessoryDisclosureIndicator;
    
    return cell;
}

#pragma mark - UITableViewDelegate

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return kPPUIKitCellHeight;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    PPUIKit2ConfigItem *configItem = [self.configArray objectAtIndex:indexPath.row];
    if (configItem.block) {
        configItem.block();
    }
    if (configItem.class) {
        UIViewController *controller = [[configItem.class new] spk_as:[UIViewController class]];
        if (controller) {
            [self.navigationController pushViewController:controller animated:YES];
        }
    }
    
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
}

@end
