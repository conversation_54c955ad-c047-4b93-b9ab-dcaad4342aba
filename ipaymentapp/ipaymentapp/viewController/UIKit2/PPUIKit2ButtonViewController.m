//
//  PPUIKit2ButtonViewController.m
//  ipaymentapp
//
//  Created by wangpengbo on 2018/11/29.
//  Copyright © 2018 Meituan.com. All rights reserved.
//

#import "PPUIKit2ButtonViewController.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"
#import "MTFUSubmitButton.h"
#import "SPKUIKitMacros.h"
#import "UIImage+SPKPayment.h"
#import "MTFUCheckbox.h"
#import "MTFURadio.h"
#import "MTFUHotTagView.h"
#import "MTFUSwitch.h"

static const CGFloat kButtonMargin = 40;
static const CGFloat kButtonHeight = 44;
static const CGFloat kSmallButtonHeight = 28;

@interface PPUIKit2ButtonViewController ()

@property (nonatomic, strong) UIScrollView *scrollView;

// 普通按钮
@property (nonatomic, strong) MTFUSubmitButton *btn1;
@property (nonatomic, strong) MTFUSubmitButton *btn2;
@property (nonatomic, strong) MTFUSubmitButton *btn3;
@property (nonatomic, strong) MTFUSubmitButton *btn4;
@property (nonatomic, strong) MTFUSubmitButton *btn5;
@property (nonatomic, strong) MTFUSubmitButton *btn6;
@property (nonatomic, strong) MTFUSubmitButton *btn7;
@property (nonatomic, strong) MTFUSubmitButton *btn8;

// 小按钮
@property (nonatomic, strong) MTFUSubmitButton *sbtn1;
@property (nonatomic, strong) MTFUSubmitButton *sbtn2;
@property (nonatomic, strong) MTFUSubmitButton *sbtn3;
@property (nonatomic, strong) MTFUSubmitButton *sbtn4;
@property (nonatomic, strong) MTFUSubmitButton *sbtn5;
@property (nonatomic, strong) MTFUSubmitButton *sbtn6;
@property (nonatomic, strong) MTFUSubmitButton *sbtn7;
@property (nonatomic, strong) MTFUSubmitButton *sbtn8;
@property (nonatomic, strong) MTFUSubmitButton *sbtn9;
@property (nonatomic, strong) MTFUSubmitButton *sbtn10;
@property (nonatomic, strong) MTFUSubmitButton *sbtn11;
@property (nonatomic, strong) MTFUSubmitButton *sbtn12;

@property (nonatomic, strong) MTFUCheckbox *checkbox1;
@property (nonatomic, strong) MTFUCheckbox *checkbox2;
@property (nonatomic, strong) MTFUCheckbox *checkbox3;

@property (nonatomic, strong) MTFURadio *radio1;
@property (nonatomic, strong) MTFURadio *radio2;
@property (nonatomic, strong) MTFURadio *radio3;
@property (nonatomic, strong) MTFURadio *radio4;

@property (nonatomic, strong) MTFUSwitch *switch1;
@property (nonatomic, strong) MTFUSwitch *switch2;
@property (nonatomic, strong) MTFUSwitch *switch3;

@property (nonatomic, strong) MTFUHotTagView *hotTag1;
@property (nonatomic, strong) MTFUHotTagView *hotTag2;
@property (nonatomic, strong) MTFUHotTagView *hotTag3;
@property (nonatomic, strong) MTFUHotTagView *hotTag4;
@property (nonatomic, strong) MTFUHotTagView *hotTag5;

@end

@implementation PPUIKit2ButtonViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.title = @"按钮";
    
    self.scrollView = [[UIScrollView alloc] init];
    self.scrollView.frame = CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT);
    self.scrollView.backgroundColor = [UIColor whiteColor];
    [self.view addSubview:self.scrollView];
    
    [self configButtons];
    
    [self configSmallButtons];
    
    [self configCheckboxs];
    
    [self configRadios];

    [self configSwitch];
    
    [self configHotTags];
    
    [self.view setNeedsUpdateConstraints];
}

- (void)configButtons
{
    self.btn1 = ({
        MTFUSubmitButton *btn = [MTFUSubmitButton button];
        [btn setTitle:@"默认状态" forState:UIControlStateNormal];
        btn;
    });
    [self.scrollView addSubview:self.btn1];
    
    self.btn2 = ({
        MTFUSubmitButton *btn = [MTFUSubmitButton button];
        [btn setEnabled:NO];
        [btn setTitle:@"禁用态" forState:UIControlStateNormal];
        btn;
    });
    [self.scrollView addSubview:self.btn2];
    
    self.btn3 = ({
        MTFUSubmitButton *btn = [MTFUSubmitButton buttonWithCorner:kButtonHeight / 2];
        [btn setTitle:@"默认状态（圆角）" forState:UIControlStateNormal];
        btn;
    });
    [self.scrollView addSubview:self.btn3];
    
    self.btn4 = ({
        MTFUSubmitButton *btn = [MTFUSubmitButton buttonWithCorner:kButtonHeight / 2];
        [btn setEnabled:NO];
        [btn setTitle:@"禁用态（圆角）" forState:UIControlStateNormal];
        btn;
    });
    [self.scrollView addSubview:self.btn4];
    
    self.btn5 = ({
        MTFUSubmitButton *btn = [MTFUSubmitButton strokedButton];
        [btn setTitle:@"默认状态（次要按钮）" forState:UIControlStateNormal];
        btn;
    });
    [self.scrollView addSubview:self.btn5];
    
    self.btn6 = ({
        MTFUSubmitButton *btn = [MTFUSubmitButton strokedButton];
        [btn setEnabled:NO];
        [btn setTitle:@"禁用态（次要按钮）" forState:UIControlStateNormal];
        btn;
    });
    [self.scrollView addSubview:self.btn6];
    
    self.btn7 = ({
        MTFUSubmitButton *btn = [MTFUSubmitButton strokedButtonWithCorner:kButtonHeight / 2];
        [btn setTitle:@"默认状态（圆角次要按钮）" forState:UIControlStateNormal];
        btn;
    });
    [self.scrollView addSubview:self.btn7];
    
    self.btn8 = ({
        MTFUSubmitButton *btn = [MTFUSubmitButton strokedButtonWithCorner:kButtonHeight / 2];
        [btn setEnabled:NO];
        [btn setTitle:@"禁用态（圆角次要按钮）" forState:UIControlStateNormal];
        btn;
    });
    [self.scrollView addSubview:self.btn8];
}

- (void)configSmallButtons
{
    self.sbtn1 = ({
        MTFUSubmitButton *btn = [MTFUSubmitButton button];
        btn.titleLabel.font = Font(12);
        [btn setTitle:@"按钮" forState:UIControlStateNormal];
        [self.scrollView addSubview:btn];
        btn;
    });
    
    self.sbtn2 = ({
        MTFUSubmitButton *btn = [MTFUSubmitButton button];
        btn.titleLabel.font = Font(12);
        [btn setTitle:@"操作按钮" forState:UIControlStateNormal];
        [self.scrollView addSubview:btn];
        btn;
    });
    
    self.sbtn3 = ({
        MTFUSubmitButton *btn = [MTFUSubmitButton buttonWithCorner:kSmallButtonHeight / 2];
        btn.titleLabel.font = Font(12);
        [btn setTitle:@"操作按钮" forState:UIControlStateNormal];
        [self.scrollView addSubview:btn];
        btn;
    });
    
    self.sbtn4 = ({
        MTFUSubmitButton *btn = [MTFUSubmitButton strokedButton];
        btn.titleLabel.font = Font(12);
        [btn setTitle:@"按钮" forState:UIControlStateNormal];
        [self.scrollView addSubview:btn];
        btn;
    });
    [self.scrollView addSubview:self.btn4];
    
    self.sbtn5 = ({
        MTFUSubmitButton *btn = [MTFUSubmitButton strokedButton];
        btn.titleLabel.font = Font(12);
        [btn setTitle:@"操作按钮" forState:UIControlStateNormal];
        [self.scrollView addSubview:btn];
        btn;
    });
    [self.scrollView addSubview:self.btn5];
    
    self.sbtn6 = ({
        MTFUSubmitButton *btn = [MTFUSubmitButton strokedButtonWithCorner:kSmallButtonHeight / 2];
        btn.titleLabel.font = Font(12);
        [btn setTitle:@"操作按钮" forState:UIControlStateNormal];
        [self.scrollView addSubview:btn];
        btn;
    });
    [self.scrollView addSubview:self.btn6];
    
    self.sbtn7 = ({
        MTFUSubmitButton *btn = [MTFUSubmitButton button];
        [btn setEnabled:NO];
        btn.titleLabel.font = Font(12);
        [btn setTitle:@"按钮" forState:UIControlStateNormal];
        [self.scrollView addSubview:btn];
        btn;
    });
    
    self.sbtn8 = ({
        MTFUSubmitButton *btn = [MTFUSubmitButton button];
        [btn setEnabled:NO];
        btn.titleLabel.font = Font(12);
        [btn setTitle:@"操作按钮" forState:UIControlStateNormal];
        [self.scrollView addSubview:btn];
        btn;
    });
    
    self.sbtn9 = ({
        MTFUSubmitButton *btn = [MTFUSubmitButton buttonWithCorner:kSmallButtonHeight / 2];
        [btn setEnabled:NO];
        btn.titleLabel.font = Font(12);
        [btn setTitle:@"操作按钮" forState:UIControlStateNormal];
        [self.scrollView addSubview:btn];
        btn;
    });
    
    self.sbtn10 = ({
        MTFUSubmitButton *btn = [MTFUSubmitButton strokedButton];
        [btn setEnabled:NO];
        btn.titleLabel.font = Font(12);
        [btn setTitle:@"按钮" forState:UIControlStateNormal];
        [self.scrollView addSubview:btn];
        btn;
    });
    [self.scrollView addSubview:self.btn4];
    
    self.sbtn11 = ({
        MTFUSubmitButton *btn = [MTFUSubmitButton strokedButton];
        [btn setEnabled:NO];
        btn.titleLabel.font = Font(12);
        [btn setTitle:@"操作按钮" forState:UIControlStateNormal];
        [self.scrollView addSubview:btn];
        btn;
    });
    [self.scrollView addSubview:self.btn5];
    
    self.sbtn12 = ({
        MTFUSubmitButton *btn = [MTFUSubmitButton strokedButtonWithCorner:kSmallButtonHeight / 2];
        [btn setEnabled:NO];
        btn.titleLabel.font = Font(12);
        [btn setTitle:@"操作按钮" forState:UIControlStateNormal];
        [self.scrollView addSubview:btn];
        btn;
    });
    [self.scrollView addSubview:self.btn6];
}


- (void)configCheckboxs
{
    self.checkbox1 = ({
        MTFUCheckbox *checkbox = [[MTFUCheckbox alloc] init];
        checkbox.checked = YES;
        [self.scrollView addSubview:checkbox];
        checkbox;
    });
    
    self.checkbox2 = ({
        MTFUCheckbox *checkbox = [[MTFUCheckbox alloc] init];
        checkbox.checkable = NO;
        [self.scrollView addSubview:checkbox];
        checkbox;
    });

    self.checkbox3 = ({
        MTFUCheckbox *checkbox = [[MTFUCheckbox alloc] init];
        checkbox.enabled = NO;
        [self.scrollView addSubview:checkbox];
        checkbox;
    });
}

- (void)configRadios
{
    self.radio1 = ({
        MTFURadio *radio = [[MTFURadio alloc] init];
        radio.checked = YES;
        [self.scrollView addSubview:radio];
        radio;
    });
    
    self.radio2 = ({
        MTFURadio *radio = [[MTFURadio alloc] init];
        radio.checkable = NO;
        [self.scrollView addSubview:radio];
        radio;
    });
    
    self.radio3 = ({
        MTFURadio *radio = [[MTFURadio alloc] init];
        radio.enabled = NO;
        [self.scrollView addSubview:radio];
        radio;
    });
    
    self.radio4 = ({
        MTFURadio *radio = [[MTFURadio alloc] init];
        [self.scrollView addSubview:radio];
        radio;
    });
}

- (void)configSwitch
{
    self.switch1 = ({
        MTFUSwitch *switch1 = [[MTFUSwitch alloc] initWithFrame:CGRectZero];
        switch1.on = YES;
        [self.scrollView addSubview:switch1];
        switch1;
    });
    
    self.switch2 = ({
        MTFUSwitch *switch2 = [[MTFUSwitch alloc] initWithFrame:CGRectZero];
        switch2.on = YES;
        switch2.enabled = NO;
        [self.scrollView addSubview:switch2];
        switch2;
    });
    
    self.switch3 = ({
        MTFUSwitch *switch3 = [[MTFUSwitch alloc] initWithFrame:CGRectZero];
        switch3.on = NO;
        switch3.enabled = NO;
        [self.scrollView addSubview:switch3];
        switch3;
    });
}

- (void)configHotTags
{
    self.hotTag1 = ({
        MTFUHotTagView *hotTag = [[MTFUHotTagView alloc] initDotTag];
        [self.scrollView addSubview:hotTag];
        hotTag;
    });
    
    self.hotTag2 = ({
        MTFUHotTagView *hotTag = [[MTFUHotTagView alloc] init];
        hotTag.numberContent = 6;
        [self.scrollView addSubview:hotTag];
        hotTag;
    });

    self.hotTag3 = ({
        MTFUHotTagView *hotTag = [[MTFUHotTagView alloc] init];
        hotTag.numberContent = 100;
        [self.scrollView addSubview:hotTag];
        hotTag;
    });
    
    self.hotTag4 = ({
        MTFUHotTagView *hotTag = [[MTFUHotTagView alloc] init];
        hotTag.stringContent = @"推荐";
        hotTag.isLeftBottomRightAngle = YES;
        [self.scrollView addSubview:hotTag];
        hotTag;
    });
    
    self.hotTag5 = ({
        MTFUHotTagView *hotTag = [[MTFUHotTagView alloc] init];
        hotTag.stringContent = @"控件不会帮忙做字数限制~！";
        hotTag.isLeftBottomRightAngle = YES;
        [self.scrollView addSubview:hotTag];
        hotTag;
    });
}


- (void)updateViewConstraints {
    
    [self.scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
        make.bottom.equalTo(self.hotTag5).offset(30);
    }];
    
    [self.btn1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.scrollView).offset(20);
        make.left.equalTo(self.view).offset(kButtonMargin);
        make.right.equalTo(self.view).offset(-kButtonMargin);
        make.height.mas_equalTo(kButtonHeight);
    }];
    
    [self.btn2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.btn1.mas_bottom).offset(15);
        make.left.equalTo(self.view).offset(kButtonMargin);
        make.right.equalTo(self.view).offset(-kButtonMargin);
        make.height.mas_equalTo(kButtonHeight);
    }];
    
    [self.btn3 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.btn2.mas_bottom).offset(15);
        make.left.equalTo(self.view).offset(kButtonMargin);
        make.right.equalTo(self.view).offset(-kButtonMargin);
        make.height.mas_equalTo(kButtonHeight);
    }];
    
    [self.btn4 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.btn3.mas_bottom).offset(15);
        make.left.equalTo(self.view).offset(kButtonMargin);
        make.right.equalTo(self.view).offset(-kButtonMargin);
        make.height.mas_equalTo(kButtonHeight);
    }];
    
    [self.btn5 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.btn4.mas_bottom).offset(15);
        make.left.equalTo(self.view).offset(kButtonMargin);
        make.right.equalTo(self.view).offset(-kButtonMargin);
        make.height.mas_equalTo(kButtonHeight);
    }];
    
    [self.btn6 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.btn5.mas_bottom).offset(15);
        make.left.equalTo(self.view).offset(kButtonMargin);
        make.right.equalTo(self.view).offset(-kButtonMargin);
        make.height.mas_equalTo(kButtonHeight);
    }];
    
    [self.btn7 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.btn6.mas_bottom).offset(15);
        make.left.equalTo(self.view).offset(kButtonMargin);
        make.right.equalTo(self.view).offset(-kButtonMargin);
        make.height.mas_equalTo(kButtonHeight);
    }];
    
    [self.btn8 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.btn7.mas_bottom).offset(15);
        make.left.equalTo(self.view).offset(kButtonMargin);
        make.right.equalTo(self.view).offset(-kButtonMargin);
        make.height.mas_equalTo(kButtonHeight);
    }];
    
    // 小按钮
    [self.sbtn1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.btn8.mas_bottom).offset(30);
        make.left.equalTo(self.view).offset(kButtonMargin);
        make.width.mas_equalTo(64);
        make.height.mas_equalTo(kSmallButtonHeight);
    }];
    
    [self.sbtn2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.btn8.mas_bottom).offset(30);
        make.left.equalTo(self.sbtn1.mas_right).offset(kButtonMargin);
        make.width.mas_equalTo(72);
        make.height.mas_equalTo(kSmallButtonHeight);
    }];
    
    [self.sbtn3 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.btn8.mas_bottom).offset(30);
        make.left.equalTo(self.sbtn2.mas_right).offset(kButtonMargin);
        make.width.mas_equalTo(72);
        make.height.mas_equalTo(kSmallButtonHeight);
    }];
    
    [self.sbtn4 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.sbtn1.mas_bottom).offset(15);
        make.left.equalTo(self.view).offset(kButtonMargin);
        make.width.mas_equalTo(64);
        make.height.mas_equalTo(kSmallButtonHeight);
    }];
    
    [self.sbtn5 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.sbtn1.mas_bottom).offset(15);
        make.left.equalTo(self.sbtn4.mas_right).offset(kButtonMargin);
        make.width.mas_equalTo(72);
        make.height.mas_equalTo(kSmallButtonHeight);
    }];
    
    [self.sbtn6 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.sbtn1.mas_bottom).offset(15);
        make.left.equalTo(self.sbtn5.mas_right).offset(kButtonMargin);
        make.width.mas_equalTo(72);
        make.height.mas_equalTo(kSmallButtonHeight);
    }];
    
    [self.sbtn7 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.sbtn4.mas_bottom).offset(15);
        make.left.equalTo(self.view).offset(kButtonMargin);
        make.width.mas_equalTo(64);
        make.height.mas_equalTo(kSmallButtonHeight);
    }];
    
    [self.sbtn8 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.sbtn4.mas_bottom).offset(15);
        make.left.equalTo(self.sbtn7.mas_right).offset(kButtonMargin);
        make.width.mas_equalTo(72);
        make.height.mas_equalTo(kSmallButtonHeight);
    }];
    
    [self.sbtn9 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.sbtn4.mas_bottom).offset(15);
        make.left.equalTo(self.sbtn8.mas_right).offset(kButtonMargin);
        make.width.mas_equalTo(72);
        make.height.mas_equalTo(kSmallButtonHeight);
    }];
    
    [self.sbtn10 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.sbtn7.mas_bottom).offset(15);
        make.left.equalTo(self.view).offset(kButtonMargin);
        make.width.mas_equalTo(64);
        make.height.mas_equalTo(kSmallButtonHeight);
    }];
    
    [self.sbtn11 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.sbtn7.mas_bottom).offset(15);
        make.left.equalTo(self.sbtn10.mas_right).offset(kButtonMargin);
        make.width.mas_equalTo(72);
        make.height.mas_equalTo(kSmallButtonHeight);
    }];
    
    [self.sbtn12 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.sbtn7.mas_bottom).offset(15);
        make.left.equalTo(self.sbtn11.mas_right).offset(kButtonMargin);
        make.width.mas_equalTo(72);
        make.height.mas_equalTo(kSmallButtonHeight);
    }];
    
    // 单复选框、滑动开关、徽标
    [self.checkbox1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.sbtn12.mas_bottom).offset(30);
        make.left.equalTo(self.view).offset(kButtonMargin);
        make.width.height.mas_equalTo(16);
    }];

    [self.checkbox2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.sbtn12.mas_bottom).offset(30);
        make.left.equalTo(self.checkbox1).offset(kButtonMargin);
        make.width.height.mas_equalTo(16);
    }];

    [self.checkbox3 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.sbtn12.mas_bottom).offset(30);
        make.left.equalTo(self.checkbox2).offset(kButtonMargin);
        make.width.height.mas_equalTo(16);
    }];
    
    
    [self.radio1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.checkbox1.mas_bottom).offset(30);
        make.left.equalTo(self.view).offset(kButtonMargin);
        make.width.height.mas_equalTo(16);
    }];
    
    [self.radio2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.checkbox1.mas_bottom).offset(30);
        make.left.equalTo(self.radio1).offset(kButtonMargin);
        make.width.height.mas_equalTo(16);
    }];
    
    [self.radio3 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.checkbox1.mas_bottom).offset(30);
        make.left.equalTo(self.radio2).offset(kButtonMargin);
        make.width.height.mas_equalTo(16);
    }];
    
    [self.radio4 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.radio3.mas_centerY);
        make.left.equalTo(self.radio3).offset(kButtonMargin);
        make.width.height.mas_equalTo(22);
    }];
    
    [self.switch1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.radio1.mas_bottom).offset(30);
        make.left.equalTo(self.view).offset(kButtonMargin);
    }];
    
    [self.switch2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.radio1.mas_bottom).offset(30);
        make.left.equalTo(self.switch1.mas_right).offset(kButtonMargin);
    }];
    
    [self.switch3 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.radio1.mas_bottom).offset(30);
        make.left.equalTo(self.switch2.mas_right).offset(kButtonMargin);
    }];
    
    [self.hotTag1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.hotTag2.mas_centerY);
        make.left.equalTo(self.view).offset(kButtonMargin);
    }];
    
    [self.hotTag2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.switch1.mas_bottom).offset(30);
        make.left.equalTo(self.hotTag1.mas_right).offset(kButtonMargin / 2);
    }];

    [self.hotTag3 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.switch1.mas_bottom).offset(30);
        make.left.equalTo(self.hotTag2.mas_right).offset(kButtonMargin / 2);
    }];
    
    [self.hotTag4 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.switch1.mas_bottom).offset(30);
        make.left.equalTo(self.hotTag3.mas_right).offset(kButtonMargin / 2);
    }];
    
    [self.hotTag5 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.switch1.mas_bottom).offset(30);
        make.left.equalTo(self.hotTag4.mas_right).offset(kButtonMargin / 2);
    }];
    
    [super updateViewConstraints];
}

@end
