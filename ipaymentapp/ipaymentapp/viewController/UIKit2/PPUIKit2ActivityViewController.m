//
//  PPUIKit2ActivityViewController.m
//  ipaymentapp
//
//  Created by wangpengbo on 2018/11/29.
//  Copyright © 2018 Meituan.com. All rights reserved.
//

#import "PPUIKit2ActivityViewController.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"
#import "MTFUSubmitButton.h"
#import "SPKUIKitMacros.h"
#import "MTFULoadingView.h"
#import "MTFULoadingButton.h"
#import "UIImage+MTFU.h"

static const CGFloat kLoadingButtonMargin = 40;
static const CGFloat kLoadingButtonHeight = 44;

@interface PPUIKit2ActivityViewController ()

@property (nonatomic, strong) MTFUSubmitButton *button1;
@property (nonatomic, strong) MTFUSubmitButton *button2;
@property (nonatomic, strong) MTFULoadingButton *loadingButton;

@end

@implementation PPUIKit2ActivityViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.title = @"加载动效";
    
    self.button1 = ({
        MTFUSubmitButton *button = [MTFUSubmitButton button];
        [button setTitle:@"美团钱包 💎" forState:UIControlStateNormal];
        @weakify(self);
        [[button rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
            @strongify(self);
            [MTFULoadingView loadingViewForView:self.view withImage:[UIImage mtfu_imageNamed:@"mtfu_icon_loading_brand.png"] label:@"美团钱包"];
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^(void){
                [MTFULoadingView removeViewAnimated:YES];
            });
        }];
        button;
    });
    [self.view addSubview:self.button1];
    
    self.button2 = ({
        MTFUSubmitButton *button = [MTFUSubmitButton button];
        [button setTitle:@"去支付 💳" forState:UIControlStateNormal];
        @weakify(self);
        [[button rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
            @strongify(self);
            [MTFULoadingView loadingViewForView:self.view withImage:[UIImage mtfu_imageNamed:@"mtfu_icon_loading_card.png"] label:@"去支付"];
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^(void){
                [MTFULoadingView removeViewAnimated:YES];
            });
        }];
        button;
    });
    [self.view addSubview:self.button2];
    
    self.loadingButton = ({
        MTFULoadingButton *button = [[MTFULoadingButton alloc] initWithFrame:CGRectZero];
        [button setTitle:@"加载动效 🚀" forState:UIControlStateNormal];
        button.backgroundColor = kSPKButtonBackgroundColor;
        @weakify(button);
        [[button rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
            @strongify(button);
            [button startAnimation];
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^(void){
                @strongify(button);
                [button stopAnimation];
            });
        }];
        button;
    });
    [self.view addSubview:self.loadingButton];
    [self.view setNeedsUpdateConstraints];
}

- (void)updateViewConstraints {
    [self.button1 mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(kLoadingButtonMargin);
        make.left.equalTo(self.view).offset(kLoadingButtonMargin);
        make.right.equalTo(self.view).offset(-kLoadingButtonMargin);
        make.height.mas_equalTo(kLoadingButtonHeight);
    }];
    
    [self.button2 mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.button1.mas_bottom).offset(kLoadingButtonMargin);
        make.left.equalTo(self.view).offset(kLoadingButtonMargin);
        make.right.equalTo(self.view).offset(-kLoadingButtonMargin);
        make.height.mas_equalTo(kLoadingButtonHeight);
    }];
    
    [self.loadingButton mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.button2.mas_bottom).offset(kLoadingButtonMargin);
        make.left.equalTo(self.view).offset(kLoadingButtonMargin);
        make.right.equalTo(self.view).offset(-kLoadingButtonMargin);
        make.height.mas_equalTo(kLoadingButtonHeight);
    }];
    
    [super updateViewConstraints];
}

@end
