//
//  PPUIKit2PickerViewController.m
//  ipaymentapp
//
//  Created by wangpeng<PERSON> on 2018/11/29.
//  Copyright © 2018 Meituan.com. All rights reserved.
//

#define MTFUAccessoryPickerViewMacro

#import "PPUIKit2PickerViewController.h"

#ifdef MTFUAccessoryPickerViewMacro
#import "MTFUDatePickerView.h"
#import "MTFUAccessoryPickerView.h"
#endif

@interface PPUIKit2PickerViewController ()

#ifdef MTFUAccessoryPickerViewMacro
@property (nonatomic, strong) MTFUDatePickerView *datePicker;
@property (nonatomic, strong) MTFUAccessoryPickerView *accessoryPickerView;
@property (nonatomic, strong) MTFUAccessoryPickerView *monthPickerView;
@property (nonatomic, strong) UITextField *textField;
@property (nonatomic, strong) UITextField *textField2;
@property (nonatomic, strong) UITextField *textField3;
#endif

@end

@implementation PPUIKit2PickerViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.title = @"日期选择器";
    
#ifdef MTFUAccessoryPickerViewMacro
    self.textField = ({
        UITextField *textField = [[UITextField alloc] init];
        [self.view addSubview:textField];
        [textField setPlaceholder:@"选择日期"];
        [textField mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(self.view);
            make.top.equalTo(@10);
            make.height.equalTo(@44);
        }];
        textField;
    });
    
    self.textField2 = ({
        UITextField *textField = [[UITextField alloc] init];
        [self.view addSubview:textField];
        [textField setPlaceholder:@"选择工作"];
        [textField mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(self.view);
            make.top.equalTo(self.textField.mas_bottom).offset(10);
            make.height.equalTo(@44);
        }];
        textField;
    });
    
    self.textField3 = ({
        UITextField *textField = [[UITextField alloc] init];
        [self.view addSubview:textField];
        [textField setPlaceholder:@"选择月份"];
        [textField mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(self.view);
            make.top.equalTo(self.textField2.mas_bottom).offset(10);
            make.height.equalTo(@44);
        }];
        textField;
    });
    
    self.datePicker = ({
        
        NSDateComponents *minComponents = [[NSCalendar currentCalendar] components:NSCalendarUnitMonth | NSCalendarUnitYear fromDate:[NSDate date]];
        minComponents.timeZone = [NSTimeZone defaultTimeZone];
        minComponents.year = 1949;
        minComponents.month = 10;
        
        NSDate *minDate = [[NSCalendar currentCalendar] dateFromComponents:minComponents];
        
        NSDateComponents *maxComponents = [[NSCalendar currentCalendar] components:NSCalendarUnitMonth | NSCalendarUnitYear fromDate:[NSDate date]];
        maxComponents.timeZone = [NSTimeZone defaultTimeZone];
        maxComponents.year = 2049 ;
        maxComponents.month = 10;
        
        NSDate *maxDate = [[NSCalendar currentCalendar] dateFromComponents:maxComponents];
        
        MTFUDatePickerView *datePicker = [[MTFUDatePickerView alloc] init];
        datePicker.backgroundColor = [UIColor whiteColor];
        datePicker.date = [NSDate date];
        datePicker.minDate = minDate;
        datePicker.maxDate = maxDate;
        self.textField.inputView = datePicker;
        @weakify(self)
        datePicker.didCancelSelectBlock = ^( ) {
            @strongify(self)
            [self.textField resignFirstResponder];
        };
        datePicker.didFinishSelectedDateBlock = ^(NSDate * _Nonnull date) {
            
            @strongify(self)
            [self.textField resignFirstResponder];
            
            NSDateComponents *minComponents = [[NSCalendar currentCalendar] components:NSCalendarUnitMonth | NSCalendarUnitYear fromDate:date];
            minComponents.timeZone = [NSTimeZone defaultTimeZone];
            self.textField.text = [NSString stringWithFormat:@"%d %d", minComponents.year, minComponents.month];
        };
        datePicker;
    });
    
    self.accessoryPickerView = ({
        
        MTFUAccessoryPickerView *datePicker = [[MTFUAccessoryPickerView alloc] initWithTitles:@[@"工人",@"农民",@"中国人民解放军",@"工程师",@"中华人民共和国国务院总理",@"中华人民共和国人大常务委员会常务委员长",@"哈哈",@"呵呵"]];
        datePicker.backgroundColor = [UIColor whiteColor];
        self.textField2.inputView = datePicker;
        @weakify(self)
        datePicker.didCancelSelectBlock = ^( ) {
            @strongify(self)
            [self.textField2 resignFirstResponder];
        };
        datePicker.didFinishSelectedBlock = ^(NSString *title) {
            
            @strongify(self)
            [self.textField2 resignFirstResponder];
            self.textField2.text = title;
        };
        datePicker;
    });
    
    self.monthPickerView = ({
        
        MTFUAccessoryPickerView *datePicker = [[MTFUAccessoryPickerView alloc] init];
        datePicker.backgroundColor = [UIColor whiteColor];
        self.textField3.inputView = datePicker;
        @weakify(self)
        datePicker.didCancelSelectBlock = ^( ) {
            @strongify(self)
            [self.textField3 resignFirstResponder];
        };
        datePicker.didFinishSelectedBlock = ^(NSString *title) {
            
            @strongify(self)
            [self.textField3 resignFirstResponder];
            self.textField3.text = title;
        };
        datePicker;
    });
#endif
}

@end
