//
//  PPUIKit2AlertViewController.m
//  ipaymentapp
//
//  Created by wangpengbo on 2018/11/29.
//  Copyright © 2018 Meituan.com. All rights reserved.
//

#import "PPUIKit2AlertViewController.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"
#import "MTFUSubmitButton.h"
#import "MTFUAlertView.h"
#import "SPKUIKitMacros.h"

static const CGFloat kAlertButtonMargin = 20;
static const CGFloat kAlertButtonHeight = 44;

@interface PPUIKit2AlertViewController ()

@property (nonatomic, strong) MTFUSubmitButton *DL3Button;
@property (nonatomic, strong) MTFUSubmitButton *DL4Button;
@property (nonatomic, strong) MTFUSubmitButton *DL5Button;
@property (nonatomic, strong) MTFUSubmitButton *DL6Button;
@property (nonatomic, strong) MTFUSubmitButton *DL8Button;

@end

@implementation PPUIKit2AlertViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.title = @"弹框测试";
    self.view.backgroundColor = [UIColor whiteColor];
    
    self.DL3Button = ({
        MTFUSubmitButton *button = [MTFUSubmitButton button];
        [button setTitle:@"1⃣ 内容 + 单按钮" forState:UIControlStateNormal];
        [[button rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
            [MTFUAlertView showAlertViewWithMessage:@"因国家法律规定，本内容居中" completionButtonTitle:@"知道了" completion:nil];
        }];
        
        button;
    });
    [self.view addSubview:self.DL3Button];
    
    self.DL4Button = ({
        MTFUSubmitButton *button = [MTFUSubmitButton button];
        [button setTitle:@"2⃣ 内容 + 双按钮" forState:UIControlStateNormal];
        [[button rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
            [MTFUAlertView showAlertViewWithTitle:nil
                                         message:@"林俊杰告诉我，文案过长需要折行展示（居中对齐）"
                               cancelButtonTitle:@"取消"
                           completionButtonTitle:@"确定"
                                        canceled:^{
                                        }
                                      completion:^{
                                          //
                                      }];
        }];
        button;
    });
    [self.view addSubview:self.DL4Button];
    
    self.DL5Button = ({
        MTFUSubmitButton *button = [MTFUSubmitButton button];
        [button setTitle:@"3⃣ 主内容 + 辅助内容 + 按钮" forState:UIControlStateNormal];
        [[button rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
            [MTFUAlertView showErrorAlertViewWithMessage:@"正文内容居中展示，如有错误码改变色值"
                                               errorCode:@"errCode: #999999"
                                       cancelButtonTitle:@"取消" completionButtonTitle:@"确定" canceled:nil completion:nil];
        }];
        
        button;
    });
    [self.view addSubview:self.DL5Button];
    
    self.DL6Button = ({
        MTFUSubmitButton *button = [MTFUSubmitButton button];
        
        [button setTitle:@"4⃣ 标题 + 单行内容 + 单按钮" forState:UIControlStateNormal];
        [[button rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
            [MTFUAlertView showAlertViewWithTitle:@"注销实名认证"
                                          message:@"干嘛注销，想皮一下么？"
                                cancelButtonTitle:nil
                            completionButtonTitle:@"引导按钮"
                                         canceled:^{
                                         }
                                       completion:^{
                                           //
                                       }];
        }];
        
        button;
    });
    [self.view addSubview:self.DL6Button];
    
    self.DL8Button = ({
        MTFUSubmitButton *button = [MTFUSubmitButton button];
        [button setTitle:@"5⃣ 标题 + 多行内容 + 双按钮" forState:UIControlStateNormal];
        [[button rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
            [MTFUAlertView showAlertViewWithTitle:@"注销实名认证"
                                          message:@"1. 美团钱包绑定的银行卡将被解绑\n2.本自然年内，还可注销 5 次"
                                cancelButtonTitle:@"确认注销"
                            completionButtonTitle:@"暂不注销"
                                         canceled:^{
                                         }
                                       completion:^{
                                           //
                                       }];
        }];
        
        button;
    });
    [self.view addSubview:self.DL8Button];
    
    [self setupConstraints];
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

- (void)setupConstraints {
    [self.DL3Button mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(80);
        make.left.equalTo(self.view).offset(kAlertButtonMargin);
        make.right.equalTo(self.view).offset(-kAlertButtonMargin);
        make.height.mas_equalTo(kAlertButtonHeight);
    }];
    
    [self.DL4Button mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.DL3Button.mas_bottom).offset(40);
        make.left.equalTo(self.view).offset(kAlertButtonMargin);
        make.right.equalTo(self.view).offset(-kAlertButtonMargin);
        make.height.mas_equalTo(kAlertButtonHeight);
    }];
    
    [self.DL5Button mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.DL4Button.mas_bottom).offset(40);
        make.left.equalTo(self.view).offset(kAlertButtonMargin);
        make.right.equalTo(self.view).offset(-kAlertButtonMargin);
        make.height.mas_equalTo(kAlertButtonHeight);
    }];
    
    [self.DL6Button mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.DL5Button.mas_bottom).offset(40);
        make.left.equalTo(self.view).offset(kAlertButtonMargin);
        make.right.equalTo(self.view).offset(-kAlertButtonMargin);
        make.height.mas_equalTo(kAlertButtonHeight);
    }];
    
    [self.DL8Button mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.DL6Button.mas_bottom).offset(40);
        make.left.equalTo(self.view).offset(kAlertButtonMargin);
        make.right.equalTo(self.view).offset(-kAlertButtonMargin);
        make.height.mas_equalTo(kAlertButtonHeight);
    }];
}

@end
