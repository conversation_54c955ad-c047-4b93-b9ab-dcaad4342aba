//
//  PPUIKit2TabSwitchViewController.m
//  ipaymentapp
//
//  Created by wa<PERSON><PERSON><PERSON><PERSON> on 2018/11/29.
//  Copyright © 2018 Meituan.com. All rights reserved.
//
#define MTFUScrollPageTabBarMacro

#import "PPUIKit2TabSwitchViewController.h"
#ifdef MTFUScrollPageTabBarMacro
#import "MTFUScrollPageTabBar.h"
#endif

@interface PPUIKit2TabSwitchViewController ()

@property(nonatomic, strong) NSArray *titles;
@property(nonatomic, weak) UIScrollView *scrollView;
#ifdef MTFUScrollPageTabBarMacro
@property(nonatomic, weak) MTFUScrollPageTabBar *scrollPageTabBar;
#endif
@end

@implementation PPUIKit2TabSwitchViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.title = @"标签栏切换";
    
#ifdef MTFUScrollPageTabBarMacro
    NSArray *titles = @[@"标",@"标题1",@"标题2",@"这个标题比较长3",@"标题4",@"标题5",@"标题6",@"标题7",@"标题8",@"标题9"];
    MTFUScrollPageTabBar *scrollPageTabBar = [[MTFUScrollPageTabBar alloc] initWithTitles:titles];
    
    [self.view addSubview:scrollPageTabBar];
    
    [scrollPageTabBar mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.view);
        make.top.equalTo(@10);
        make.height.equalTo(@44);
    }];
    
    UIScrollView *scrollPageView = [[UIScrollView alloc] initWithFrame:self.view.bounds];
    [self.view addSubview:scrollPageView];
    
    scrollPageTabBar.associatedScrollView = scrollPageView;
    
    [scrollPageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.view);
        make.top.equalTo(scrollPageTabBar.mas_bottom).offset(10);
        make.bottom.equalTo(self.view);
    }];
    scrollPageView.backgroundColor = [UIColor redColor];
    
    UIView *preView = nil;
    for ( NSString *title in titles) {
        UIView *view = [[UIView alloc] initWithFrame:self.view.bounds];
        
        [scrollPageView addSubview:view];
        
        [view mas_makeConstraints:^(MASConstraintMaker *make) {
            if ( preView) {
                
                make.left.equalTo(preView.mas_right);
            } else {
                make.left.equalTo(@0);
            }
            make.width.equalTo(self.view);
            make.top.equalTo(@0);
            make.height.equalTo(scrollPageView.mas_height);
        }];
        
        UILabel *resultLabel = [[UILabel alloc] init];
        
        resultLabel.text = title;
        [resultLabel setTextAlignment:NSTextAlignmentCenter];
        
        [view addSubview:resultLabel];
        
        [resultLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(view);
            make.centerY.equalTo(view.mas_centerY);
        }];
        view.backgroundColor = [UIColor yellowColor];
        preView = view;
    }
    
    scrollPageTabBar.didClickedTitleBlock = ^(NSUInteger index, NSString * _Nonnull title) {
        
        //[scrollPageView setContentOffset:CGPointMake(index*scrollPageView.frame.size.width, 0) animated:YES];
    };
    
    scrollPageView.pagingEnabled = YES;
    self.scrollView = scrollPageView;
    self.scrollPageTabBar = scrollPageTabBar;
    self.titles = titles;
    
    [self.scrollView setContentSize:CGSizeMake(self.scrollView.frame.size.width*self.titles.count, self.scrollView.frame.size.height)];
    self.scrollPageTabBar.defaultIndex = 1;
#endif
}

- (void)viewDidAppear:(BOOL)animated
{
    [super viewDidAppear:animated];
#ifdef MTFUScrollPageTabBarMacro
    [self.scrollView setContentSize:CGSizeMake(self.scrollView.frame.size.width*self.titles.count, self.scrollView.frame.size.height)];
#endif
}

@end
