//
//  PPUIKit2BannerViewController.m
//  ipaymentapp
//
//  Created by wangpeng<PERSON> on 2018/11/29.
//  Copyright © 2018 Meituan.com. All rights reserved.
//

#import "PPUIKit2BannerViewController.h"
#import "MTFUBannerPagingView.h"
#import "MTFUBannerUIObject.h"
#import <Masonry.h>

@interface PPUIKit2BannerViewController ()

@property (nonatomic, strong) MTFUBannerPagingView *bannerView;
@property (nonatomic, strong) MTFUBannerPagingView *bannerView2;

@end

@implementation PPUIKit2BannerViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.view.backgroundColor = [UIColor grayColor];
    
    self.bannerView = [MTFUBannerPagingView new];
    NSMutableArray *array = [NSMutableArray new];
    MTFUBannerUIObject *obj1 = [MTFUBannerUIObject new];
    obj1.image = [UIImage imageNamed:@"banner2"];
    obj1.linkURL = [NSURL URLWithString:@"https://www.baidu.com"];
    [array addObject:obj1];
    MTFUBannerUIObject *obj2 = [MTFUBannerUIObject new];
    obj2.image = [UIImage imageNamed:@"banner2"];
    obj2.linkURL = [NSURL URLWithString:@"https://www.meituan.com"];
    [array addObject:obj2];
    [array addObject:obj1];
    [array addObject:obj2];
    self.bannerView.banners = [array copy];
    [self.view addSubview:self.bannerView];
    self.bannerView.pageControlType = MTFUPageControlOverBanner;
    self.bannerView.didClickBannerBlock = ^(NSInteger index, MTFUBannerUIObject *banner) {
        NSLog(@"didClickBannerBlock: index:%ld, banner:%@", (long)index, banner);
    };
    self.bannerView.didShowBannerBlock = ^(NSInteger index, MTFUBannerUIObject *banner) {
        NSLog(@"didShowBannerBlock: index:%ld, banner:%@", index, banner);
    };
    
    {
        self.bannerView2 = [MTFUBannerPagingView new];
        NSMutableArray *array = [NSMutableArray new];
        MTFUBannerUIObject *obj1 = [MTFUBannerUIObject new];
        obj1.image = [UIImage imageNamed:@"banner2"];
        obj1.linkURL = [NSURL URLWithString:@"https://www.baidu.com"];
        [array addObject:obj1];
        MTFUBannerUIObject *obj2 = [MTFUBannerUIObject new];
        obj2.image = [UIImage imageNamed:@"banner2"];
        obj2.linkURL = [NSURL URLWithString:@"https://www.meituan.com"];
        [array addObject:obj2];
        [array addObject:obj1];
        [array addObject:obj2];
        self.bannerView2.banners = [array copy];
        [self.view addSubview:self.bannerView2];
        self.bannerView2.pageControlType = MTFUPageControlBelowBanner;
        
        self.bannerView2.didClickBannerBlock = ^(NSInteger index, MTFUBannerUIObject *banner) {
            NSLog(@"didClickBannerBlock: index:%ld, banner:%@", (long)index, banner);
        };
        self.bannerView2.didShowBannerBlock = ^(NSInteger index, MTFUBannerUIObject *banner) {
            NSLog(@"didShowBannerBlock: index:%ld, banner:%@", index, banner);
        };
    }
    
    [self.view setNeedsUpdateConstraints];
}

- (void)dealloc
{
    [self.bannerView clear];
    [self.bannerView2 clear];
}

- (void)updateViewConstraints
{
    [self.bannerView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(100);
        make.left.right.equalTo(self.view);
        make.height.equalTo(@(74));
    }];
    
    [self.bannerView2 mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(300);
        make.left.right.equalTo(self.view);
        make.height.equalTo(@(74));
    }];
    
    [super updateViewConstraints];
}

@end
