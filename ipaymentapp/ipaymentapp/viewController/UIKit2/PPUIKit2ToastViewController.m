//
//  PPUIKit2ToastViewController.m
//  ipaymentapp
//
//  Created by wangpengbo on 2018/11/29.
//  Copyright © 2018 Meituan.com. All rights reserved.
//

#import "PPUIKit2ToastViewController.h"
#import "Masonry.h"
#import "ReactiveCocoa.h"
#import "SPKUIKitMacros.h"
#import "MTFUToastCenter.h"
#import "UIImage+MTFU.h"
#import "MTFUSubmitButton.h"

static const CGFloat kToastButtonMargin = 40;
static const CGFloat kToastButtonHeight = 44;

@interface PPUIKit2ToastViewController ()

@property (nonatomic, strong) MTFUSubmitButton *btn1;
@property (nonatomic, strong) MTFUSubmitButton *btn2;
@property (nonatomic, strong) MTFUSubmitButton *btn3;
@property (nonatomic, strong) MTFUSubmitButton *btn4;
@property (nonatomic, strong) MTFUSubmitButton *btn5;
@property (nonatomic, strong) MTFUSubmitButton *btn6;
@property (nonatomic, strong) MTFUSubmitButton *btn7;

@end

@implementation PPUIKit2ToastViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.title = @"Toast 提示";
    
    self.btn1 = ({
        MTFUSubmitButton *btn1 = [MTFUSubmitButton button];
        [btn1 setTitle:@"文字提示 - 短" forState:UIControlStateNormal];
        btn1.backgroundColor = [UIColor redColor];
        [[btn1 rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
            dispatch_async(dispatch_get_main_queue(), ^{
                [[MTFUToastCenter defaultCenter] toastWithMessage:@"信息错误"];
            });
        }];
        btn1;
    });
    [self.view addSubview:self.btn1];
    
    self.btn2 = ({
        MTFUSubmitButton *btn2 = [MTFUSubmitButton button];
        [btn2 setTitle:@"文字提示 - 长" forState:UIControlStateNormal];
        btn2.backgroundColor = [UIColor orangeColor];
        [[btn2 rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
            dispatch_async(dispatch_get_main_queue(), ^{
                [[MTFUToastCenter defaultCenter] toastWithMessage:@"信息输入错误信息输入错误信息输入错误信息输入错误"];
            });
        }];
        btn2;
    });
    [self.view addSubview:self.btn2];
    
    self.btn3 = ({
        MTFUSubmitButton *btn3 = [MTFUSubmitButton button];
        btn3.backgroundColor = [UIColor brownColor];
        [btn3 setTitle:@"错误码提示" forState:UIControlStateNormal];
        [[btn3 rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
            dispatch_async(dispatch_get_main_queue(), ^{
                NSError *error = [[NSError alloc] initWithDomain:@"test" code:1234567 userInfo:nil];
                [[MTFUToastCenter defaultCenter] toastWithError:error weakenErrorCode:YES];
            });
        }];
        btn3;
    });
    [self.view addSubview:self.btn3];
    
    self.btn4 = ({
        MTFUSubmitButton *btn = [MTFUSubmitButton button];
        btn.backgroundColor = [UIColor brownColor];
        [btn setTitle:@"icon + 单行提示" forState:UIControlStateNormal];
        [[btn rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
            dispatch_async(dispatch_get_main_queue(), ^{
                [[MTFUToastCenter defaultCenter] toastWithMessage:@"提示文案" image:[UIImage mtfu_imageNamed:@"mtfu_icon_success_toast.png"]];
            });
        }];
        btn;
    });
    [self.view addSubview:self.btn4];
    
    self.btn5 = ({
        MTFUSubmitButton *btn = [MTFUSubmitButton button];
        btn.backgroundColor = [UIColor brownColor];
        [btn setTitle:@"icon + 多行提示" forState:UIControlStateNormal];
        [[btn rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
            dispatch_async(dispatch_get_main_queue(), ^{
                [[MTFUToastCenter defaultCenter] toastSuccessWithMessage:@"已开启\n微信免密支付"];
            });
        }];
        btn;
    });
    [self.view addSubview:self.btn5];
    
    self.btn6 = ({
        MTFUSubmitButton *btn = [MTFUSubmitButton button];
        btn.backgroundColor = [UIColor brownColor];
        [btn setTitle:@"加载动画 + 普通提示" forState:UIControlStateNormal];
        [[btn rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
            dispatch_async(dispatch_get_main_queue(), ^{
                [[MTFUToastCenter defaultCenter] toastWithLoadingAndMessage:@"加载中..."];
                
            });
        }];
        btn;
    });
    [self.view addSubview:self.btn6];
    
    self.btn7 = ({
        MTFUSubmitButton *btn = [MTFUSubmitButton button];
        btn.backgroundColor = [UIColor brownColor];
        [btn setTitle:@"加载动画 + 长提示" forState:UIControlStateNormal];
        [[btn rac_signalForControlEvents:UIControlEventTouchUpInside] subscribeNext:^(id x) {
            dispatch_async(dispatch_get_main_queue(), ^{
                [[MTFUToastCenter defaultCenter] toastSuccessWithMessage:@"你可以在“我的-卡券包”查看"];
            });
        }];
        btn;
    });
    [self.view addSubview:self.btn7];
    
    
    [self.view setNeedsUpdateConstraints];
}

- (void)updateViewConstraints {
    [self.btn1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(80);
        make.left.equalTo(self.view).offset(kToastButtonMargin);
        make.right.equalTo(self.view).offset(-kToastButtonMargin);
        make.height.mas_equalTo(kToastButtonHeight);
    }];
    
    [self.btn2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.btn1.mas_bottom).offset(40);
        make.left.equalTo(self.view).offset(kToastButtonMargin);
        make.right.equalTo(self.view).offset(-kToastButtonMargin);
        make.height.mas_equalTo(kToastButtonHeight);
    }];
    
    [self.btn3 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.btn2.mas_bottom).offset(40);
        make.left.equalTo(self.view).offset(kToastButtonMargin);
        make.right.equalTo(self.view).offset(-kToastButtonMargin);
        make.height.mas_equalTo(kToastButtonHeight);
    }];
    
    [self.btn4 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.btn3.mas_bottom).offset(40);
        make.left.equalTo(self.view).offset(kToastButtonMargin);
        make.right.equalTo(self.view).offset(-kToastButtonMargin);
        make.height.mas_equalTo(kToastButtonHeight);
    }];
    
    [self.btn5 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.btn4.mas_bottom).offset(40);
        make.left.equalTo(self.view).offset(kToastButtonMargin);
        make.right.equalTo(self.view).offset(-kToastButtonMargin);
        make.height.mas_equalTo(kToastButtonHeight);
    }];
    
    [self.btn6 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.btn5.mas_bottom).offset(40);
        make.left.equalTo(self.view).offset(kToastButtonMargin);
        make.right.equalTo(self.view).offset(-kToastButtonMargin);
        make.height.mas_equalTo(kToastButtonHeight);
    }];
    
    [super updateViewConstraints];
}

@end
