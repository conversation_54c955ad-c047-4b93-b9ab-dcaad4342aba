//
//  PPUIKitActionSheetViewController.m
//  ipaymentapp
//
//  Created by wangpeng<PERSON> on 2018/11/29.
//  Copyright © 2018 Meituan.com. All rights reserved.
//

#import "PPUIKitActionSheetViewController.h"
#import "MTFUCustomActionSheet.h"
#import "MTFUSubmitButton.h"

static const CGFloat kButtonMargin = 40;
static const CGFloat kButtonHeight = 44;

@interface PPUIKitActionSheetViewController ()

@property (nonatomic, strong) MTFUSubmitButton *btn1;
@property (nonatomic, strong) MTFUSubmitButton *btn2;

@end

@implementation PPUIKitActionSheetViewController

- (void)viewDidLoad {
    [super viewDidLoad];

    self.btn1 = ({
        MTFUSubmitButton *btn = [MTFUSubmitButton button];
        [btn setTitle:@"ActionSheet" forState:UIControlStateNormal];
        btn;
    });
    [self.view addSubview:self.btn1];

    [self.btn1 addTarget:self action:@selector(clickButton1) forControlEvents:UIControlEventTouchUpInside];

    self.btn2 = ({
        MTFUSubmitButton *btn = [MTFUSubmitButton button];
        [btn setTitle:@"ActionSheet_Title" forState:UIControlStateNormal];
        btn;
    });
    [self.view addSubview:self.btn2];

    [self.btn2 addTarget:self action:@selector(clickButton2) forControlEvents:UIControlEventTouchUpInside];
    [self.view setNeedsUpdateConstraints];
}

- (void)clickButton1
{
    MTFUCustomActionSheet *sheet = [[MTFUCustomActionSheet alloc] initWithActionButtonTitles:@[@"Title1", @"Title2", @"Title3"]];

    sheet.didSelectedItemIndexBlock = ^(NSInteger buttonIndex) {

    };
    [sheet show];
}

- (void)clickButton2
{
    MTFUCustomActionSheet *sheet = [[MTFUCustomActionSheet alloc] initWithActionTitle:@"哈哈哈哈哈" buttonTitles:@[@"Title1", @"Title2", @"Title3"]];

    sheet.didSelectedItemIndexBlock = ^(NSInteger buttonIndex) {

    };
    [sheet show];
}

- (void)updateViewConstraints
{
    [self.btn1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(20);
        make.left.equalTo(self.view).offset(kButtonMargin);
        make.right.equalTo(self.view).offset(-kButtonMargin);
        make.height.mas_equalTo(kButtonHeight);
    }];

    [self.btn2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.btn1.mas_bottom).offset(20);
        make.left.equalTo(self.view).offset(kButtonMargin);
        make.right.equalTo(self.view).offset(-kButtonMargin);
        make.height.mas_equalTo(kButtonHeight);
    }];

    [super updateViewConstraints];
}

@end
