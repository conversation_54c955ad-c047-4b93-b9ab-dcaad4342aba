//
//  PPBarcodeCashierHasParametersViewController.m
//  ipaymentapp
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/12/24.
//  Copyright © 2018 Meituan.com. All rights reserved.
//

#import "PPBarcodeCashierHasParametersViewController.h"
#import "UIImage+SAKColor.h"
#import "SAKPortal.h"

@interface PPBarcodeCashierHasParametersViewController ()

@property (nonatomic, strong) UITextField *parametersField;
@property (nonatomic, strong) UIButton *beginButton;

@end

@implementation PPBarcodeCashierHasParametersViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.title = @"付款码scheme带参数";
    self.view.backgroundColor = [UIColor whiteColor];
    
    [self setupUI];
}

- (void)setupUI
{
    [self.view addSubview:self.parametersField];
    [self.view addSubview:self.beginButton];

    [self.parametersField mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(SCREEN_WIDTH - 100, 35));
        make.top.equalTo(self.view).offset(20);
        make.centerX.equalTo(self.view);
    }];
    
    [self.beginButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.equalTo(self.parametersField);
        make.centerX.equalTo(self.parametersField);
        make.top.equalTo(self.view).offset(220);
    }];
}

- (void)jumpToBarcodeCashierVC
{
    NSString *url = @"meituanpayment://barcodecashier/launch";
    if (self.parametersField.text.length > 0) {
        // 例子：meituanpayment://barcodecashier/launch?sellerId=11000017816558&extraInfo=%7b%22a%22%3a%22aaa%22%7d
        url = [url stringByAppendingString:[NSString stringWithFormat:@"?%@", self.parametersField.text]];
    } else {
        // do nothing
    }
    NSURL *resultURL = [NSURL URLWithString:url];
    [SAKPortal transferFromViewController:nil toURL:resultURL completion:nil];
}

#pragma mark - Properties
- (UITextField *)parametersField
{
    if (!_parametersField) {
        _parametersField = [[UITextField alloc] init];
        _parametersField.placeholder = @"输入参数，比如 key=value";
        _parametersField.layer.borderColor = [HEXCOLOR(0x31BCAD) CGColor];
        _parametersField.layer.borderWidth = 1;
        _parametersField.textAlignment = NSTextAlignmentCenter;
    }
    return _parametersField;
}

- (UIButton *)beginButton
{
    if (!_beginButton) {
        _beginButton = [UIButton new];
        [_beginButton setTitle:@"开始" forState:UIControlStateNormal];
        [_beginButton setBackgroundImage:[UIImage imageWithColor:HEXCOLOR(0x31BCAD)] forState:UIControlStateNormal];
        [_beginButton addTarget:self action:@selector(jumpToBarcodeCashierVC)
               forControlEvents:UIControlEventTouchUpInside];
    }
    return _beginButton;
}

@end
