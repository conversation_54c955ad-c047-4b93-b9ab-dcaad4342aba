//
//  PPFingerPrintPayViewController.m
//  ipaymentapp
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2018/2/22.
//  Copyright © 2018年 Meituan.com. All rights reserved.
//

#import "PPFingerPrintPayViewController.h"
#import "SAKCellSectionBackgroundView.h"
#import "PPContentTextFieldCell.h"
#import "UIButton+Custom.h"
#import "SAKUIKitMacros.h"
#import "View+MASAdditions.h"
#import <libextobjc/extobjc.h>
#import "CIPStringAdditions.h"
#import "PPContentInfo.h"
#import "PPService.h"
#import "SAKPortal.h"

@interface PPFingerPrintPayViewController () <UITableViewDelegate, UITableViewDataSource, UIScrollViewDelegate>

@property (nonatomic, strong) UIScrollView *submitOrderView;
@property (nonatomic, strong) UITableView *contentInfoTableView;
@property (nonatomic, strong) UIButton *verfifyButton;
@property (nonatomic, strong) UILabel *payTokenLabel;
@property (nonatomic, strong) NSMutableArray <PPContentInfo *> *contentArray;

@end

@implementation PPFingerPrintPayViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.title = @"独立指纹支付模块";
    self.view.backgroundColor = HEXCOLOR(0xf0efed);
    
    self.submitOrderView = [[UIScrollView alloc] init];
    self.submitOrderView.alwaysBounceVertical = YES;
    self.submitOrderView.delegate = self;
    [self.view addSubview:self.submitOrderView];
    [self activateAutoScrollingForView:self.submitOrderView];
#pragma deploymate push "ignored-api-availability"
    if (@available(iOS 11.0, *)) {
        self.submitOrderView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    }
#pragma deploymate pop
    self.contentArray = [[PPService defaultInstance] getVerifyPayPasswordContentArray];
    [self.contentArray enumerateObjectsUsingBlock:^(id obj, NSUInteger idx, BOOL *stop) {
        PPContentInfo *contentInfo = obj;
        if (![contentInfo.value length]) {
            contentInfo.value = contentInfo.defaultValue;
        }
    }];
    
    self.contentInfoTableView = ({
        UITableView *tableView = [[UITableView alloc] init];
        tableView.backgroundColor = [UIColor clearColor];
        tableView.dataSource = self;
        tableView.delegate = self;
        tableView.scrollEnabled = NO;
        tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        tableView.backgroundView = [[SAKCellSectionBackgroundView alloc] init];
        tableView;
    });
    [self.submitOrderView addSubview:self.contentInfoTableView];
    
    self.verfifyButton = ({
        UIButton *button = [UIButton orangeCustomButton];
        [button setTitle:@"开始指纹支付" forState:UIControlStateNormal];
        [button addTarget:self action:@selector(didClickVerifyPageButton:) forControlEvents:UIControlEventTouchUpInside];
        button.enabled = YES;
        button;
    });
    [self.submitOrderView addSubview:self.verfifyButton];
    
    self.payTokenLabel = ({
        UILabel *label = [[UILabel alloc] init];
        label.textColor = [UIColor redColor];
        label.backgroundColor = [UIColor grayColor];
        label.textAlignment = NSTextAlignmentCenter;
        label.font = Font(18);
        label;
    });
    [self.submitOrderView addSubview:self.payTokenLabel];
    
    [self.view setNeedsUpdateConstraints];
}

- (void)viewDidAppear:(BOOL)animated
{
    [super viewDidAppear:animated];
    
    /// 进入页面滑动至底部
    CGPoint bottomOffset = CGPointMake(0, [self.submitOrderView contentSize].height - self.submitOrderView.frame. size.height);
    if (bottomOffset.y> 0 ) [self.submitOrderView setContentOffset: bottomOffset animated: NO];
}

- (void)updateViewConstraints
{
    [self.submitOrderView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
        make.size.equalTo(self.view);
        make.top.left.width.height.equalTo(self.view);
    }];
    
    [self.contentInfoTableView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.submitOrderView);
        make.left.equalTo(self.submitOrderView);
        make.width.equalTo(self.view);
        make.right.equalTo(self.submitOrderView.mas_right);
        make.height.equalTo(@(40 * [self.contentArray count]));
    }];
    
    [self.verfifyButton mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentInfoTableView.mas_bottom).offset(10);
        make.left.equalTo(self.submitOrderView).offset(12);
        make.right.equalTo(self.submitOrderView.mas_right).offset(-12);
        make.height.equalTo(@40);
    }];
    
    [self.payTokenLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.verfifyButton.mas_bottom).offset(20);
        make.centerX.equalTo(self.submitOrderView);
        make.height.mas_equalTo(40);
    }];
    
    [super updateViewConstraints];
}

#pragma mark - event

- (void)didClickVerifyPageButton:(id)sender
{
    NSString *urlString = @"meituanpayment://auth/verifyfingerprint";
    for (PPContentInfo *contentInfo in self.contentArray) {
        if ([contentInfo.value length] && [contentInfo.identifier length]) {
            NSString *parameterString = [NSString stringWithFormat:@"%@=%@", contentInfo.identifier, contentInfo.value];
            urlString = [urlString cipf_stringByAppendingURLParameters:parameterString];
        }
    }
    
    NSString *callback = @"callback_url=meituanpayment://pptest/fingerprintpay";
    urlString = [urlString cipf_stringByAppendingURLParameters:callback];
    [SAKPortal transferFromViewController:self
                                    toURL:[NSURL URLWithString:urlString]
                               completion:nil];
}

- (void)updateOutNoCell
{
    [self.contentArray enumerateObjectsUsingBlock:^(id obj, NSUInteger idx, BOOL *stop) {
        PPContentInfo *contentInfo = (PPContentInfo*)obj;
        if ([contentInfo.identifier isEqualToString:@"out_no"]) {
            NSIndexPath *indexPath = [NSIndexPath indexPathForRow:idx inSection:0];
            [self.contentInfoTableView  reloadRowsAtIndexPaths:[NSArray arrayWithObjects:indexPath, nil]
                                              withRowAnimation:UITableViewRowAnimationNone];
            *stop = YES;
        }
    }];
}

#pragma mark - UIScrollViewDelegate

- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView
{
    [self.view endEditing:YES];
}

#pragma mark - UITableViewDataSource

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return [self.contentArray count];
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    PPContentInfo *contentInfo = self.contentArray[indexPath.row];
    PPContentTextFieldCell *cell;
    
    if ([contentInfo.identifier isEqualToString:@"out_no"]) {
        NSInteger timeStamp = [[NSDate date] timeIntervalSince1970];
        contentInfo.value = [NSString stringWithFormat:@"%ld", (long)timeStamp];
    }
    cell = [PPContentTextFieldCell instanceOfContentInfo:contentInfo];
    
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return 40;
}

@end
