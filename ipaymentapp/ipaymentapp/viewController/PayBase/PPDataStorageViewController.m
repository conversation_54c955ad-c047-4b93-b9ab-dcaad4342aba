//
//  PPDataStorageViewController.m
//  ipaymentapp
//
//  Created by wangtao37 on 2017/2/22.
//  Copyright © 2017年 Meituan.com. All rights reserved.
//

#import "PPDataStorageViewController.h"
#import "SPGDataStorageManager.h"
#import "NSString+SPGPersistDataAESCrypt.h"
#import "SPKToastCenter.h"

@interface PPDataStorageViewController ()

@property (nonatomic, strong) UIView *contentView;
@property (nonatomic, strong) UILabel *locationLabel;
@property (nonatomic, strong) UISegmentedControl *locationControl;
@property (nonatomic, strong) UILabel *needEncryptLabel;
@property (nonatomic, strong) UISegmentedControl *needEncryptControl;

@property (nonatomic, strong) UITextView *sourceTextView;
@property (nonatomic, strong) UIButton *saveButton;
@property (nonatomic, strong) UITextView *encryptedTextView;
@property (nonatomic, strong) UIButton *readButton;
@property (nonatomic, strong) UIButton *deleteButton;

@property (nonatomic, strong) NSString *userDefaultsKey;
@property (nonatomic, strong) NSString *fileKey;
@property (nonatomic, strong) NSString *keychainKey;

@end

@implementation PPDataStorageViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.userDefaultsKey = @"USERDEFAULT";
    self.fileKey = @"FILE";
    self.keychainKey = @"KEYCHAIN";
    
    [self setupUI];
    [self.view setNeedsUpdateConstraints];
}

- (void)setupUI
{
    self.view.backgroundColor = [UIColor whiteColor];
    self.title = @"基础数据加密";
    
    UITapGestureRecognizer *recognizer = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(handleTapGesture)];
    [self.view addGestureRecognizer:recognizer];
    
    self.contentView = [[UIView alloc] init];
    [self.view addSubview:self.contentView];
    
    self.locationLabel = ({
        UILabel *label = [[UILabel alloc] init];
        label.text = @"请选择存储位置";
        label.textAlignment = NSTextAlignmentCenter;
        label.font = [UIFont systemFontOfSize:14];
        label;
    });
    [self.contentView addSubview:self.locationLabel];
    
    self.locationControl = ({
        UISegmentedControl *control = [[UISegmentedControl alloc] initWithItems:@[@"KeyChain", @"UserDefaults", @"File"]];
        control.selectedSegmentIndex = 0;
        control.tintColor = HEXCOLOR(0xBDA45A);
        control;
    });
    [self.contentView addSubview:self.locationControl];
    
    self.needEncryptLabel = ({
        UILabel *label = [[UILabel alloc] init];
        label.text = @"请选择是否加密存储";
        label.textAlignment = NSTextAlignmentCenter;
        label.font = [UIFont systemFontOfSize:14];
        label;
    });
    [self.contentView addSubview:self.needEncryptLabel];
    
    self.needEncryptControl = ({
        UISegmentedControl *control = [[UISegmentedControl alloc] initWithItems:@[@"YES", @"NO"]];
        control.selectedSegmentIndex = 0;
        control.tintColor = HEXCOLOR(0xBDA45A);
        control;
    });
    [self.contentView addSubview:self.needEncryptControl];
    
    self.sourceTextView = ({
        UITextView *view = [[UITextView alloc] init];
        view.backgroundColor = RGBCOLOR(240, 242, 245);
        view;
        
    });
    [self.contentView addSubview:self.sourceTextView];
    
    self.saveButton = ({
        UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
        button.backgroundColor = [UIColor clearColor];
        button.titleLabel.font = Font(14);
        [button setTitle:@"保存" forState:UIControlStateNormal];
        [button setTitleColor:HEXCOLOR(0xBDA45A) forState:UIControlStateNormal];
        [button addTarget:self action:@selector(didClickedSaveButton) forControlEvents:UIControlEventTouchUpInside];
        button;
    });
    [self.contentView addSubview:self.saveButton];
    
    self.encryptedTextView = ({
        UITextView *view = [[UITextView alloc] init];
        view.editable = NO;
        view.backgroundColor = RGBCOLOR(240, 242, 245);
        view;
    });
    [self.contentView addSubview:self.encryptedTextView];
    
    self.readButton = ({
        UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
        button.backgroundColor = [UIColor clearColor];
        button.titleLabel.font = Font(14);
        [button setTitle:@"读取" forState:UIControlStateNormal];
        [button setTitleColor:HEXCOLOR(0xBDA45A) forState:UIControlStateNormal];
        [button addTarget:self action:@selector(didClickedReadButton) forControlEvents:UIControlEventTouchUpInside];
        button;
    });
    [self.contentView addSubview:self.readButton];
    
    self.deleteButton = ({
        UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
        button.backgroundColor = [UIColor clearColor];
        button.titleLabel.font = Font(14);
        [button setTitle:@"删除" forState:UIControlStateNormal];
        [button setTitleColor:HEXCOLOR(0xBDA45A) forState:UIControlStateNormal];
        [button addTarget:self action:@selector(didClickedDeleteButton) forControlEvents:UIControlEventTouchUpInside];
        button;
    });
    [self.contentView addSubview:self.deleteButton];
    
}

- (void)handleTapGesture
{
    [self.view endEditing:YES];
}

- (void)didClickedSaveButton
{
    SPGDataStorageLocation location;
    NSString *key;
    if (self.locationControl.selectedSegmentIndex == 0) {
        location = SPGDataStorageLocationKeyChain;
        key = self.keychainKey;
    } else if (self.locationControl.selectedSegmentIndex == 1) {
        location = SPGDataStorageLocationUserDefaults;
        key = self.userDefaultsKey;
    } else if (self.locationControl.selectedSegmentIndex == 2) {
        location = SPGDataStorageLocationFile;
        key = self.fileKey;
    } else {
        location = SPGDataStorageLocationUserDefaults;
        key = self.userDefaultsKey;
    }
    
    BOOL needEncrypt = (self.needEncryptControl.selectedSegmentIndex == 0) ? YES : NO;
    NSString *sourceTextString = self.sourceTextView.text;
    
    if ([sourceTextString length]) {
        
        if (needEncrypt) {
            NSString *enctyptedString = [sourceTextString spg_persistDataAESEncrypted];
            self.encryptedTextView.text = enctyptedString;
        } else {
            self.encryptedTextView.text = sourceTextString;
        }
        
        CIPError *error;
        
        [[SPGDataStorageManager sharedStorageManager] saveStringAssociatedUserID:sourceTextString
                                                                          forKey:key
                                                                 storageLocation:location
                                                                   shouldEncrypt:needEncrypt
                                                                  updateExisting:YES
                                                                           error:&error];
        
        if (!error) {
            [[SPKToastCenter defaultCenter] toastWithMessage:@"存储成功"];
            return;
        }
        [[SPKToastCenter defaultCenter] toastWithMessage:@"存储失败"];
    } else {
        [[SPKToastCenter defaultCenter] toastWithMessage:@"请输入要存储的内容"];
    }
}

- (void)didClickedReadButton
{
    SPGDataStorageLocation location;
    NSString *key;
    if (self.locationControl.selectedSegmentIndex == 0) {
        location = SPGDataStorageLocationKeyChain;
        key = self.keychainKey;
    } else if (self.locationControl.selectedSegmentIndex == 1) {
        location = SPGDataStorageLocationUserDefaults;
        key = self.userDefaultsKey;
    } else if (self.locationControl.selectedSegmentIndex == 2) {
        location = SPGDataStorageLocationFile;
        key = self.fileKey;
    } else {
        location = SPGDataStorageLocationUserDefaults;
        key = self.userDefaultsKey;
    }
    
    BOOL needDecrypt = (self.needEncryptControl.selectedSegmentIndex == 0) ? YES : NO;
    NSString *sourceTextString = self.encryptedTextView.text;
    
    if ([sourceTextString length]) {
        CIPError *error;
        
        NSString *decrtyptedString = [[SPGDataStorageManager sharedStorageManager] stringAssociatedUserIDForKey:key storageLocation:location shouldDecrypt:needDecrypt error:&error];
        self.sourceTextView.text = decrtyptedString;
        
        if (!error) {
            [[SPKToastCenter defaultCenter] toastWithMessage:[NSString stringWithFormat:@"读取成功【%@】", decrtyptedString]];
            return;
        }
        [[SPKToastCenter defaultCenter] toastWithMessage:@"读取失败"];
    } else {
        [[SPKToastCenter defaultCenter] toastWithMessage:@"请输入要读取的内容"];
    }
}

- (void)didClickedDeleteButton
{
    SPGDataStorageLocation location;
    NSString *key;
    if (self.locationControl.selectedSegmentIndex == 0) {
        location = SPGDataStorageLocationKeyChain;
        key = self.keychainKey;
    } else if (self.locationControl.selectedSegmentIndex == 1) {
        location = SPGDataStorageLocationUserDefaults;
        key = self.userDefaultsKey;
    } else if (self.locationControl.selectedSegmentIndex == 2) {
        location = SPGDataStorageLocationFile;
        key = self.fileKey;
    } else {
        location = SPGDataStorageLocationUserDefaults;
        key = self.userDefaultsKey;
    }
    
    NSString *sourceTextString = self.encryptedTextView.text;
    
    if ([sourceTextString length]) {
        
        
        BOOL result = [[SPGDataStorageManager sharedStorageManager] deleteStringAssociatedUserIDForKey:key storageLocation:location];
        if (result) {
            [[SPKToastCenter defaultCenter] toastWithMessage:@"删除成功"];
            return;
        }
        [[SPKToastCenter defaultCenter] toastWithMessage:@"删除失败"];
    } else {
        [[SPKToastCenter defaultCenter] toastWithMessage:@"请输入要删除的内容"];
    }
    
}

- (void)updateViewConstraints
{
    [self.contentView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
        make.width.mas_equalTo(SCREEN_WIDTH);
        make.height.mas_equalTo(SCREEN_HEIGHT);
    }];
    
    [self.locationLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView.mas_top).offset(10);
        make.left.equalTo(self.contentView.mas_left);
        make.right.equalTo(self.contentView.mas_right);
        make.height.equalTo(@25);
    }];
    
    [self.locationControl mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.locationLabel.mas_bottom).offset(15);
        make.left.equalTo(self.contentView.mas_left).offset(50);
        make.right.equalTo(self.contentView.mas_right).offset(-50);
        make.height.equalTo(@30);
    }];
    
    [self.needEncryptLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.locationControl.mas_bottom).offset(15);
        make.left.equalTo(self.contentView.mas_left);
        make.right.equalTo(self.contentView.mas_right);
        make.height.equalTo(@25);
    }];
    
    [self.needEncryptControl mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.needEncryptLabel.mas_bottom).offset(15);
        make.left.equalTo(self.contentView.mas_left).offset(50);
        make.right.equalTo(self.contentView.mas_right).offset(-50);
        make.height.equalTo(@30);
    }];
    
    [self.sourceTextView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.needEncryptControl.mas_bottom).offset(15);
        make.left.equalTo(self.contentView.mas_left).offset(30);
        make.right.equalTo(self.contentView.mas_right).offset(-30);
        make.height.equalTo(@80);
    }];
    
    [self.saveButton mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.sourceTextView.mas_bottom).offset(15);
        make.left.equalTo(self.contentView.mas_left).offset(30);
        make.width.mas_equalTo((SCREEN_WIDTH - 120) / 3);
        make.height.equalTo(@40);
    }];
    
    [self.readButton mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.sourceTextView.mas_bottom).offset(15);
        make.centerX.equalTo(self.contentView);
        make.width.mas_equalTo((SCREEN_WIDTH - 120) / 3);
        make.height.equalTo(@40);
    }];
    
    [self.deleteButton mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.sourceTextView.mas_bottom).offset(15);
        make.right.equalTo(self.contentView.mas_right).offset(-30);
        make.width.mas_equalTo((SCREEN_WIDTH - 120) / 3);
        make.height.equalTo(@40);
    }];
    
    [self.encryptedTextView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.saveButton.mas_bottom).offset(10);
        make.left.equalTo(self.contentView.mas_left).offset(30);
        make.right.equalTo(self.contentView.mas_right).offset(-30);
        make.height.equalTo(@140);
    }];
    
    [super updateViewConstraints];
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

@end
