//
//  PPPaymentKitViewController.m
//  ipaymentapp
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2017/11/21.
//  Copyright © 2017年 Meituan.com. All rights reserved.
//

#import "PPPaymentKitViewController.h"
#import "Masonry.h"
#import "PPConfigTableViewCell.h"
#import "PPAppSelectViewController.h"
#import "PPDataStorageViewController.h"
#import "PPRegisterAPPMock.h"
#import "SAKPortal.h"
#import "VANStateManager.h"
#import "NVSharkDebugPanel.h"
#import "SAKMemoryLeakMonitor.h"
#import "PPNFCManagementViewController.h"
#import "SPKFoundationMacros.h"
#import "SPKToastCenter.h"
#import "PPUIKitViewController.h"
#import "SPKAlertView.h"
#import "PPVerifyPasswordViewController.h"
#import "SAKUserService.h"
#import "PPAccountController.h"
#import "PPScanCardViewController.h"
#import "PPFinacialUtilityViewController.h"
#import "PPFingerPrintPayViewController.h"
#import "UIColor+Addition.h"

static const CGFloat kPPPayKitCellHeight = 50;

@interface PPPaymentKitViewController () <UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UITableView *tableview;
@property (nonatomic, strong) NSArray *configArray;

@end

@implementation PPPaymentKitViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self.view setBackgroundColor:[UIColor generalBackgroundColor]];
    self.title = @"设置";
    
    self.configArray = @[@"基础数据加密",@"NFC 测试", @"验证密码服务", @"拍照识别卡号服务", @"指纹支付服务", @"金融专项工具集"];
    
    self.tableview = ({
        UITableView *tableview = [[UITableView alloc] init];
        tableview.backgroundColor = [UIColor clearColor];
        tableview.delegate = self;
        tableview.dataSource = self;
        [self.view addSubview:tableview];
        tableview;
    });
#pragma deploymate push "ignored-api-availability"
    if (@available(iOS 11.0, *)) {
        self.tableview.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    }
#pragma deploymate pop
    [self.tableview registerClass:[PPConfigTableViewCell class] forCellReuseIdentifier:NSStringFromClass([PPConfigTableViewCell class])];
    
    [self.view setNeedsUpdateConstraints];
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

- (void)updateViewConstraints
{
    [self.tableview mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view);
        make.left.equalTo(self.view);
        make.right.equalTo(self.view);
        make.bottom.equalTo(self.view);
    }];
    
    [super updateViewConstraints];
}

#pragma mark - UITableViewDataSource

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return [self.configArray count];
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    PPConfigTableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:NSStringFromClass([PPConfigTableViewCell class])];
    if (!cell) {
        cell = [[PPConfigTableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:NSStringFromClass([PPConfigTableViewCell class])];
    }
    cell.titleLabel.text = self.configArray[indexPath.row];
    cell.switchShow = NO;
    cell.accessoryType = UITableViewCellAccessoryDisclosureIndicator;
    
    return cell;
}

#pragma mark - UITableViewDelegate

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    return kPPPayKitCellHeight;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    if (indexPath.row == 0) {
        // @"基础数据加密"
        PPDataStorageViewController *dataStorageVC = [[PPDataStorageViewController alloc] init];
        [self.navigationController pushViewController:dataStorageVC animated:YES];
    } else if (indexPath.row == 1) {
        // NFC 测试
        if (SPK_SYSTEM_VERSION_GREATER_THAN_OR_EQUAL_TO(@"11.0")) {
            PPNFCManagementViewController *NFCManagementVC = [[PPNFCManagementViewController alloc] init];
            [self.navigationController pushViewController:NFCManagementVC animated:YES];
        } else {
            [[SPKToastCenter defaultCenter] toastWithMessage:@"系统版本不支持 NFC Core"];
        }
    } else if (indexPath.row == 2) {
        // 验证密码服务
        if ([SAKUserService sharedUserService].userAvailable) {
            PPVerifyPasswordViewController *controller = [[PPVerifyPasswordViewController alloc] init];
            [self.navigationController pushViewController:controller animated:YES];
        } else {
            @weakify(self);
            [[PPAccountController defaultInstance] loginWith:self completedBlock:^{
                @strongify(self);
                PPVerifyPasswordViewController *controller = [[PPVerifyPasswordViewController alloc] init];
                [self.navigationController pushViewController:controller animated:YES];
            }];
        }
    } else if (indexPath.row == 3) {
        PPScanCardViewController *controller = [[PPScanCardViewController alloc] init];
        [self.navigationController pushViewController:controller animated:YES];
    } else if (indexPath.row == 4) {
        PPFingerPrintPayViewController *controller = [[PPFingerPrintPayViewController alloc] init];
        [self.navigationController pushViewController:controller animated:YES];
    } else if (indexPath.row == 5) {
        PPFinacialUtilityViewController *controller = [[PPFinacialUtilityViewController alloc] init];
        [self.navigationController pushViewController:controller animated:YES];
    }
    
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
}

@end
