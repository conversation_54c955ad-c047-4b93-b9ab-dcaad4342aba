//
//  PPNFCManagementViewController.m
//  ipaymentapp
//
//  Created by 罗俊 on 2017/10/18.
//  Copyright © 2017年 Meituan.com. All rights reserved.
//

#import "PPNFCManagementViewController.h"
#import "SAKUIKitMacros.h"
#import <CoreNFC/CoreNFC.h>
#import "SPKToastCenter.h"

#pragma deploymate push "ignored-api-availability"

@interface PPNFCManagementViewController ()<NFCNDEFReaderSessionDelegate>

@property (nonatomic, strong) UIButton *readButton;
@property (nonatomic, strong) UILabel *boardLabel;
@property (nonatomic, strong) UIButton *writeButton;
@property (nonatomic, strong) UIButton *clearButtton;
@property (nonatomic, strong) UITextField *textField;
@property (nonatomic, strong) NFCNDEFReaderSession *session;

@end

@implementation PPNFCManagementViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    self.title = @"NFC 测试";
    self.view.backgroundColor = [UIColor whiteColor];

    [self setupUI];
}

- (void)setupUI
{
    _readButton = ({
        UIButton *button = [UIButton buttonWithType:UIButtonTypeSystem];
        [button setTitle:@"读取 NFC Tag" forState:UIControlStateNormal];
        [button addTarget:self action:@selector(didReadButton:) forControlEvents:UIControlEventTouchUpInside];
        button;
    });
    [self.view addSubview:_readButton];
    
    _boardLabel = ({
        UILabel *label = [[UILabel alloc] init];
        label.font = Font(15);
        label.numberOfLines = 0;
        label.backgroundColor = RGBCOLOR(240, 242, 245);
        label;
    });
    [self.view addSubview:_boardLabel];

    _writeButton = ({
        UIButton *button = [UIButton buttonWithType:UIButtonTypeSystem];
        [button setTitle:@"写 NFC Tag" forState:UIControlStateNormal];
        [button addTarget:self action:@selector(didWriteButton:) forControlEvents:UIControlEventTouchUpInside];
        button;
    });
    [self.view addSubview:_writeButton];
    
    _clearButtton = ({
        UIButton *button = [UIButton buttonWithType:UIButtonTypeSystem];
        [button setTitle:@"clear" forState:UIControlStateNormal];
        [button addTarget:self action:@selector(didClearButton:) forControlEvents:UIControlEventTouchUpInside];
        button;
    });
    [self.view addSubview:_clearButtton];
    
    _textField = ({
        UITextField *textField = [[UITextField alloc] init];
        textField.returnKeyType = UIReturnKeyDone;
        textField.font = Font(15);
        textField.placeholder = @"请输入将写入 NFC Tag 的内容";
        textField.clearButtonMode = UITextFieldViewModeWhileEditing;
        textField.rightViewMode = UITextFieldViewModeUnlessEditing;
        textField.backgroundColor = RGBCOLOR(240, 242, 245);
        textField;
    });
    [self.view addSubview:_textField];
}

- (void)didReadButton:(id)sender
{
     _session = [[NFCNDEFReaderSession alloc] initWithDelegate:self queue:nil invalidateAfterFirstRead:YES];
    [_session beginSession];
}

- (void)didClearButton:(id)sender
{
    self.boardLabel.text = @"";
}

- (void)didWriteButton:(id)sender
{
    [[SPKToastCenter defaultCenter] toastWithMessage:@"不支持写 NFC Tag"];

    if ([self.textField.text length] == 0) {
        return;
    }
    
    
}

- (void)updateViewConstraints
{
    [self.boardLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(10);
        make.centerX.equalTo(self.view);
        make.height.equalTo(@150);
        make.width.equalTo(self.view).offset(-20);
    }];
    
    [self.readButton mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.boardLabel.mas_bottom).offset(10);
        make.right.equalTo(self.view.mas_centerX);
        make.height.equalTo(@50);
        make.left.equalTo(self.view).offset(10);
    }];
    
    [self.clearButtton mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.boardLabel.mas_bottom).offset(10);
        make.left.equalTo(self.view.mas_centerX);
        make.height.equalTo(@50);
        make.right.equalTo(self.view).offset(-10);
    }];
    
    [self.textField mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.readButton.mas_bottom).offset(10);
        make.centerX.equalTo(self.view);
        make.height.equalTo(@100);
        make.width.equalTo(self.view).offset(-20);
    }];
    
    [self.writeButton mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.textField.mas_bottom).offset(10);
        make.centerX.equalTo(self.view);
        make.height.equalTo(@50);
        make.width.equalTo(self.view).offset(-20);
    }];
    
    [super updateViewConstraints];
}

#pragma mark - NFCNDEFReaderSessionDelegate

- (void)readerSession:(NFCNDEFReaderSession *)session didInvalidateWithError:(NSError *)error
{
    if (error) {
        dispatch_async(dispatch_get_main_queue(), ^{
             [[SPKToastCenter defaultCenter] toastWithMessage:[NSString stringWithFormat:@"error code = %ld, %@", error.code, [error localizedDescription]]];
        });
    } else {
        dispatch_async(dispatch_get_main_queue(), ^{
            [[SPKToastCenter defaultCenter] toastWithMessage:@"no error"];
        });
    }
}

- (void)readerSession:(NFCNDEFReaderSession *)session didDetectNDEFs:(NSArray<NFCNDEFMessage *> *)message
{
    NSMutableString *result = [NSMutableString string];
    [message enumerateObjectsUsingBlock:^(NFCNDEFMessage * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        NFCNDEFMessage *message = (NFCNDEFMessage *)obj;
        [message.records enumerateObjectsUsingBlock:^(NFCNDEFPayload * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            NFCNDEFPayload *payloadData = (NFCNDEFPayload *)obj;
            NSString *format = [NSString stringWithFormat:@"format = %d", payloadData.typeNameFormat];
            NSString *type = [NSString stringWithFormat:@"type = %@", payloadData.type];
            // 要删掉前3个字节 https://ecgbao.github.io/2017/09/04/%E5%9C%A8iOS11%E4%B8%AD%E4%BD%BF%E7%94%A8CoreNFC/
            NSData *data = [payloadData.payload subdataWithRange:NSMakeRange(3, payloadData.payload.length - 3)];
            NSString *payload = [NSString stringWithFormat:@"payload = %@", [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding]];
            NSString *ID = [NSString stringWithFormat:@"id = %@", payloadData.identifier];
            [result appendString:[NSString stringWithFormat:@"%@ %@ %@ %@", format, type, payload, ID]];
        }];
    }];
    dispatch_async(dispatch_get_main_queue(), ^{
        [[SPKToastCenter defaultCenter] toastWithMessage:@"NFC Tag 读取完成"];
        self.boardLabel.text = result;
    });
}

@end

#pragma deploymate pop

