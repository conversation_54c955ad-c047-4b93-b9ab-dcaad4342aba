//
//  PPTestViewController.m
//  ipaymentapp
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2017/12/4.
//  Copyright © 2017年 Meituan.com. All rights reserved.
//

#import "PPTestViewController.h"
#import "SAKPortal.h"
#import "SPKAlertView.h"
#import "CIPStringAdditions.h"
#import "NSDictionary+SPKShortcuts.h"

static NSString * const kPPVerifyPasswordCallbackURL = @"meituanpayment://pptest/verifypassword";
static NSString * const kPPFingerPrintPayCallbackURL = @"meituanpayment://pptest/fingerprintpay";

@interface PPTestViewController () <SAKPortalable>

@property (nonatomic, strong) NSURL *callbackURL;

@end

@implementation PPTestViewController

#pragma mark - portal

SAK_PORTAL_REGISTER()
{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        [SAKPortal registerPortalWithHandler:^UIViewController<SAKPortalable> * _Nullable(NSURL * _Nonnull URL, BOOL shouldTransfer, UIViewController * _Nonnull sourceViewController) {
            if ([URL hasSameTrunkWithURL:[NSURL URLWithString:kPPVerifyPasswordCallbackURL]]) {
                return [PPTestViewController startTestResultFromViewController:sourceViewController URL:URL];
            } else {
                return nil;
            }
        }
                                   prefixURL:[NSURL URLWithString:kPPVerifyPasswordCallbackURL]
                                    pageInfo:[SAKPortalPageInfo pageInfoWithPageName:@"测试结果页"
                                                                           className:@"PPTestViewController"
                                                                                path:kPPVerifyPasswordCallbackURL
                                                                  requiredParameters:nil
                                                                  optionalParameters:nil]];
        
        [SAKPortal registerPortalWithHandler:^UIViewController<SAKPortalable> * _Nullable(NSURL * _Nonnull URL, BOOL shouldTransfer, UIViewController * _Nonnull sourceViewController) {
            if ([URL hasSameTrunkWithURL:[NSURL URLWithString:kPPFingerPrintPayCallbackURL]]) {
                return [PPTestViewController startTestResultFromViewController:sourceViewController URL:URL];
            } else {
                return nil;
            }
        }
                                   prefixURL:[NSURL URLWithString:kPPFingerPrintPayCallbackURL]
                                    pageInfo:[SAKPortalPageInfo pageInfoWithPageName:@"测试结果页"
                                                                           className:@"PPTestViewController"
                                                                                path:kPPFingerPrintPayCallbackURL
                                                                  requiredParameters:nil
                                                                  optionalParameters:nil]];
    });

}

+ (UIViewController<SAKPortalable>* _Nullable)startTestResultFromViewController:(UIViewController * _Nonnull)fromViewController URL:(NSURL *)URL
{
    NSParameterAssert(fromViewController != nil);
    NSParameterAssert(fromViewController.navigationController != nil);
    
    PPTestViewController *controller = [[self alloc] init];
    controller.callbackURL = URL;
    [fromViewController.navigationController pushViewController:controller animated:YES];
    
    return controller;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.title = @"测试结果页";
    [self.view setBackgroundColor:[UIColor whiteColor]];
    
    NSDictionary *queryParameterDictionary = [[self.callbackURL query] cipf_dictionaryByParseInURLParameterFormat];
    if ([self.callbackURL.path isEqualToString:@"/verifypassword"]) {
        NSString *payToken = [queryParameterDictionary spk_stringForKey:@"password_token"];
        [SPKAlertView showAlertViewWithTitle:@"验证密码测试结果"
                                     message:[NSString stringWithFormat:@"payToken=%@", payToken]
                           cancelButtonTitle:nil
                       completionButtonTitle:@"知道了"
                                    canceled:nil
                                  completion:^{
                                      [self.navigationController popToRootViewControllerAnimated:YES];
                                  }];
    } else {
        NSInteger result = [queryParameterDictionary spk_integerForKey:@"result"];
        NSString *token = [queryParameterDictionary spk_stringForKey:@"token"];
        if (result == 0) {
            [SPKAlertView showAlertViewWithTitle:@"指纹支付测试结果"
                                         message:[NSString stringWithFormat:@"payToken=%@", token]
                               cancelButtonTitle:nil
                           completionButtonTitle:@"知道了"
                                        canceled:nil
                                      completion:^{
                                          [self.navigationController popToRootViewControllerAnimated:YES];
                                      }];
        } else if (result == 1) {
            [SPKAlertView showAlertViewWithMessage:@"指纹支付失败"
                             completionButtonTitle:@"知道了"
                                        completion:^{
                                            [self.navigationController popToRootViewControllerAnimated:YES];
                                        }];
        } else {
            [SPKAlertView showAlertViewWithMessage:@"指纹支付取消"
                             completionButtonTitle:@"知道了"
                                        completion:^{
                                            [self.navigationController popToRootViewControllerAnimated:YES];
                                        }];
        }
    }
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

#pragma mark - SAKPortalable

- (NSString *)pageDescription
{
    return self.title;
}

@end
