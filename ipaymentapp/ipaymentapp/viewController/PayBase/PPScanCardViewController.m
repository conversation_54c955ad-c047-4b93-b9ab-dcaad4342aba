//
//  PPScanCardViewController.m
//  ipaymentapp
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2018/2/22.
//  Copyright © 2018年 Meituan.com. All rights reserved.
//

#import "PPScanCardViewController.h"
#import "SPKScanCardViewController.h"

@interface PPScanCardViewController ()

@property (nonatomic, strong) UIButton *scanButton;
@property (nonatomic, strong) UILabel *nameLabel;
@property (nonatomic, strong) UILabel *cardLabel;

@end

@implementation PPScanCardViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.title = @"拍照识别银行卡号";
    
    _scanButton = ({
        UIButton *button = [UIButton buttonWithType:UIButtonTypeCustom];
        button.backgroundColor = [UIColor lightGrayColor];
        [button setTitle:@"开始识别" forState:UIControlStateNormal];
        [button addTarget:self action:@selector(didClickScanButton) forControlEvents:UIControlEventTouchUpInside];
        button;
    });
    [self.view addSubview:_scanButton];
    
    _nameLabel = ({
        UILabel *label = [[UILabel alloc] init];
        label.backgroundColor = [UIColor clearColor];
        label.font = [UIFont systemFontOfSize:18];
        label.textColor = [UIColor blackColor];
        label.text = @"卡号：";
        label.numberOfLines = 1;
        label;
    });
    [self.view addSubview:_nameLabel];
    
    _cardLabel = ({
        UILabel *label = [[UILabel alloc] init];
        label.backgroundColor = [UIColor lightGrayColor];
        label.font = [UIFont systemFontOfSize:13];
        label.textColor = [UIColor blackColor];
        label.numberOfLines = 1;
        label;
    });
    [self.view addSubview:_cardLabel];
    [self.view setNeedsUpdateConstraints];
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

#pragma mark - Action

- (void)didClickScanButton {
    SPKScanCardViewController *controller = [[SPKScanCardViewController alloc] init];
    __weak typeof(self) weakSelf = self;
    [controller setCompletionBlock:^(NSString *cardNumber){
        __strong typeof(weakSelf) strongSelf = weakSelf;
        strongSelf.cardLabel.text = cardNumber;
    }];
    [self.navigationController pushViewController:controller animated:YES];
}

#pragma mark - Override

- (void)updateViewConstraints {
    [self.scanButton mas_updateConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view).offset(150);
        make.centerX.equalTo(self.view);
        make.height.mas_equalTo(44);
        make.width.mas_equalTo(100);
    }];
    
    [self.nameLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).offset(20);
        make.top.equalTo(self.scanButton.mas_bottom).offset(20);
        make.width.mas_equalTo(60);
        make.height.mas_equalTo(40);
    }];
    
    [self.cardLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.nameLabel.mas_right);
        make.top.equalTo(self.scanButton.mas_bottom).offset(20);
        make.right.equalTo(self.view).offset(-30);
        make.height.mas_equalTo(40);
    }];
    
    [super updateViewConstraints];
}

@end
