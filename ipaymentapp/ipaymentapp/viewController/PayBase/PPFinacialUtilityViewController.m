//
//  PPFinacialUtilityViewController.m
//  ipaymentapp
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2018/2/22.
//  Copyright © 2018年 Meituan.com. All rights reserved.
//

#import "PPFinacialUtilityViewController.h"
#import "NSString+SPKFactors.h"
#import "Masonry.h"
#import "SPKUIKitMacros.h"

@interface PPFinacialUtilityViewController ()

@end

@implementation PPFinacialUtilityViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.title = @"金融专项工具集";
    
    [self setupUI];
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

- (void)setupUI {
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.backgroundColor = [UIColor clearColor];
    titleLabel.text = @"分段前号码";
    titleLabel.font = Font(17);
    [self.view addSubview:titleLabel];
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).offset(60);
        make.top.equalTo(self.view).offset(40);
    }];
    
    UILabel *titleLabel1 = [[UILabel alloc] init];
    titleLabel1.backgroundColor = [UIColor clearColor];
    titleLabel1.text = @"分段后号码";
    titleLabel1.font = Font(17);
    [self.view addSubview:titleLabel1];
    [titleLabel1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.view).offset(-80);
        make.top.equalTo(self.view).offset(40);
    }];
    
    UILabel *bankCardLabel = [[UILabel alloc] init];
    bankCardLabel.backgroundColor = [UIColor clearColor];
    bankCardLabel.text = @"银行卡号:";
    bankCardLabel.font = Font(12);
    [self.view addSubview:bankCardLabel];
    [bankCardLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).offset(5);
        make.top.equalTo(titleLabel.mas_bottom).offset(20);
    }];
    
    NSString *bankCardNumber = @"6217000830000123038";
    UILabel *bankCardLabel1 = [[UILabel alloc] init];
    bankCardLabel1.backgroundColor = [UIColor clearColor];
    bankCardLabel1.text = bankCardNumber;
    bankCardLabel1.font = Font(12);
    [self.view addSubview:bankCardLabel1];
    [bankCardLabel1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(titleLabel);
        make.top.equalTo(titleLabel.mas_bottom).offset(20);
    }];
    
    UILabel *bankCardLabel2 = [[UILabel alloc] init];
    bankCardLabel2.backgroundColor = [UIColor clearColor];
    bankCardLabel2.text = [NSString spk_separateCardNumberWithSpace:bankCardNumber];
    bankCardLabel2.font = Font(12);
    [self.view addSubview:bankCardLabel2];
    [bankCardLabel2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(titleLabel1);
        make.top.equalTo(titleLabel1.mas_bottom).offset(20);
    }];
    
    UILabel *phoneNumberLabel = [[UILabel alloc] init];
    phoneNumberLabel.backgroundColor = [UIColor clearColor];
    phoneNumberLabel.text = @"手机号:";
    phoneNumberLabel.font = Font(12);
    [self.view addSubview:phoneNumberLabel];
    [phoneNumberLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).offset(5);
        make.top.equalTo(bankCardLabel.mas_bottom).offset(20);
    }];
    
    NSString *phoneNumber = @"***********";
    UILabel *phoneNumberLabel1 = [[UILabel alloc] init];
    phoneNumberLabel1.backgroundColor = [UIColor clearColor];
    phoneNumberLabel1.text = phoneNumber;
    phoneNumberLabel1.font = Font(12);
    [self.view addSubview:phoneNumberLabel1];
    [phoneNumberLabel1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(titleLabel);
        make.top.equalTo(bankCardLabel.mas_bottom).offset(20);
    }];
    
    UILabel *phoneNumberLabel2 = [[UILabel alloc] init];
    phoneNumberLabel2.backgroundColor = [UIColor clearColor];
    phoneNumberLabel2.text = [NSString spk_separatePhoneNumberWithSpace:phoneNumber];
    phoneNumberLabel2.font = Font(12);
    [self.view addSubview:phoneNumberLabel2];
    [phoneNumberLabel2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(titleLabel1);
        make.top.equalTo(bankCardLabel.mas_bottom).offset(20);
    }];
    
    UILabel *IDLabel = [[UILabel alloc] init];
    IDLabel.backgroundColor = [UIColor clearColor];
    IDLabel.text = @"身份证号:";
    IDLabel.font = Font(12);
    [self.view addSubview:IDLabel];
    [IDLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).offset(5);
        make.top.equalTo(phoneNumberLabel.mas_bottom).offset(20);
    }];
    
    NSString *IDNumber = @"110123199001011234";
    UILabel *IDLabel1 = [[UILabel alloc] init];
    IDLabel1.backgroundColor = [UIColor clearColor];
    IDLabel1.text = IDNumber;
    IDLabel1.font = Font(12);
    [self.view addSubview:IDLabel1];
    [IDLabel1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(titleLabel);
        make.top.equalTo(phoneNumberLabel.mas_bottom).offset(20);
    }];
    
    UILabel *IDLabel2 = [[UILabel alloc] init];
    IDLabel2.backgroundColor = [UIColor clearColor];
    IDLabel2.text = [NSString spk_separateIDNumberWithSpace:IDNumber];
    IDLabel2.font = Font(12);
    [self.view addSubview:IDLabel2];
    [IDLabel2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(titleLabel1);
        make.top.equalTo(phoneNumberLabel.mas_bottom).offset(20);
    }];
    
    UILabel *dateLabel = [[UILabel alloc] init];
    dateLabel.backgroundColor = [UIColor clearColor];
    dateLabel.text = @"有效期:";
    dateLabel.font = Font(12);
    [self.view addSubview:dateLabel];
    [dateLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).offset(5);
        make.top.equalTo(IDLabel.mas_bottom).offset(20);
    }];
    
    NSString *dateNumber = @"0522";
    UILabel *dateLabel1 = [[UILabel alloc] init];
    dateLabel1.backgroundColor = [UIColor clearColor];
    dateLabel1.text = dateNumber;
    dateLabel1.font = Font(12);
    [self.view addSubview:dateLabel1];
    [dateLabel1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(titleLabel);
        make.top.equalTo(IDLabel.mas_bottom).offset(20);
    }];
    
    UILabel *dateLabel2 = [[UILabel alloc] init];
    dateLabel2.backgroundColor = [UIColor clearColor];
    dateLabel2.text = [NSString spk_separateValidDateWithSlash:dateNumber];
    dateLabel2.font = Font(12);
    [self.view addSubview:dateLabel2];
    [dateLabel2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(titleLabel1);
        make.top.equalTo(IDLabel.mas_bottom).offset(20);
    }];
}

@end
