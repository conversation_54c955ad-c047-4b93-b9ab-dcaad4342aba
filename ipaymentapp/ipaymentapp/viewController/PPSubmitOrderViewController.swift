//
//  PPSubmitOrderViewController.swift
//  ipaymentapp
//
//  Created by l<PERSON><PERSON><PERSON> on 2018/2/28.
//  Copyright © 2018年 Meituan.com. All rights reserved.
//
//  下单页

import UIKit

class PPSubmitOrderViewController: MTBaseViewController, UITableViewDelegate, UITableViewDataSource, MTCCashierDelegate, UIScrollViewDelegate {
    
    @objc var contentArray: [PPContentInfo] = []
    let cellHeight = 44
    var contentInfoTableView : UITableView!
    var submitOrderView : UIScrollView!
    var submitOrderButton : UIButton!
    var onclickPayButton : UIButton!
    var helloSubmitButton : UIButton!
    var h5SubmitButton : UIButton!
    var icButton : UIButton!
    var prePaymentButton : UIButton!
    var helloPayController : PPHelloPayController?
    var installmentSubOrderListView : PPPaymentSettingView!
    var useHybridCashierView : PPPaymentSettingView!
    var callbackURLView : PPPaymentSettingView!
    var callbackSettingView : PPPaymentSettingView!
    var chargePageURLView : PPPaymentSettingView!
    var combineOrderView : PPPaymentSettingView!
    var oneclickPayOpenSettingView : PPPaymentSettingView!
    var oneclickPayReconfirmAlertSettingView : PPPaymentSettingView!
    var creditMerchantView : PPPaymentSettingView!
    var DCEPModelView : PPPaymentSettingView!
    var useLastOrderInfoView : PPPaymentSettingView!
    var appIdView : PPPaymentSettingView!
    var extraStaticsView : PPPaymentSettingView!
    var extraDataView : PPPaymentSettingView!
    var preCashierInfoView : PPPaymentSettingView!
    var prePromoPayInfoView : PPPaymentSettingView!
    var cifDataView : PPPaymentSettingView!
    var payLaterView : PPPaymentSettingView!
    var payDeferView : PPPaymentSettingView!

    override init(nibName nibNameOrNil: String?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
        self.title = "支付订单"
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        // jenkins 打包报错，先注释掉
        PPHostSwitcher.shared().setup(withParams: ["isUseCreditPay" : false]);
        
        let button = UIButton.greyCustom()
        button?.frame = CGRect(x: 0, y: 0, width: 100, height: 30)
        button?.addTarget(self, action: #selector(didClickedAddArgs(sender:)), for: .touchUpInside)
        button?.setTitle("添加参数", for: .normal)
        button?.backgroundColor = UIColor.red
        self.navigationItem.rightBarButtonItem = UIBarButtonItem.init(customView: button!)
        
        view.backgroundColor = .generalBackground
        setupNavBar()
        
        self.submitOrderView = {
            let scrollView = UIScrollView()
            scrollView.alwaysBounceVertical = true
            scrollView.delegate = self
            return scrollView
        }()
        self.view.addSubview(self.submitOrderView)
        self.activateAutoScrolling(for: self.submitOrderView)
        
        if #available(iOS 11.0, *) {
            self.submitOrderView.contentInsetAdjustmentBehavior = .never
        }
        for conentInfo in self.contentArray {
            if conentInfo.value == nil || conentInfo.value.count == 0 {
                conentInfo.value = conentInfo.defaultValue
            }
        }
        
        self.contentInfoTableView = {
            let tableView = UITableView()
            tableView.backgroundColor = UIColor.clear
            tableView.dataSource = self
            tableView.delegate = self
            tableView.isScrollEnabled = false
            tableView.separatorStyle = .none
            return tableView
        }()
        self.submitOrderView.addSubview(self.contentInfoTableView)
        
        self.submitOrderButton = {
            let button : UIButton = UIButton.greenCustom()
            button.setTitle("提交订单", for: .normal)
            button.addTarget(self, action: #selector(didClickSubmitOrderButton(sender:)), for: .touchUpInside)
            button.isEnabled = true
            return button
        }()
        self.submitOrderView.addSubview(self.submitOrderButton)
        
        self.onclickPayButton = {
            let button : UIButton = UIButton.greenCustom()
            button.setTitle("极速支付", for: .normal)
            button.addTarget(self, action: #selector(didClickOneClickPayButton(sender:)), for: .touchUpInside)
            return button
        }()
        self.submitOrderView.addSubview(self.onclickPayButton)
        
        self.helloSubmitButton = {
            let button : UIButton = UIButton.greenCustom()
            button.setTitle("支付组件", for: .normal)
            button.addTarget(self, action: #selector(didClickedHelloPayOrderButton(sender:)), for: .touchUpInside)
            return button
        }()
        self.submitOrderView.addSubview(self.helloSubmitButton)
        
        self.h5SubmitButton = {
            let button : UIButton = UIButton.greenCustom()
            button.setTitle("H5提单页", for: .normal)
            button.addTarget(self, action: #selector(didClickedH5SubmitButton(sender:)), for: .touchUpInside)
            return button
        }()
        self.submitOrderView.addSubview(self.h5SubmitButton)
        
        self.icButton = {
            let button : UIButton = UIButton.greenCustom()
            button.setTitle("国际版提单页", for: .normal)
            button.addTarget(self, action: #selector(didClickedIcSubmitButton(sender:)), for: .touchUpInside)
            return button
        }()
        self.submitOrderView.addSubview(self.icButton)
        
        self.prePaymentButton = {
            let button : UIButton = UIButton.greenCustom()
            button.setTitle("前置支付组件", for: .normal)
            button.addTarget(self, action: #selector(didClickedPrePaymentButton(sender:)), for: .touchUpInside)
            return button
        }()
        self.submitOrderView.addSubview(self.prePaymentButton)
        
        self.installmentSubOrderListView = PPPaymentSettingView()
        self.installmentSubOrderListView.titleLabel.text = "商品贴息信息"
        self.installmentSubOrderListView.titleField.text = "{\"installmentSubsidyList\":[{\"commodityList\":[{\"commodityActivity\":\"{\\\"outerSubActivityId\\\":\\\"-101\\\",\\\"contractNo\\\":\\\"cx0111\\\",\\\"subsidyFlag\\\":false,\\\"interestSubsidyDetailList\\\":[{\\\"installmentTerm\\\":3,\\\"undertakeRatios\\\":[{\\\"ratioType\\\":\\\"PLATFORM\\\",\\\"ratioNum\\\":0.50},{\\\"ratioType\\\":\\\"MERCHANT\\\",\\\"ratioNum\\\":0.50},{\\\"ratioType\\\":\\\"USER\\\",\\\"ratioNum\\\":0.00}]},{\\\"installmentTerm\\\":6,\\\"undertakeRatios\\\":[{\\\"ratioType\\\":\\\"PLATFORM\\\",\\\"ratioNum\\\":1.00},{\\\"ratioType\\\":\\\"MERCHANT\\\",\\\"ratioNum\\\":0.00},{\\\"ratioType\\\":\\\"USER\\\",\\\"ratioNum\\\":0.00}]},{\\\"installmentTerm\\\":9,\\\"undertakeRatios\\\":[{\\\"ratioType\\\":\\\"PLATFORM\\\",\\\"ratioNum\\\":0.00},{\\\"ratioType\\\":\\\"MERCHANT\\\",\\\"ratioNum\\\":1.00},{\\\"ratioType\\\":\\\"USER\\\",\\\"ratioNum\\\":0.00}]},{\\\"installmentTerm\\\":12,\\\"undertakeRatios\\\":[{\\\"ratioType\\\":\\\"PLATFORM\\\",\\\"ratioNum\\\":0.30},{\\\"ratioType\\\":\\\"MERCHANT\\\",\\\"ratioNum\\\":0.30},{\\\"ratioType\\\":\\\"USER\\\",\\\"ratioNum\\\":0.40}]}]}\",\"payAmountOfFen\":100000,\"poiId\":\"610496\",\"skuCount\":1,\"skuId\":\"2206156123\",\"spuId\":\"2202800740\"}],\"selectedTerm\":3,\"totalOriginalAmountOfFen\":0,\"totalPayAmountOfFen\":100000}]}"
        self.submitOrderView.addSubview(self.installmentSubOrderListView)
        
        self.useHybridCashierView = PPPaymentSettingView()
        self.useHybridCashierView.titleLabel.text = "Hybrid收银台"
        self.useHybridCashierView.titleField.isEditable = false
        self.useHybridCashierView.titleField.text = "N/A(不需要填写)"
        self.submitOrderView.addSubview(self.useHybridCashierView)
        
        self.callbackURLView = PPPaymentSettingView()
        self.callbackURLView.titleLabel.text = "callback"
        self.callbackURLView.titleField.text = "https://i.meituan.com"
        self.submitOrderView.addSubview(self.callbackURLView)
        
        self.callbackSettingView = PPPaymentSettingView()
        self.callbackSettingView.titleLabel.text = "收银台回调"
        self.callbackSettingView.titleField.isEditable = false
        self.callbackSettingView.titleField.text = "URL和delegate同时回调"
        self.submitOrderView.addSubview(self.callbackSettingView)
        
        self.chargePageURLView = PPPaymentSettingView()
        self.chargePageURLView.titleLabel.text = "充值单"
        self.chargePageURLView.titleField.isEditable = false
        self.chargePageURLView.titleField.text = "N/A(不需要填写)"
        self.submitOrderView.addSubview(self.chargePageURLView)
        
        self.combineOrderView = PPPaymentSettingView()
        self.combineOrderView.titleLabel.text = "合单支付"
        self.combineOrderView.titleField.isEditable = true
        let combinDic:[String: String] = ["ct": "standard-cashier"]
        self.combineOrderView.titleField.text = "输入参数例如:"+(combinDic as NSDictionary).spk_JSONString()
        self.combineOrderView.titleField.font = UIFont.systemFont(ofSize: 12)
        self.submitOrderView.addSubview(self.combineOrderView)
        
        self.oneclickPayOpenSettingView = PPPaymentSettingView()
        self.oneclickPayOpenSettingView.titleLabel.text = "开启极速"
        self.oneclickPayOpenSettingView.titleField.isEditable = false
        self.oneclickPayOpenSettingView.titleField.text = "需要开通极速支付时打开"
        self.submitOrderView.addSubview(self.oneclickPayOpenSettingView)
        
        self.oneclickPayReconfirmAlertSettingView = PPPaymentSettingView()
        self.oneclickPayReconfirmAlertSettingView.titleLabel.text = "确认弹窗"
        self.oneclickPayReconfirmAlertSettingView.titleField.isEditable = false
        self.oneclickPayReconfirmAlertSettingView.titleField.text = "开启极速支付是否确认弹窗"
        self.submitOrderView.addSubview(self.oneclickPayReconfirmAlertSettingView)

        self.creditMerchantView = PPPaymentSettingView()
        self.creditMerchantView.titleLabel.text = "切小贷商户"
        self.creditMerchantView.titleField.isEditable = false
        self.creditMerchantView.titleField.text = "ST及线上生效(谨慎使用)"
        self.submitOrderView.addSubview(self.creditMerchantView)
        
        self.DCEPModelView = PPPaymentSettingView()
        self.DCEPModelView.titleLabel.text = "DCEP商户切换"
        self.DCEPModelView.titleField.isEditable = false
        self.DCEPModelView.titleField.text = "DCEP-ST测试-开关必须打开"
        self.submitOrderView.addSubview(self.DCEPModelView)

        self.useLastOrderInfoView = PPPaymentSettingView()
        self.useLastOrderInfoView.titleLabel.text = "使用旧交易单"
        self.useLastOrderInfoView.titleField.isEditable = false
        self.useLastOrderInfoView.titleField.text = "使用上一次的交易单信息"
        self.submitOrderView.addSubview(self.useLastOrderInfoView)
        
        self.appIdView = PPPaymentSettingView()
        self.appIdView.titleLabel.text = "app_id参数"
        self.appIdView.titleField.text = "DefaultAppID"
        self.submitOrderView.addSubview(self.appIdView)
        
        self.extraStaticsView = PPPaymentSettingView()
        self.extraStaticsView.titleLabel.text = "extra_statics参数"
        self.extraStaticsView.titleField.text = "Text is Default"
        self.submitOrderView.addSubview(self.extraStaticsView)
        
        self.extraDataView = PPPaymentSettingView()
        self.extraDataView.titleField.isEditable = false
        self.extraDataView.titleLabel.text = "extra_data"
        self.extraDataView.titleField.text = "下方四个参数可拼接该参"
        self.submitOrderView.addSubview(self.extraDataView)
        
        self.preCashierInfoView = PPPaymentSettingView()
        let preCashierInfoDic:[String: Any] = ["preCashierInfo": "{\"cashierType\":\"pre-cashier\",\"lastCashierScene\":\"pre_promo_cashier\",\"openStatus\":true,\"payFeeCent\":2480,\"payType\":\"creditpay\",\"payTypeUniqueKey\":\"creditpay\",\"product\":18,\"uniqueIdentifyCode\":\"4297a2ea-4829-4bf0-a192-aba913021a53\",\"verifyOptStatus\":false}",
                                               "extdata": "{\"componentSkinPayTypeUniqueKey\":\"creditpay\",\"disabledPayTypeList\":[\"weeklypay\"],\"preComponentDataLinkType\":2,\"preComponentType\":\"thirdPayPreComponent\",\"preShowCashierProducts\":[\"invitepay\",\"oneclickpay\"],\"serialCode\":\"fde909f7-8988-4a00-add0-3f0bf7b4fd00\",\"uniqueIdentifyCode\":\"4297a2ea-4829-4bf0-a192-aba913021a53\"}",
                                               "serialCode": "fde909f7-8988-4a00-add0-3f0bf7b4fd00"]
        self.preCashierInfoView.titleLabel.text = "前置组件选中月付唤起前置收银台"
        self.preCashierInfoView.titleLabel.numberOfLines = 3;
        self.preCashierInfoView.titleLabelHeight = 120;
        self.preCashierInfoView.titleFieldHeight = 120;
        self.preCashierInfoView.titleField.isEditable = true
        self.preCashierInfoView.titleField.showsVerticalScrollIndicator = true;
        self.preCashierInfoView.titleField.font = UIFont.systemFont(ofSize: 12)
        self.preCashierInfoView.titleField.text = (preCashierInfoDic as NSDictionary).spk_JSONString()
        self.submitOrderView.addSubview(self.preCashierInfoView)
        
        self.prePromoPayInfoView = PPPaymentSettingView()
        let prePromoPayInfoDic:[String: Any] = ["prePromoPayInfo": "{\"payType\":\"mtpay\",\"passParams\":\"{\\\"payType\\\":\\\"MAIDAN\\\",\\\"discount\\\":0.88,\\\"campaigns\\\":[{\\\"reduce\\\":\\\"0.88\\\",\\\"promoType\\\":2,\\\"assetId\\\":\\\"2000005576466119\\\",\\\"govCouponFlag\\\":false}],\\\"preChannel\\\":\\\"pre_promo\\\",\\\"requestId\\\":\\\"ee48688e-100c-4d78-91aa-b8e7a472bcca\\\"}\",\"serialCode\":\"3fb5acfb-e162-4124-badd-3ca9df583ce6\",\"uniqueIdentityCode\":\"e24ddf49-cfc9-4845-921f-bcbf92e671d7\",\"creditStatus\":\"0\",\"mtPayType\":\"creditpay\",\"cashierType\":\"preorder-guide\"}",
                                                "prePromoSelected": 1]
        self.prePromoPayInfoView.titleLabel.text = "小黄条优惠前置"
        self.prePromoPayInfoView.titleLabel.numberOfLines = 2;
        self.prePromoPayInfoView.titleLabelHeight = 80;
        self.prePromoPayInfoView.titleFieldHeight = 80;
        self.prePromoPayInfoView.titleField.isEditable = true
        self.prePromoPayInfoView.titleField.showsVerticalScrollIndicator = true;
        self.prePromoPayInfoView.titleField.font = UIFont.systemFont(ofSize: 12)
        self.prePromoPayInfoView.titleField.text = (prePromoPayInfoDic as NSDictionary).spk_JSONString()
        self.submitOrderView.addSubview(self.prePromoPayInfoView)
        
        self.cifDataView = PPPaymentSettingView()
        let cifDataDic:[String: String] = ["ct": "pre-cashier"]
        self.cifDataView.titleLabel.text = "cif"
        self.cifDataView.titleField.text = (cifDataDic as NSDictionary).spk_JSONString()
        self.submitOrderView.addSubview(self.cifDataView)
        
        self.payLaterView = PPPaymentSettingView()
        self.payLaterView.titleLabel.text = "代扣模\n版列表"
        self.payLaterView.titleLabel.numberOfLines = 2;
        self.payLaterView.titleLabelHeight = 80;
        self.payLaterView.titleFieldHeight = 80;
        
        let guide_plan_infos = NSMutableArray()
        guide_plan_infos[0] = ["plan_type":1, "plan_id":1872, "sign_merchant_no": 41910171103242253]
        guide_plan_infos[1] = ["plan_type":2, "plan_id":3481, "sign_merchant_no": 42006022103344691]
        self.payLaterView.titleField.text = guide_plan_infos.spk_JSONString()
        self.payLaterView.titleField.isEditable = true
        self.payLaterView.titleField.showsVerticalScrollIndicator = true;
        self.payLaterView.titleField.font = UIFont.systemFont(ofSize: 12)
        self.submitOrderView.addSubview(self.payLaterView)
        
        self.payDeferView = PPPaymentSettingView()
        self.payDeferView.titleLabel.text = "先用后付"
        self.payDeferView.titleField.isEditable = false
        self.payDeferView.titleField.text = "需要使用先用后付时打开"
        self.submitOrderView.addSubview(self.payDeferView)
        
        bindData()
        self.view.setNeedsUpdateConstraints()
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        /// 进入页面滑动至底部
        let bottomOffset = CGPoint(x: 0, y: submitOrderView.contentSize.height - submitOrderView.frame.size.height)
        if bottomOffset.y > 0 {
            submitOrderView.setContentOffset(bottomOffset, animated: false)
        }
        
    }
    
    override func traitCollectionDidChange(_ previousTraitCollection: UITraitCollection?) {
        setupNavBar()
    }
    
    func setupNavBar() {
        if #available(iOS 13.0, *) {
            if traitCollection.userInterfaceStyle == .dark {
                let textAttributes = [NSAttributedString.Key.foregroundColor:UIColor.white]
                self.navigationController?.navigationBar.titleTextAttributes = textAttributes
                self.navigationController?.navigationBar.setBackgroundImage(UIImage(named: "bg_navigationBar_white")?.sakui_image(withTintColor: .black)?.sakui_resizable(), for: .default)
            } else {
                let textAttributes = [NSAttributedString.Key.foregroundColor:UIColor.black]
                self.navigationController?.navigationBar.titleTextAttributes = textAttributes
                self.navigationController?.navigationBar.setBackgroundImage(UIImage(named: "bg_navigationBar_white")?.sakui_resizable(), for: .default)
            }
        }
    }
    
    func bindData() {
        chargePageURLView.switchView.rac_signal(for: .valueChanged).deliverOnMainThread().subscribeNext { [unowned self] (value) in
            if self.chargePageURLView.switchView.isOn {
                let rechargeType = PPContentInfo()
                let rechargeScene = PPContentInfo()
                rechargeType.canEditOfTitle = true
                rechargeType.identifier = "recharge_type"
                rechargeType.title = "recharge_type"
                rechargeType.promptString = "充值类型: 1: b2c 2: c2c 3: b2b"
                rechargeType.defaultValue = "1"
                rechargeType.value = rechargeType.defaultValue;
                rechargeScene.canEditOfTitle = true
                rechargeScene.identifier = "nb_source"
                rechargeScene.title = "nb_source"
                rechargeScene.promptString = "充值场景: special_recharge"
                rechargeScene.defaultValue = "special_recharge"
                rechargeScene.value = rechargeScene.defaultValue;
                self.contentArray.append(rechargeType)
                self.contentArray.append(rechargeScene)
                
                PPHostSwitcher.shared().contentArray = self.contentArray;
                PPHostSwitcher.shared().setup(withParams: ["isUseCreditPay" : false]);
                self.updateViewConstraints()
                self.contentInfoTableView?.reloadData()
                
            } else {
                self.contentArray = self.contentArray.filter{
                    return $0.identifier != "nb_source" && $0.identifier != "recharge_type"
                }
                
                PPHostSwitcher.shared().contentArray = self.contentArray;
                PPHostSwitcher.shared().setup(withParams: ["isUseCreditPay" : false]);
                self.updateViewConstraints()
                self.contentInfoTableView?.reloadData()
            }
        }
        
        useHybridCashierView.switchView.rac_signal(for: .valueChanged).deliverOnMainThread().subscribeNext { [unowned self] (value) in
            self.combineOrderView.isUserInteractionEnabled = !self.useHybridCashierView.switchView.isOn
            self.combineOrderView.switchView.isEnabled = !self.useHybridCashierView.switchView.isOn
            self.helloSubmitButton.isEnabled = !self.useHybridCashierView.switchView.isOn
            self.h5SubmitButton.isEnabled = !self.useHybridCashierView.switchView.isOn
            self.callbackURLView.switchView.isOn = self.useHybridCashierView.switchView.isOn
            self.chargePageURLView.switchView.isEnabled = !self.useHybridCashierView.switchView.isOn
            self.chargePageURLView.isUserInteractionEnabled = !self.useHybridCashierView.switchView.isOn
        }
        
        creditMerchantView.switchView.rac_signal(for: .valueChanged).deliverOnMainThread().subscribeNext { [unowned self] (value) in

            PPHostSwitcher.shared().setup(withParams: ["isUseCreditPay" : self.creditMerchantView.switchView.isOn]);
        }
        
        useHybridCashierView.switchView.rac_signal(for: .valueChanged).deliverOnMainThread().subscribeNext { [unowned self] (value) in
            self.combineOrderView.isUserInteractionEnabled = !self.useHybridCashierView.switchView.isOn
            self.combineOrderView.switchView.isEnabled = !self.useHybridCashierView.switchView.isOn
            self.callbackSettingView.isUserInteractionEnabled = !self.useHybridCashierView.switchView.isOn
            self.callbackSettingView.switchView.isEnabled = !self.useHybridCashierView.switchView.isOn
            self.helloSubmitButton.isEnabled = !self.useHybridCashierView.switchView.isOn
            self.h5SubmitButton.isEnabled = !self.useHybridCashierView.switchView.isOn
            self.callbackURLView.switchView.isOn = self.useHybridCashierView.switchView.isOn
            self.chargePageURLView.switchView.isEnabled = !self.useHybridCashierView.switchView.isOn
            self.chargePageURLView.isUserInteractionEnabled = !self.useHybridCashierView.switchView.isOn
        }
        
        DCEPModelView.switchView.rac_signal(for: .valueChanged).deliverOnMainThread().subscribeNext { [unowned self] (value) in
            
            if self.DCEPModelView.switchView.isOn {
                let switchType = PPContentInfo()
                switchType.identifier = "switch_DCEP"
                self.contentArray.append(switchType)
                
                PPHostSwitcher.shared().contentArray = self.contentArray;
                PPHostSwitcher.shared().setup(withParams: ["isUseCreditPay" : false]);
                self.contentArray.removeLast();
                self.updateViewConstraints()
                self.contentInfoTableView?.reloadData()
            }
            else {
                self.contentArray = self.contentArray.filter{
                    return $0.identifier != "switch_DCEP"
                }
                
                PPHostSwitcher.shared().contentArray = self.contentArray;
                PPHostSwitcher.shared().setup(withParams: ["isUseCreditPay" : false]);
                self.updateViewConstraints()
                self.contentInfoTableView?.reloadData()
            }
        }
        
        useLastOrderInfoView.switchView.rac_signal(for: .valueChanged).deliverOnMainThread().subscribeNext { [unowned self] (value) in
            
            if self.useLastOrderInfoView.switchView.isOn {
                self.onclickPayButton.isEnabled = false
                self.helloSubmitButton.isEnabled = false
                self.h5SubmitButton.isEnabled = false
            } else {
                self.onclickPayButton.isEnabled = true
                self.helloSubmitButton.isEnabled = true
                self.h5SubmitButton.isEnabled = true
            }
        }
        
        installmentSubOrderListView.switchView.rac_signal(for: .valueChanged).deliverOnMainThread().subscribeNext { [unowned self] (value) in
            
            if self.installmentSubOrderListView.switchView.isOn {
                let installmentSubOrderList = PPContentInfo()
                installmentSubOrderList.identifier = "installmentSubOrderList"
                installmentSubOrderList.title = "installmentSubOrList"
                installmentSubOrderList.defaultValue = self.installmentSubOrderListView.titleField.text
                installmentSubOrderList.value = installmentSubOrderList.defaultValue;
                self.contentArray.append(installmentSubOrderList)
            } else {
                self.contentArray = self.contentArray.filter { $0.identifier != "installmentSubOrderList" }
            }
        }
        
        preCashierInfoView.switchView.rac_signal(for: .valueChanged).deliverOnMainThread().subscribeNext { [unowned self] (value) in
            
            if self.preCashierInfoView.switchView.isOn {
                self.extraDataView.switchView.isOn = true
            } else {
                self.extraDataView.switchView.isOn = !checkAllSwitchesOff()
            }
        }
        
        prePromoPayInfoView.switchView.rac_signal(for: .valueChanged).deliverOnMainThread().subscribeNext { [unowned self] (value) in
            
            if self.prePromoPayInfoView.switchView.isOn {
                self.extraDataView.switchView.isOn = true
            } else {
                self.extraDataView.switchView.isOn = !checkAllSwitchesOff()
            }
        }
        
        appIdView.switchView.rac_signal(for: .valueChanged).deliverOnMainThread().subscribeNext { [unowned self] (value) in
            
            if self.appIdView.switchView.isOn {
                self.extraDataView.switchView.isOn = true
            } else {
                self.extraDataView.switchView.isOn = !checkAllSwitchesOff()
            }
        }
        
        payLaterView.switchView.rac_signal(for: .valueChanged).deliverOnMainThread().subscribeNext { [unowned self] (value) in
            
            if self.payLaterView.switchView.isOn {
                self.extraDataView.switchView.isOn = true
            } else {
                self.extraDataView.switchView.isOn = !checkAllSwitchesOff()
            }
        }
    }
    
    // 新增状态检查方法
    private func checkAllSwitchesOff() -> Bool {
        return !preCashierInfoView.switchView.isOn &&
               !prePromoPayInfoView.switchView.isOn &&
               !appIdView.switchView.isOn &&
               !payLaterView.switchView.isOn
    }
    
    override func updateViewConstraints() {
        self.submitOrderView.mas_updateConstraints({ make in
            make?.edges.equalTo()(self.view)
            make?.size.equalTo()(self.view)
            make?.top.left().width().height().equalTo()(self.view)
        })
        self.contentInfoTableView.mas_updateConstraints({ make in
            make?.top.equalTo()(self.submitOrderView)
            make?.left.equalTo()(self.submitOrderView)
            make?.width.equalTo()(self.view)
            make?.right.equalTo()(self.submitOrderView.mas_right)
            make?.height.equalTo()((self.cellHeight * self.contentArray.count))
        })
        
        self.installmentSubOrderListView.mas_updateConstraints { (make) in
            make?.top.equalTo()(self.contentInfoTableView.mas_bottom)?.offset()(10)
            make?.left.equalTo()(self.submitOrderView)
            make?.width.equalTo()(UIScreen.main.bounds.width)
            make?.height.equalTo()(40)
        }
        
        self.useHybridCashierView.mas_updateConstraints { (make) in
            make?.top.equalTo()(self.installmentSubOrderListView.mas_bottom)?.offset()(10)
            make?.left.equalTo()(self.submitOrderView)
            make?.width.equalTo()(UIScreen.main.bounds.width)
            make?.height.equalTo()(40)
        }
        
        self.callbackURLView.mas_updateConstraints { (make) in
            make?.top.equalTo()(self.useHybridCashierView.mas_bottom)?.offset()(10)
            make?.left.equalTo()(self.submitOrderView)
            make?.width.equalTo()(UIScreen.main.bounds.width)
            make?.height.equalTo()(40)
        }
        
        self.callbackSettingView.mas_updateConstraints { (make) in
            make?.top.equalTo()(self.callbackURLView.mas_bottom)?.offset()(10)
            make?.left.equalTo()(self.chargePageURLView)
            make?.width.equalTo()(UIScreen.main.bounds.width)
            make?.height.equalTo()(40)
        }
        
        self.chargePageURLView.mas_updateConstraints { (make) in
            make?.top.equalTo()(self.callbackSettingView.mas_bottom)?.offset()(10)
            make?.left.equalTo()(self.submitOrderView)
            make?.width.equalTo()(UIScreen.main.bounds.width)
            make?.height.equalTo()(40)
        }
        
        self.DCEPModelView.mas_updateConstraints { (make) in
            make?.top.equalTo()(self.chargePageURLView.mas_bottom)?.offset()(10)
            make?.left.equalTo()(self.submitOrderView)
            make?.width.equalTo()(UIScreen.main.bounds.width)
            make?.height.equalTo()(40)
        }
        
        self.useLastOrderInfoView.mas_updateConstraints { (make) in
            make?.top.equalTo()(self.DCEPModelView.mas_bottom)?.offset()(10)
            make?.left.equalTo()(self.submitOrderView)
            make?.width.equalTo()(UIScreen.main.bounds.width)
            make?.height.equalTo()(40)
        }
        
        self.combineOrderView.mas_updateConstraints { (make) in
            make?.top.equalTo()(self.useLastOrderInfoView.mas_bottom)?.offset()(10)
            make?.left.equalTo()(self.submitOrderView)
            make?.width.equalTo()(UIScreen.main.bounds.width)
            make?.height.equalTo()(40)
        }
        
        self.creditMerchantView.mas_updateConstraints { (make) in
            make?.top.equalTo()(self.combineOrderView.mas_bottom)?.offset()(10)
            make?.left.equalTo()(self.submitOrderView)
            make?.width.equalTo()(UIScreen.main.bounds.width)
            make?.height.equalTo()(40)
        }
        
        self.extraStaticsView.mas_updateConstraints({ make in
             make?.top.equalTo()(self.creditMerchantView.mas_bottom)?.offset()(10)
             make?.left.equalTo()(self.submitOrderView)
             make?.width.equalTo()(UIScreen.main.bounds.width)
             make?.height.equalTo()(40)
         })
        
        self.cifDataView.mas_updateConstraints({ make in
             make?.top.equalTo()(self.extraStaticsView.mas_bottom)?.offset()(10)
             make?.left.equalTo()(self.submitOrderView)
             make?.width.equalTo()(UIScreen.main.bounds.width)
             make?.height.equalTo()(40)
         })
        
        self.oneclickPayOpenSettingView.mas_updateConstraints { (make) in
            make?.top.equalTo()(self.cifDataView.mas_bottom)?.offset()(10)
            make?.left.equalTo()(self.submitOrderView)
            make?.width.equalTo()(UIScreen.main.bounds.width)
            make?.height.equalTo()(40)
        }
         
        self.oneclickPayReconfirmAlertSettingView.mas_updateConstraints { (make) in
            make?.top.equalTo()(self.oneclickPayOpenSettingView.mas_bottom)?.offset()(10)
            make?.left.equalTo()(self.submitOrderView)
            make?.width.equalTo()(UIScreen.main.bounds.width)
            make?.height.equalTo()(40)
        }
        
        self.payDeferView.mas_updateConstraints { (make) in
            make?.top.equalTo()(self.oneclickPayReconfirmAlertSettingView.mas_bottom)?.offset()(10)
            make?.left.equalTo()(self.submitOrderView)
            make?.width.equalTo()(UIScreen.main.bounds.width)
            make?.height.equalTo()(40)
        }
        
        self.extraDataView.mas_updateConstraints({ make in
             make?.top.equalTo()(self.payDeferView.mas_bottom)?.offset()(10)
             make?.left.equalTo()(self.submitOrderView)
             make?.width.equalTo()(UIScreen.main.bounds.width)
             make?.height.equalTo()(40)
         })
        
        self.preCashierInfoView.mas_updateConstraints({ make in
             make?.top.equalTo()(self.extraDataView.mas_bottom)?.offset()(10)
             make?.left.equalTo()(self.submitOrderView)
             make?.width.equalTo()(UIScreen.main.bounds.width)
             make?.height.equalTo()(120)
         })
        
        self.prePromoPayInfoView.mas_updateConstraints({ make in
             make?.top.equalTo()(self.preCashierInfoView.mas_bottom)?.offset()(10)
             make?.left.equalTo()(self.submitOrderView)
             make?.width.equalTo()(UIScreen.main.bounds.width)
             make?.height.equalTo()(80)
         })
        
        
        self.appIdView.mas_updateConstraints({ make in
             make?.top.equalTo()(self.prePromoPayInfoView.mas_bottom)?.offset()(10)
             make?.left.equalTo()(self.submitOrderView)
             make?.width.equalTo()(UIScreen.main.bounds.width)
             make?.height.equalTo()(40)
         })
        
        self.payLaterView.mas_updateConstraints { (make) in
            make?.top.equalTo()(self.appIdView.mas_bottom)?.offset()(10)
            make?.left.equalTo()(self.submitOrderView)
            make?.width.equalTo()(UIScreen.main.bounds.width)
            make?.height.equalTo()(80)
        }
        
        let buttonWidth = (UIScreen.main.bounds.width - 60 ) / 2
        self.submitOrderButton.mas_updateConstraints({ make in
            make?.top.equalTo()(self.payLaterView.mas_bottom)?.offset()(20)
            make?.left.equalTo()(self.submitOrderView)?.offset()(20)
            make?.width.equalTo()(buttonWidth)
            make?.height.equalTo()(40)
        })
        
        self.onclickPayButton.mas_updateConstraints({ make in
            make?.top.equalTo()(self.payLaterView.mas_bottom)?.offset()(20)
            make?.left.equalTo()(self.submitOrderButton.mas_right)?.offset()(20)
            make?.width.equalTo()(buttonWidth)
            make?.height.equalTo()(40)
        })
        
        self.helloSubmitButton.mas_updateConstraints({ make in
            make?.top.equalTo()(self.submitOrderButton.mas_bottom)?.offset()(10)
            make?.left.equalTo()(self.submitOrderButton)
            make?.width.equalTo()(buttonWidth)
            make?.height.equalTo()(40)
        })
        
        self.h5SubmitButton.mas_updateConstraints({ make in
            make?.top.equalTo()(self.submitOrderButton.mas_bottom)?.offset()(10)
            make?.left.equalTo()(self.helloSubmitButton.mas_right)?.offset()(20)
            make?.width.equalTo()(buttonWidth)
            make?.height.equalTo()(40)
        })
        self.icButton.mas_updateConstraints({ make in
            make?.top.equalTo()(self.helloSubmitButton.mas_bottom)?.offset()(10)
            make?.left.width().height().mas_equalTo()(self.helloSubmitButton)
            make?.bottom.equalTo()(self.submitOrderView.mas_bottom)?.offset()(-10)

        })
        self.prePaymentButton.mas_updateConstraints({ make in
            make?.top.equalTo()(self.h5SubmitButton.mas_bottom)?.offset()(10)
            make?.left.equalTo()(self.icButton.mas_right)?.offset()(20)
            make?.width.equalTo()(buttonWidth)
            make?.height.equalTo()(40)

        })
        
        super.updateViewConstraints()
    }
    
    @objc func refreshUI() {
        self.contentInfoTableView.reloadData()
    }
    
    // MTCCashierDelegate
    func paymentDidFinish(_ cashier: MTCCashier!) {
        self.updateOutNoCell()
        TKAlertCenter.default().postAlert(withMessage: "ipayment:支付成功")
    }
    
    func paymentDidFail(_ cashier: MTCCashier!) {
        self.updateOutNoCell()
        TKAlertCenter.default().postAlert(withMessage: "ipayment:支付失败")
    }
    
    func paymentDidCancel(_ cashier: MTCCashier!, cancelType type: MTCPaymentCancelType) {
        TKAlertCenter.default().postAlert(withMessage: "ipayment:支付取消")
        self.updateOutNoCell()
    }
    
    // UITableViewDataSource
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return self.contentArray.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let contentInfo: PPContentInfo? = contentArray[indexPath.row]
        if (contentInfo?.identifier == "out_no") {
            let timeStamp = Int(Date().timeIntervalSince1970)
            contentInfo?.value = "\(timeStamp)"
        }
        return PPContentTextFieldCell.instance(of: contentInfo)!
    }
    
    // UITableViewDelegate
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return CGFloat(self.cellHeight)
    }
    
    // event
    @objc private func didClickedAddArgs(sender:Any) {
        let newInfo = PPContentInfo()
        newInfo.canEditOfTitle = true
        self.contentArray.append(newInfo)
        self.updateViewConstraints()
        self.contentInfoTableView?.reloadData()
        
    }
    
    @objc private func didClickSubmitOrderButton(sender: Any) {
        if SAKUserService.shared().isUserAvailable {
            self.generatePayOrder()
        } else {
            PPAccountController.defaultInstance().login(with: self, completedBlock: { [weak self] in
                if let strongSelf = self {
                    strongSelf.generatePayOrder()
                }
            })
        }
    }
    
    @objc private func didClickOneClickPayButton(sender: Any) {
        if SAKUserService.shared().isUserAvailable {
            self.generateOneClickPayOrder()
        } else {
            PPAccountController.defaultInstance().login(with: self, completedBlock: { [weak self] in
                if let strongSelf = self {
                    strongSelf.generateOneClickPayOrder()
                }
            })
        }
    }
    
    @objc private func didClickedHelloPayOrderButton(sender: Any) {
        if SAKUserService.shared().isUserAvailable {
            self.generateHelloPayOrder()
        } else {
            PPAccountController.defaultInstance().login(with: self, completedBlock: {[weak self] in
                if let strongSelf = self {
                    strongSelf.generateHelloPayOrder()
                }
            })
        }
    }
    
    @objc private func didClickedH5SubmitButton(sender: Any) {
        let resultString = SAKHorn.getCachedData(forType: "pay_demo_config");
        let resutlDictionary = SPKUtil.jsonString(toDictionary: resultString)
        let urlString: String = resutlDictionary!["h5SubmitOrderURL"] as! String;
        
        if SAKUserService.shared().isUserAvailable {
            SAKPortal.transfer(from: self, to: URL(string: urlString)!, completion: nil)
        } else {
            PPAccountController.defaultInstance().login(with: self, completedBlock: {[weak self] in
                if let strongSelf = self {
                    SAKPortal.transfer(from: strongSelf, to: URL(string: urlString)!, completion: nil)
                }
            })
        }
    }
    
    @objc private func didClickedIcSubmitButton(sender: Any) {
        let resultString = SAKHorn.getCachedData(forType: "pay_demo_config");
        let resutlDictionary = SPKUtil.jsonString(toDictionary: resultString)
        let urlString: String = resutlDictionary!["icSubmitOrderURL"] as! String;
        if SAKUserService.shared().isUserAvailable {
            SAKPortal.transfer(from: self, to: URL(string: urlString)!, completion: nil)
        } else {
            PPAccountController.defaultInstance().login(with: self, completedBlock: {[weak self] in
                if let strongSelf = self {
                    SAKPortal.transfer(from: strongSelf, to: URL(string: urlString)!, completion: nil)
                }
            })
        }
    }
    
    @objc private func didClickedPrePaymentButton(sender: Any) {
        let urlString = "meituanpayment://www.meituan.com/mrn?mrn_biz=pay&mrn_entry=mrn-pay-sakbus-caller-mrn&mrn_component=sakbus-caller-mrn";
        if SAKUserService.shared().isUserAvailable {
            SAKPortal.transfer(from: self, to: URL(string: urlString)!, completion: nil)
        } else {
            PPAccountController.defaultInstance().login(with: self, completedBlock: {[weak self] in
                if let strongSelf = self {
                    SAKPortal.transfer(from: strongSelf, to: URL(string: urlString)!, completion: nil)
                }
            })
        }
        
    }
    
    func generatePayOrder() {
        if self.useLastOrderInfoView.switchView.isOn {
            let url = UserDefaults.standard.url(forKey: "lastSubmitOrderInfo")
            if url != nil && url!.absoluteString.count > 0 {
                SAKPortal.transfer(from: self, to: url!, completion: nil)
            } else {
                SPKToastCenter.default().toast(withMessage: "获取存储的提单信息异常")
                UserDefaults.standard.removeObject(forKey: "lastSubmitOrderInfo")
                self.useLastOrderInfoView.switchView.isOn = false
                self.useLastOrderInfoView.switchView.isEnabled = false
                self.useLastOrderInfoView.titleField.text = "请先下一单"
            }
            return
        }
        
        _ = SPKNoneMaskBezelActivityView(for: self.view, withLabel: nil, width: 0)
        
        let cifJsonStr = getCif()
        
        if !self.combineOrderView.switchView.isOn {
            (PPService.defaultInstance() as? PPService)?.generatePayOrder(withContentArray: contentArray, finished: {
                (_ paymentRequest: MTCPaymentRequest?, _ error: CIPError?) -> Void in
                self.updateOutNoCell()
                SPKNoneMaskBezelActivityView.remove()
                if let err = error {
                    // TODO: 张扬，此处需要处理401~405登录风控错误，引导用户退出并重新登录
                    TKAlertCenter.default().postAlertWithError(err)
                } else {
                    
                    var extraData = NSMutableDictionary()
                    let payLaterSwitchOn = self.payLaterView.switchView.isOn
                    let payDeferSwitchOn = self.payDeferView.switchView.isOn
                    let extraDataSwitchOn = self.extraDataView.switchView.isOn
                    var cashierType = ""
                    if (payLaterSwitchOn) {
                        var str = self.payLaterView.titleField.text!
                        str = str.replacingOccurrences(of: " ", with: "", options: .literal, range: nil)
                        str = str.replacingOccurrences(of: "\n", with: "", options: .literal, range: nil)
                        extraData["guide_plan_infos"] = str
                        cashierType = "payLater"
                    } else if (payDeferSwitchOn) {
                        cashierType = "pay_defer_sign"
                    } else {
                        cashierType = "traditionpay"
                    }
                    
                    if ((paymentRequest?.cashierType) != nil) {
                        cashierType = paymentRequest?.cashierType ?? "traditionpay"
                    }
                    
                    if self.preCashierInfoView.switchView.isOn {
                        var preCashierInfoStr = self.preCashierInfoView.titleField.text!
                        preCashierInfoStr = preCashierInfoStr.replacingOccurrences(of: " ", with: "", options: .literal, range: nil)
                        preCashierInfoStr = preCashierInfoStr.replacingOccurrences(of: "\n", with: "", options: .literal, range: nil)
                        let dic:[String:Any]? = preCashierInfoStr.spk_JSONDictionary() as? [String:Any]
                        if dic != nil {
                            extraData.addEntries(from: dic!)
                        }
                    }
                    
                    if self.prePromoPayInfoView.switchView.isOn {
                        var prePromoPayInfoStr = self.prePromoPayInfoView.titleField.text!
                        prePromoPayInfoStr = prePromoPayInfoStr.replacingOccurrences(of: " ", with: "", options: .literal, range: nil)
                        prePromoPayInfoStr = prePromoPayInfoStr.replacingOccurrences(of: "\n", with: "", options: .literal, range: nil)
                        let dic:[String:Any]? = prePromoPayInfoStr.spk_JSONDictionary() as? [String:Any]
                        if dic != nil {
                            extraData.addEntries(from: dic!)
                        }
                    }
                    
                    // url query 额外参数
                    if self.appIdView.switchView.isOn,
                       let appIdStr = self.appIdView.titleField.text,
                       appIdStr.count > 0 {
                        extraData["app_id"] = appIdStr
                    }
                    
                    var targetExtraDataStr = ""
                    if let extraDataEncodedString = extraData.spk_JSONString().cipf_URLEncoded() {
                        targetExtraDataStr = "&extra_data=\(extraDataEncodedString)"
                    }
                    
                    var targetExtraStaticsStr = ""
                    
                    if self.extraStaticsView.switchView.isOn {
                        var extraStaticsTextStr = self.extraStaticsView.titleField.text ?? "Text is Default"
                        if extraStaticsTextStr.count == 0 {
                            extraStaticsTextStr = "Text is Default"
                        }
                        let extraStaticsDict = NSMutableDictionary()
                        extraStaticsDict["operation_source"] = extraStaticsTextStr
                        let extraStaticsJSONString = extraStaticsDict.spk_JSONString() ?? "{\"operation_source\":\"Text is Default\"}"
                        if let extraStaticsEncodedString = extraStaticsJSONString.cipf_URLEncoded() {
                            targetExtraStaticsStr = "&extra_statics=\(extraStaticsEncodedString)"
                        }
                    }
                    
                    
                    var cifData = NSMutableDictionary()
                    if self.cifDataView.switchView.isOn {
                        var cifDataStr = self.cifDataView.titleField.text!
                        cifDataStr = cifDataStr.replacingOccurrences(of: " ", with: "", options: .literal, range: nil)
                        cifDataStr = cifDataStr.replacingOccurrences(of: "\n", with: "", options: .literal, range: nil)
                        let dic:[String:Any]? = cifDataStr.spk_JSONDictionary() as? [String:Any]
                        if dic != nil {
                            cifData = (dic! as NSDictionary).mutableCopy() as! NSMutableDictionary
                        }
                    }
                    
                    var targetCifStr = ""
                    if let extraDataEncodedString = cifData.spk_JSONString().cipf_URLEncoded() {
                        targetCifStr = "&cif=\(extraDataEncodedString)"
                    }
                    
                    MTCCashier.default().delegate = self
                    let callbackURL = self.callbackURLView.switchView.isOn ? self.callbackURLView.titleField.text ?? "" : ""
                    
                    var urlString = ""
                    
                    if self.useHybridCashierView.switchView.isOn {
                        // 进入 Hybrid 收银台
                        urlString = "meituanpayment://cashier/launch?trade_number=\(paymentRequest?.tradeNumber ?? "")&pay_token=\(paymentRequest?.payToken ?? "")&callback_url=\(callbackURL)&merchant_no=111" + targetExtraDataStr + targetExtraStaticsStr + cifJsonStr + targetCifStr
                    } else {
                        // callbackURL 和 delegate 同时回调
                        let callbackTogether = self.callbackSettingView.switchView.isOn ? true : false
                        urlString = "meituanpayment://cashier/launch?trade_number=\(paymentRequest?.tradeNumber ?? "")&pay_token=\(paymentRequest?.payToken ?? "")&callback_url=\(callbackURL)\(callbackTogether ? "&is_callback_both=1" : "")&cashier_type=\(cashierType)" + targetExtraDataStr + targetExtraStaticsStr + cifJsonStr + targetCifStr
                    }
                    let url = URL(string: urlString)!
                    UserDefaults.standard.set(url, forKey: "lastSubmitOrderInfo")
                    SAKPortal.transfer(from: self, to: url, completion: nil)
                }
            })
        } else {
            // 打开合单时 请求合单接口
            (PPService.defaultInstance() as? PPService)?.generateCombinePayOrder(withContentArray: contentArray, finished: {
                (_ paymentRequest: MTCPaymentRequest?, _ error: CIPError?) -> Void in
                self.updateOutNoCell()
                SPKNoneMaskBezelActivityView.remove()
                if let err = error {
                    // TODO: 张扬，此处需要处理401~405登录风控错误，引导用户退出并重新登录
                    TKAlertCenter.default().postAlertWithError(err)
                } else {
                    MTCCashier.default().delegate = self
                    let callbackURL = self.callbackURLView.switchView.isOn ? self.callbackURLView.titleField.text ?? "" : ""
                    // callbackURL 和 delegate 同时回调
                    let callbackTogether = self.callbackSettingView.switchView.isOn ? true : false
                    let urlString = "meituanpayment://cashier/launch?trade_number=\(paymentRequest?.tradeNumber ?? "")&pay_token=\(paymentRequest?.payToken ?? "")&callback_url=\(callbackURL)\(callbackTogether ? "&is_callback_both=1" : "")" + cifJsonStr
                    let url = URL(string: urlString)!
                    UserDefaults.standard.set(url, forKey: "lastSubmitOrderInfo")
                    SAKPortal.transfer(from: self, to: url, completion: nil)
                }
            })
        }
    }
    
    func generateOneClickPayOrder() {
        _ = SPKNoneMaskBezelActivityView(for: self.view, withLabel: nil, width: 0)
        
        let transScene = PPContentInfo()
        transScene.identifier = "tradeScene"
        transScene.value = "7"
        var param = contentArray
        param.append(transScene)
        
        let cifJsonStr = getCif()
        
        (PPService.defaultInstance() as? PPService)?.generatePayOrder(withContentArray: param, finished: {
            (_ paymentRequest: MTCPaymentRequest?, _ error: CIPError?) -> Void in
            self.updateOutNoCell()
            SPKNoneMaskBezelActivityView.remove()
            if let err = error {
                TKAlertCenter.default().postAlertWithError(err)
            } else {
                MTCCashier.default().delegate = self
                let callbackURL = self.callbackURLView.switchView.isOn ? self.callbackURLView.titleField.text ?? "" : ""
                // callbackURL 和 delegate 同时回调
                let callbackTogether = self.callbackSettingView.switchView.isOn ? true : false
                let cashierType = "oneclickpay"
                let extraData = NSDictionary(dictionary: ["reconfirm" : self.oneclickPayReconfirmAlertSettingView.switchView.isOn,
                                                          "open_oneclickpay" : self.oneclickPayOpenSettingView.switchView.isOn,
                                                          "serialCode" : "\(Int(Date().timeIntervalSince1970 * 1000))",
                                                              "app_id" : self.appIdView.switchView.isOn ? self.appIdView.titleField.text ?? "" : "",
                                                     "guide_plan_infos": self.payLaterView.switchView.isOn ? self.payLaterView.titleField.text ?? "" : ""])
             
                let extraDataJSONString = extraData.spk_JSONString().cipf_URLEncoded()
                
                var targetExtraStaticsStr = ""
                
                if self.extraStaticsView.switchView.isOn {
                    var extraStaticsTextStr = self.extraStaticsView.titleField.text ?? "Text is Default"
                    if extraStaticsTextStr.count == 0 {
                        extraStaticsTextStr = "Text is Default"
                    }
                    let extraStaticsDict = NSMutableDictionary()
                    extraStaticsDict["operation_source"] = extraStaticsTextStr
                    let extraStaticsJSONString = extraStaticsDict.spk_JSONString() ?? "{\"operation_source\":\"Text is Default\"}"
                    if let extraStaticsEncodedString = extraStaticsJSONString.cipf_URLEncoded() {
                        targetExtraStaticsStr = "&extra_statics=\(extraStaticsEncodedString)"
                    }
                }
                
                var merchantNo = ""
                for info in param {
                    if (info.identifier == "iph_pay_merchant_no") {
                        merchantNo = info.value
                    }
                }
                
                let urlString = "meituanpayment://cashier/launch?trade_number=\(paymentRequest?.tradeNumber ?? "")&pay_token=\(paymentRequest?.payToken ?? "")&callback_url=\(callbackURL)\(callbackTogether ? "&is_callback_both=1" : "")&cashier_type=\(cashierType)&merchant_no=\(merchantNo ?? "")&extra_data=\(extraDataJSONString!)"+targetExtraStaticsStr + cifJsonStr
                
                let url = URL(string: urlString)!
                UserDefaults.standard.set(url, forKey: "lastSubmitOrderInfo")
                SAKPortal.transfer(from: self, to: url, completion: nil)
            }
        })
    }
    
    func generateHelloPayOrder() {
        _ = SPKNoneMaskBezelActivityView(for: self.view, withLabel: nil, width:0)
        (PPService.defaultInstance() as? PPService)?.generateHelloPay(withContentArray: contentArray, finished: { [unowned self]
            (_ paymentRequest: MTCPaymentRequest?, _ error: CIPError?) -> Void in
            self.updateOutNoCell()
            SPKNoneMaskBezelActivityView.remove()
            if let err = error {
                TKAlertCenter.default().postAlertWithError(err)
            } else {
                self.helloPayController = PPHelloPayController()
                self.helloPayController?.submitViewController = self
                MTPPayment.default().delegate = self.helloPayController
                let callbackURL = self.callbackURLView.switchView.isOn ? self.callbackURLView.titleField.text ?? "" : ""
                let nb_source = "special_recharge"
                let urlString = "meituanpayment://conchpay/launch?trans_id=\(paymentRequest?.tradeNumber ?? "")&pay_token=\(paymentRequest?.payToken ?? "")&cashier_type=hello&nb_source=\(nb_source)&callback_url=\(callbackURL)"
                let url = URL(string: urlString)!
                UserDefaults.standard.set(url, forKey: "lastSubmitOrderInfo")
                SAKPortal.transfer(from: self, to: url, completion: nil)
            }
        })
    }
    
    func generateMeituanPayOrder() {
        _ = SPKNoneMaskBezelActivityView(for: self.view, withLabel: nil, width:0)
        (PPService.defaultInstance() as? PPService)?.generateHelloPay(withContentArray: contentArray, finished: {
            (_ paymentRequest: MTCPaymentRequest?, _ error: CIPError?) -> Void in
            self.updateOutNoCell()
            SPKNoneMaskBezelActivityView.remove()
            if let err = error {
                TKAlertCenter.default().postAlertWithError(err)
            } else {
                // TODO:去美团App完成支付
                if (MeituanPaySDK.isSupportMeituanPay()) {
                    MeituanPaySDK.startMeituanPay(withTradeNumber:(paymentRequest?.tradeNumber ?? ""), payToken:(paymentRequest?.payToken ?? ""), completion:{
                        (result : [AnyHashable : Any]?, error: Error?) -> Void in
                        
                        TKAlertCenter.default().postAlert(withMessage: "美团支付成功")
                        
                    })
                }
            }
        })
    }
    
    func updateOutNoCell() {
        for (idx, contentInfo) in self.contentArray.enumerated() {
            if contentInfo.identifier == "out_no" {
                let indexPath = IndexPath(row: idx, section: 0)
                self.contentInfoTableView.reloadRows(at: [indexPath], with: .none)
                break
            }
        }
    }
    
    func getCif() -> String {
        var cif:String = self.combineOrderView.titleField.text!;
        let combinDic:[String: String] = ["ct": "standard-cashier"]
        let text = "输入参数例如:"+(combinDic as NSDictionary).spk_JSONString()
        if cif == text {
            cif = "";
        }
        var cifJsonStr = ""
        if !cif.isEmpty {
            if let cifEncodedString = cif.cipf_URLEncoded() {
                cifJsonStr = "&cif=\(cifEncodedString)"
            }
        }
        return cifJsonStr
    }
    
    // UIScrollViewDelegate
    func scrollViewWillBeginDragging(_ scrollView: UIScrollView) {
        self.view.endEditing(true)
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        let url = UserDefaults.standard.url(forKey: "lastSubmitOrderInfo")
        if url != nil{
            self.useLastOrderInfoView.switchView.isEnabled = true
            self.useLastOrderInfoView.titleField.text = "使用上一次的交易单信息"
        } else {
            self.useLastOrderInfoView.switchView.isEnabled = false
            self.useLastOrderInfoView.titleField.text = "请先下一单"
        }
    }
}
