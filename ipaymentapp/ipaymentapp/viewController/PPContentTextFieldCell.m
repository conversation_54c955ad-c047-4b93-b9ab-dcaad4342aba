//
//  PPContentTextFieldCell.m
//  SAKCashier
//
//  Created by sunhl on 15/3/27.
//  Copyright (c) 2015年 meituan. All rights reserved.
//

#import "PPContentTextFieldCell.h"
#import "SAKUIKitMacros.h"
#import "View+MASAdditions.h"
#import "UIColor+Addition.h"
#import "PPContentInfo.h"


@interface PPContentTextFieldCell () <UITextFieldDelegate>

@property (nonatomic, strong) UIView *separatorLine;

@end


@implementation PPContentTextFieldCell

+ (PPContentTextFieldCell *)instanceOfContentInfo:(PPContentInfo *)contentInfo
{
    PPContentTextFieldCell *cell = [[PPContentTextFieldCell alloc] init];
    
    cell.contentInfo = contentInfo;
    
    return cell;
}

- (id)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.backgroundColor = [UIColor clearColor];
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        
        _separatorLine = ({
            UIView *view = [[UIView alloc] init];
            view.backgroundColor = [UIColor generalSeparatorLineColor];
            view;
        });
        [self.contentView addSubview:_separatorLine];
        
        _titleField = ({
            UITextField *textField = [[UITextField alloc] init];
            textField.tag = 101;
            textField.textColor = [UIColor generalLabelTextColor];
            textField.font = [UIFont systemFontOfSize:14];
            textField.delegate = self;
            textField.autoresizingMask = UIViewAutoresizingFlexibleWidth;
            textField.autocapitalizationType = UITextAutocapitalizationTypeNone;
            textField.contentVerticalAlignment = UIControlContentVerticalAlignmentCenter;
            textField.returnKeyType = UIReturnKeyNext;
            textField.autocorrectionType = UITextAutocorrectionTypeNo;
            [textField addTarget:self action:@selector(textDidChange:) forControlEvents:UIControlEventEditingChanged];
            [textField addTarget:self action:@selector(textDidBegin:) forControlEvents:UIControlEventEditingDidBegin];
            [textField addTarget:self action:@selector(textDidEnd:) forControlEvents:UIControlEventEditingDidEnd];
            textField;
        });
        
        [self.contentView addSubview:_titleField];
        
        _textField = ({
            UITextField *textField = [[UITextField alloc] init];
            textField.tag = 102;
            textField.textColor = [UIColor colorWithWhite:0.2 alpha:1];
            textField.font = [UIFont systemFontOfSize:14];
            textField.delegate = self;
            textField.autoresizingMask = UIViewAutoresizingFlexibleWidth;
            textField.autocapitalizationType = UITextAutocapitalizationTypeNone;
            textField.contentVerticalAlignment = UIControlContentVerticalAlignmentCenter;
            textField.returnKeyType = UIReturnKeyNext;
            textField.autocorrectionType = UITextAutocorrectionTypeNo;
            [textField addTarget:self action:@selector(textDidChange:) forControlEvents:UIControlEventEditingChanged];
            textField;
        });
        [self.contentView addSubview:_textField];
        
        [self setNeedsUpdateConstraints];
    }
    return self;
}

- (void)setContentInfo:(PPContentInfo *)contentInfo
{
    _contentInfo = contentInfo;
    
    self.textField.placeholder = contentInfo.promptString;
    self.textField.enabled = !contentInfo.readonly;
    self.textField.text = [contentInfo.value length] ? contentInfo.value : contentInfo.defaultValue;
    
    self.titleField.placeholder = contentInfo.titlePromptString;
    self.titleField.text = contentInfo.title;
    self.titleField.enabled = contentInfo.canEditOfTitle;
    if (self.titleField.enabled) {
        self.titleField.textColor = [UIColor redColor];
    }
}

- (void)updateConstraints
{
    [self.contentView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
        make.size.equalTo(self);
    }];
    
    [self.separatorLine mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.contentView);
        make.height.equalTo(@(1));
        make.bottom.equalTo(self.contentView.mas_bottom);
    }];
    
    [self.titleField mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(12);
        make.top.equalTo(self.contentView.mas_top);
        make.width.equalTo(@90);
        make.height.equalTo(self.contentView);
    }];
    
    [self.textField mas_updateConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.titleField.mas_right).offset(9);
        make.right.equalTo(self.contentView.mas_right).offset(-7);
        make.top.equalTo(self.contentView);
        make.height.equalTo(self.contentView);
        make.bottom.equalTo(self.contentView.mas_bottom);
    }];

    [super updateConstraints];
}

- (CGSize)intrinsicContentSize
{
    return CGSizeMake(SCREEN_WIDTH, 45);
}

- (void)textDidBegin:(UITextField *)textField
{
    if (textField.tag == 101) {
        textField.text = self.contentInfo.identifier;
    } else {
        // do nothing
    }
}

- (void)textDidChange:(UITextField *)textField
{
    if (textField.tag == 102) {
        self.contentInfo.value = textField.text;
    } else if (textField.tag == 101) {        
        self.contentInfo.identifier = textField.text;
        if (self.contentInfo.identifier.length) {
            self.contentInfo.title = [self.contentInfo.identifier stringByAppendingString:@":"];
        } else
        {
            self.contentInfo.title = @"";
        }
    } else {
        // do nothing
    }
}

- (void)textDidEnd:(UITextField *)textField
{
    if (textField.tag == 101) {
        textField.text = self.contentInfo.title;
    } else {
        // do nothing
    }
}

@end
