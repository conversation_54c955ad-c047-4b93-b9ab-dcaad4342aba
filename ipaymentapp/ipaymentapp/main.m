//
//  main.m
//  ipaymentapp
//
//  Created by wang<PERSON> on 15/6/24.
//  Copyright (c) 2015年 Meituan.com. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "AppDelegate.h"

NSTimeInterval time_main_ca;        // record main time, use CACurrentMediaTime, unit:ms
NSTimeInterval time_main_datestamp;

int main(int argc, char * argv[]) {
    time_main_ca = CACurrentMediaTime() * 1000.0f;
    time_main_datestamp = [[NSDate date] timeIntervalSince1970] * 1000.0;
    @autoreleasepool {
        return UIApplicationMain(argc, argv, nil, NSStringFromClass([AppDelegate class]));
    }
}
