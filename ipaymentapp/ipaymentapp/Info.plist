<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>UIUserInterfaceStyle</key>
	<string>Light</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>zh_CN</string>
	<key>CFBundleDisplayName</key>
	<string>美团支付稳定测试包</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>12.27.1261</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>imeituan</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>ipaymentapp</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>weixin</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wxa552e31d6839de85</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>mtcalipay</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>meituancashier0010</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>meituanpayment</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>meituanpayment</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>mtcunionpay</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>meituanunionpay0010</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>cippaymentqqwallet</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>cippaymentqqwallet0010</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>None</string>
			<key>CFBundleURLName</key>
			<string></string>
			<key>CFBundleURLSchemes</key>
			<array/>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>meituanpaysdk</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>cipmeituanpaysdk0001</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>mtcunionflashpay</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>meituanunionflashpay0010</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>mtdceppay</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>meituandceppay</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>650</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>weixinULAPI</string>
		<string>dcep</string>
		<string>yghsh</string>
		<string>cmblife</string>
		<string>wechat</string>
		<string>weixin</string>
		<string>alipay</string>
		<string>mqq</string>
		<string>mqqwallet</string>
		<string>uppaysdk</string>
		<string>uppaywallet</string>
		<string>uppayx1</string>
		<string>uppayx2</string>
		<string>uppayx3</string>
		<string>imeituan</string>
		<string>alipayhk</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NFCReaderUsageDescription</key>
	<string>NFC is needed to unlock doors</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>让我用下相机，吼不吼啊</string>
	<key>NSContactsUsageDescription</key>
	<string>让我看下联系人，吼不吼啊</string>
	<key>NSFaceIDUsageDescription</key>
	<string>允许使用面容 ID，以使用面容 ID 验证</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>开启定位</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>开启定位</string>
	<key>NSLocationUsageDescription</key>
	<string>开启定位</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>开启定位</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>允许使用相册</string>
	<key>UIBackgroundModes</key>
	<array/>
	<key>UILaunchImages</key>
	<array>
		<dict>
			<key>UILaunchImageName</key>
			<string>Default-568</string>
			<key>UILaunchImageSize</key>
			<string>{320, 568}</string>
		</dict>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIAppFonts</key>
	<array>
		<string>MeituanDigitalType-SemiBold.TTF</string>
		<string>MeituanDigitalType-Bold.TTF</string>
		<string>MeituanDigitalType-Medium.TTF</string>
		<string>MeituanDigitalType-Regular.TTF</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
