//
//  NSObject+MLeaksFinderSwitch.m
//  ipaymentapp
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2018/2/28.
//  Copyright © 2018年 Meituan.com. All rights reserved.
//

#import "NSObject+MLeaksFinderSwitch.h"
#import "CIPSwizzle.h"
#import "NSObject+SAKMemoryLeak.h"

BOOL MLeaksFinderSwitchTurnoff = NO; // 默认打开检测

/**
 MLeaksFinder 开关
 */
@implementation NSObject (MLeaksFinderSwitch)

+ (void)load
{
    [self cipf_SwizzleMethod:@selector(willDealloc)
                  withMethod:@selector(ipayment_defaultWillDealloc)
                       error:nil];
}

- (BOOL)ipayment_defaultWillDealloc
{
    if (MLeaksFinderSwitchTurnoff) {
        return NO;
    } else {
        return [self ipayment_defaultWillDealloc];
    }
}

@end
