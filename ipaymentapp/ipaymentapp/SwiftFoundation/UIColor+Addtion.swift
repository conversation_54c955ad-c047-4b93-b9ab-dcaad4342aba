//
//  UIColor+Addtion.swift
//  sHotelMerchant
//
//  Created by 张宇 on 14-11-13.
//  Copyright (c) 2014年 sankuai. All rights reserved.
//

import Foundation

extension UIColor {
    class func HEXCOLOR(hexValue:Int) -> UIColor{
        return HEXACOLOR(hexValue: hexValue, alpha: 1.0)
    }
    class func HEXACOLOR(hexValue:Int, alpha:CGFloat) -> UIColor{
        return UIColor(red: ((CGFloat)((hexValue & 0xFF0000) >> 16)) / 255.0 , green: ((CGFloat)((hexValue & 0xFF00) >> 8)) / 255.0, blue: ((CGFloat)(hexValue & 0xFF)) / 255.0, alpha: alpha)
    }
}
