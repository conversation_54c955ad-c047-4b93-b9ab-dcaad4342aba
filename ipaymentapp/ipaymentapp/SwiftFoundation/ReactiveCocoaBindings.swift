//
//  ReactiveCocoaBindings.swift
//  MeituanMovie
//
//  Created by p<PERSON><PERSON> on 15/5/12.
//  Copyright (c) 2015年 sankuai. All rights reserved.
//

import Foundation

// So I expect the ReactiveCocoa fellows to figure out a replacement API for the RAC macro.
// Currently, I don't see one there, so we'll use this solution until an official one exists.

// Pulled from http://www.scottlogic.com/blog/2014/07/24/mvvm-reactivecocoa-swift.html

public struct RAC  {
    var target: NSObject
    var keyPath: String
    var nilValue: AnyObject?
    
    public init(_ target: NSObject, _ keyPath: String, nilValue: AnyObject? = nil) {
        self.target = target
        self.keyPath = keyPath
        self.nilValue = nilValue
    }
    
    func assignSignal(signal : RACSignal) -> RACDisposable {
        return signal.setKeyPath(self.keyPath, on: self.target, nilValue: self.nilValue)
    }
}

precedencegroup bindingPrecedence {
    associativity: right
    higherThan: AdditionPrecedence
}

infix operator <~: bindingPrecedence

@discardableResult
public func <~ (rac: RAC, signal: RACSignal) -> RACDisposable {
    return signal ~> rac
}

@discardableResult
public func ~> (signal: RACSignal, rac: RAC) -> RACDisposable {
    return rac.assignSignal(signal: signal)
}

@discardableResult
public func RACObserve(target: NSObject!, keyPath: String) -> RACSignal {
    return target.rac_values(forKeyPath: keyPath, observer: target)
}
