//
//  Font+Addtion.swift
//  sHotelMerchant
//
//  Created by 张宇 on 14-11-13.
//  Copyright (c) 2014年 sankuai. All rights reserved.
//

import Foundation

extension UIFont {
    class func Font(value: CGFloat) -> UIFont {
        return UIFont.systemFont(ofSize: value)
    }
    
    class func BoldFont(value: CGFloat) -> UIFont {
        return UIFont.boldSystemFont(ofSize: value)
    }
    
    class func ItalicFont(value: CGFloat) -> UIFont {
        return UIFont.italicSystemFont(ofSize: value)
    }
}
