//
//  PPOrder.h
//  SAKCashier
//
//  Created by sunhl on 15/3/31.
//  Copyright (c) 2015年 meituan. All rights reserved.
//

#import "SAKDomainObject.h"

@interface PPOrder : SAKDomainObject

@property (nonatomic, copy) NSString *orderID;
@property (nonatomic, copy) NSString *partnerID;
@property (nonatomic, strong) NSNumber *payType;
@property (nonatomic, strong) NSNumber *status;
@property (nonatomic, strong) NSDate *orderTime;
@property (nonatomic, strong) NSDate *payTime;
@property (nonatomic, copy) NSString *title;
@property (nonatomic, copy) NSString *money;
@property (nonatomic, copy) NSString *body;
@property (nonatomic, strong) NSNumber *theID;
@property (nonatomic, copy) NSString *tradeNo;

@end



