//
//  SAKBaseModel+CashierApp.h
//  SAKCashier
//
//  Created by wangfeng on 15/4/30.
//  Copyright (c) 2015年 meituan. All rights reserved.
//

#import "SAKBaseModel.h"

@interface SAKBaseModel (CashierApp)

- (MTHTTPRequestOperation *)postToCashierAppBackendPath:(NSString *)path
                                       withURLParameters:(NSDictionary *)URLParameterDictionary
                                          bodyParameters:(NSDictionary *)bodyParameterDictionary
                                                finished:(SAKModelRequestCallback)finishedCallback;

- (MTHTTPRequestOperation *)getFromCashierAppBackendPath:(NSString *)path
                                       withURLParameters:(NSDictionary *)URLParameterDictionary
                                                finished:(SAKModelRequestCallback)finishedCallback;

@end
