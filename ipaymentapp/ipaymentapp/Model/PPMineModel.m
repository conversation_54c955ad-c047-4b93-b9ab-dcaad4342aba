//
//  PPMineModel.m
//  ipaymentapp
//
//  Created by zzMBP on 16/7/25.
//  Copyright © 2016年 Meituan.com. All rights reserved.
//

#import "PPMineModel.h"

static NSString * const kURLString = @"https://pay.meituan.com/api/my/wallet/entrance";

@implementation PPMineModel

- (RACSignal *)fetchMeituanPaymentEntranceInfoSignal
{
    @weakify(self)
    return [RACSignal createSignal:^RACDisposable *(id<RACSubscriber> subscriber) {
        @strongify(self)
        [self fetchMeituanPayNameInfoFinished:^(NSDictionary *nameInfoDictionary, CIPError *error) {
            if (error) {
                [subscriber sendNext:nameInfoDictionary];
                [subscriber sendError:error];
            } else {
                [subscriber sendNext:nameInfoDictionary];
                [subscriber sendCompleted];
            }
        }];
        return nil;
    }];
}

- (void)fetchMeituanPayNameInfoFinished:(void (^)(NSDictionary *, CIPError *))finished
{
    NSMutableDictionary *parmas = [[NSMutableDictionary alloc] init];
    [parmas setObject:@"paymentapp" forKey:@"nb_app"];
    [parmas setObject:@"1.0" forKey:@"bl_version"];
    [self postToURL:kURLString withURLParameters:parmas bodyParameters:nil dataEncoding:SAKModelDataJSONEncoding finished:^(NSDictionary *dict, NSURLRequest *request, CIPError *error) {
        if (!error && [dict isKindOfClass:[NSDictionary class]] && dict[@"data"]) {
            NSDictionary *infoDict = dict[@"data"];
            NSString *plistPath = [[NSBundle mainBundle] pathForResource:@"WalletEntranceInfo" ofType:@"plist"];
            [infoDict writeToFile:plistPath atomically:YES];
            @within_main_thread(finished, infoDict, error);
        } else {
            NSString *plistPath = [[NSBundle mainBundle] pathForResource:@"WalletEntranceInfo" ofType:@"plist"];
            NSDictionary *infoDict = [[NSDictionary alloc] initWithContentsOfFile:plistPath];
            @within_main_thread(finished, infoDict, error);
        }
    }];
}

@end
