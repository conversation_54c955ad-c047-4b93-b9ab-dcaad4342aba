//
//  UIColor+Addition.h
//  ipaymentapp
//
//  Created by xiaoyangyuxuan on 2020/7/9.
//  Copyright © 2020 Meituan.com. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface UIColor (IPayment)

@property (class, nonatomic, readonly) UIColor *generalBackgroundColor;
@property (class, nonatomic, readonly) UIColor *grayBackgroundColor;
@property (class, nonatomic, readonly) UIColor *generalSeparatorLineColor;
@property (class, nonatomic, readonly) UIColor *generalLabelTextColor;
@property (class, nonatomic, readonly) UIColor *grayLabelTextColor;
@property (class, nonatomic, readonly) UIColor *blueLabelTextColor;
@property (class, nonatomic, readonly) UIColor *orangeLabelTextColor;

@end
