//
//  PPContentInfo.h
//  SAKCashier
//
//  Created by sunhl on 15/3/27.
//  Copyright (c) 2015年 meituan. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface PPContentInfo : NSObject

@property (nonatomic, copy) NSString *identifier;     // identifier
@property (nonatomic, copy) NSString *title;          // 标题
@property (nonatomic, copy) NSString *titlePromptString;   // 标题提示文案
@property (nonatomic, assign) BOOL  canEditOfTitle;   // 标题是否只读, 默认为不可编辑
@property (nonatomic, copy) NSString *promptString;   // 提示文案
@property (nonatomic, copy) NSString *defaultValue;   // 默认值
@property (nonatomic, copy) NSString *value;          // 内容
@property (nonatomic, assign) BOOL readonly;            // 只读
@property (nonatomic, assign) BOOL optional;            // 可选

@end
