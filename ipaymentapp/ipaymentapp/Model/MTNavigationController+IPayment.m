//
//  MTNavigationController+IPayment.m
//  imeituan
//
//  Created by 陈晓亮 on 13-5-15.
//  Copyright (c) 2013年 Meituan.com. All rights reserved.
//

#import "SAKBarButtonItem+SPKCustom.h"
#import "MTNavigationController.h"
#import "UIImage+SAKUI.h"
#import <objc/runtime.h>
#import "SAKBarButtonItem+SPKCustom.h"
#import <SAKSwizzle/SWLSwizzle.h>

@implementation MTNavigationController (IPayment)

swl_swizzleInstanceMethod([MTNavigationController class], @selector(backItemWithTarget:action:), @selector(ipay_backItemWithTarget:action:))
swl_swizzleInstanceMethod([MTNavigationController class], @selector(navigationBarImage), @selector(ipay_navigationBarImage))

//+ (void)load
//{
//    method_exchangeImplementations(class_getInstanceMethod([self class], @selector(backItemWithTarget:action:)),
//                                   class_getInstanceMethod([self class], @selector(ipay_backItemWithTarget:action:)));
//    method_exchangeImplementations(class_getInstanceMethod([self class], @selector(navigationBarImage)),
//                                   class_getInstanceMethod([self class], @selector(ipay_navigationBarImage)));
//}

- (UIBarButtonItem *)ipay_backItemWithTarget:(id)target action:(SEL)action
{
    return [SAKBarButtonItem spk_backBarButtonItemSupportUIConfigure:YES target:target action:action];
}

- (UIImage *)ipay_navigationBarImage
{
    return [[UIImage imageNamed:@"bg_navigationBar_white"] sakui_resizableImage];
}

@end
