//
//  PPWalletInfo.m
//  ipaymentapp
//
//  Created by zzMBP on 16/7/25.
//  Copyright © 2016年 Meituan.com. All rights reserved.
//

#import "PPWalletEntranceInfo.h"

@implementation PPWalletEntranceInfo

+ (NSDictionary *)predicateDictionary
{
    return @{@keypath(PPWalletEntranceInfo.new, name) : SAKDomainPredicate.wasString.JSONKey(@"itemName"),
              @keypath(PPWalletEntranceInfo.new, nameDescription) : SAKDomainPredicate.wasOptional.wasString.JSONKey(@"itemDesc"),
              @keypath(PPWalletEntranceInfo.new, isHighlight) : SAKDomainPredicate.wasOptional.wasBoolean.JSONKey(@"ishighlight"),
              @keypath(PPWalletEntranceInfo.new, portalUrl) : SAKDomainPredicate.wasOptional.wasString.JSONKey(@"itemUrl"),
              @keypath(PPWalletEntranceInfo.new, isShow) : SAKDomainPredicate.wasOptional.wasBoolean.JSO<PERSON><PERSON><PERSON>(@"ifShow"),
              };
}

@end

