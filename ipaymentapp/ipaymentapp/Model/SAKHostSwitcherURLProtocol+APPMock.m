//
//  SAKHostSwitcherURLProtocol+APPMock.m
//  ipaymentapp
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2017/2/8.
//  Copyright © 2017年 Meituan.com. All rights reserved.
//

#if TEST || DEBUG

#import "SAKHostSwitcherURLProtocol+APPMock.h"
#import "SAKBaseModel+AppMock.h"

@implementation SAKHostSwitcherURLProtocol (APPMock)
- (void)startLoading {
    NSMutableURLRequest *request = [self.request mutableCopy];
    
    if ([SAKBaseModel sak_isMockDebugType]) {
        NSDictionary *headFields = request.allHTTPHeaderFields;
        NSString *mkScheme = [headFields objectForKey:@"MKScheme"];
        NSString *mkHost = [headFields objectForKey:@"MKOriginHost"];
        if (mkScheme.length && mkHost.length) {
            NSString *url = [mkScheme stringByAppendingFormat:@"://%@",mkHost];
            url = [[self class] switchedRequestURLString:url];
            [request setValue:[url componentsSeparatedByString:@"://"][0] forHTTPHeaderField:@"MKScheme"];
            [request setValue:[url componentsSeparatedByString:@"://"][1] forHTTPHeaderField:@"MKOriginHost"];
        }
    } else {
        request.URL = [[NSURL alloc] initWithString:[[self class] switchedRequestURLString:[request.URL absoluteString]]];
    }
    
    // 模拟下单接口安全性考虑，未正式上线，线上环境走 st 环境域名
    if ([request.URL.path isEqualToString:@"/demo/genorder"] && [request.URL.host isEqualToString:@"mpay.vip.sankuai.com"]) {
        NSString *resultURLString = [request.URL.absoluteString stringByReplacingOccurrencesOfString:@"mpay.vip.sankuai.com" withString:@"stable.pay.st.sankuai.com"];
        request.URL = [NSURL URLWithString:resultURLString];
    }
    
    [NSURLProtocol setProperty:@YES forKey:@"SAKHostSwitcherProcessed" inRequest:request];
    
    [self setValue:[NSURLConnection connectionWithRequest:[request copy] delegate:self] forKey:@"connection"];
}
@end

#endif
