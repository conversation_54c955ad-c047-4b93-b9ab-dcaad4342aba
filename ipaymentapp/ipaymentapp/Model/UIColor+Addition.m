//
//  UIColor+Addition.m
//  ipaymentapp
//
//  Created by xiaoyangyuxuan on 2020/7/9.
//  Copyright © 2020 Meituan.com. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UIColor+Addition.h"
#import <SAKUIKit.h>

@implementation UIColor (IPayment)

+ (UIColor *)generalBackgroundColor
{
    if (@available(iOS 13.0, *)) {
        return [UIColor colorWithDynamicProvider:^UIColor *_Nonnull(UITraitCollection *_Nonnull traitCollection) {
            return traitCollection.userInterfaceStyle == UIUserInterfaceStyleDark ? UIColor.blackColor : UIColor.whiteColor;
        }];
    } else {
        return UIColor.whiteColor;
    }
}

+ (UIColor *)grayBackgroundColor
{
    if (@available(iOS 13.0, *)) {
        return [UIColor colorWithDynamicProvider:^UIColor *_Nonnull(UITraitCollection *_Nonnull traitCollection) {
            return traitCollection.userInterfaceStyle == UIUserInterfaceStyleDark ? UIColor.systemGray2Color : [UIColor colorWithWhite:0.9 alpha:1];
        }];
    } else {
        return [UIColor colorWithWhite:0.9 alpha:1];
    }
}

+ (UIColor *)generalSeparatorLineColor
{
    if (@available(iOS 13.0, *)) {
        return [UIColor colorWithDynamicProvider:^UIColor *_Nonnull(UITraitCollection *_Nonnull traitCollection) {
            return traitCollection.userInterfaceStyle == UIUserInterfaceStyleDark ? UIColor.systemGray2Color : HEXCOLOR(0xd8d8d8d8);
        }];
    } else {
        return HEXCOLOR(0xd8d8d8d8);
    }
}

+ (UIColor *)generalLabelTextColor
{
    if (@available(iOS 13.0, *)) {
        return [UIColor colorWithDynamicProvider:^UIColor *_Nonnull(UITraitCollection *_Nonnull traitCollection) {
            return traitCollection.userInterfaceStyle == UIUserInterfaceStyleDark ? UIColor.whiteColor : UIColor.blackColor;
        }];
    } else {
        return UIColor.blackColor;
    }
}

+ (UIColor *)grayLabelTextColor
{
    if (@available(iOS 13.0, *)) {
        return [UIColor colorWithDynamicProvider:^UIColor *_Nonnull(UITraitCollection *_Nonnull traitCollection) {
            return traitCollection.userInterfaceStyle == UIUserInterfaceStyleDark ? UIColor.whiteColor : HEXCOLOR(0x666666);
        }];
    } else {
        return HEXCOLOR(0x666666);
    }
}

+ (UIColor *)blueLabelTextColor
{
    if (@available(iOS 13.0, *)) {
        return UIColor.systemBlueColor;
    } else {
        return UIColor.blueColor;
    }
}

+ (UIColor *)orangeLabelTextColor
{
    if (@available(iOS 13.0, *)) {
        return UIColor.systemOrangeColor;
    } else {
        return UIColor.orangeColor;
    }
}

@end
