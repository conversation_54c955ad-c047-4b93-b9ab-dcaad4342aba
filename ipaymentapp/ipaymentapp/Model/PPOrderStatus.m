//
//  PPOrderStatus.m
//  ipaymentapp
//
//  Created by wa<PERSON><PERSON> on 15/7/17.
//  Copyright (c) 2015年 Meituan.com. All rights reserved.
//
#import "PPOrderStatus.h"

const NSString *kPPRefundTypeThirdpay = @"direction_to_thirdpay";
const NSString *kPPRefundTypeCredit = @"direction_to_credit";
const NSString *kPPTradeFinished = @"TRADE_FINISHED";
const NSString *kPPTradeSuccess = @"TRADE_SUCCESS";
const NSString *kPPStatusNew = @"STATUS_NEW";
const NSString *kPPWaitPay = @"WAIT_PAY";
const NSString *kPPStatusRefundedAll = @"STATUS_REFUNDED_ALL";
const NSString *kPPStatusRefundedPART = @"STATUS_REFUNDED_PART";
