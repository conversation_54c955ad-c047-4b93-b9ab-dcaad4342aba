//
//  PPRegisterAPPMock.m
//  ipaymentapp
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2017/3/6.
//  Copyright © 2017年 Meituan.com. All rights reserved.
//

#import "PPRegisterAPPMock.h"
#import "SAKKeyCommands.h"
#import <SAKUIKit/SAKAlertView.h>
#import <SAKPortal.h>
#import "SAKEnvironment.h"
#import "SAKWebViewController.h"

@implementation PPRegisterAPPMock

+ (void)load
{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        [[SAKKeyCommands sharedInstance] registerKeyCommandWithInput:@"z" modifierFlags:UIKeyModifierCommand action:^(UIKeyCommand *command) {
            [self showRigiestDialog];
        }];
    });
}

+ (void)showRigiestDialog
{
    SAKAlertView *alertView = [[SAKAlertView alloc] init];
    UITextView *textView = [[UITextView alloc] initWithFrame:CGRectMake(0, 0, 230, 100)];
    NSString *text = [NSString stringWithFormat:@"绑定一次即可,在此输入你的大象🐘用户名（例如zhaozhiyu）,当前uuid : %@",[[SAKEnvironment environment] UUID]];
    textView.text = text;
    alertView.title = @"请先开启AppMock";
    alertView.message = [NSString stringWithFormat:@"按cmd+z调用"];
    alertView.dismissWithButtonAction = false;
    alertView.additionalView = textView;
    NSLog(@"alertView:%@",alertView);
    [alertView addButtonWithTitle:@"注册" type:SAKAlertViewButtonTypeRight handler:^(SAKAlertView *alert) {
        [alert dismiss];
        NSString *urlString = [NSString stringWithFormat:@"https://appmock.sankuai.com/mw/register?&uuid=%@&uid=%@",[[SAKEnvironment environment] UUID],[textView.text lowercaseString]];
        NSURL *url = [NSURL URLWithString:urlString];
        NSURLRequest *request = [[NSURLRequest alloc]initWithURL:url cachePolicy:NSURLRequestReloadRevalidatingCacheData timeoutInterval:10];
        NSURLConnection *connection = [NSURLConnection connectionWithRequest:request delegate:self];
        [connection start];
    }];
    [alertView addButtonWithTitle:@"从剪贴板复制" type:SAKAlertViewButtonTypeRight handler:^(SAKAlertView *alert) {
        UIPasteboard *pastBoard = [UIPasteboard generalPasteboard];
        textView.text = pastBoard.string;
    }];
    [alertView addButtonWithTitle:@"清空" type:SAKAlertViewButtonTypeRight handler:^(SAKAlertView *alert) {
        textView.text = @"";
    }];
    [alertView addButtonWithTitle:@"退出" type:SAKAlertViewButtonTypeRight handler:^(SAKAlertView *alert) {
        [alert dismiss];
    }];
    [alertView show];
}

@end
