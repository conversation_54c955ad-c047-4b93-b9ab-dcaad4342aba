//
//  SAKBaseModel+PPHook.m
//  ipaymentapp
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2017/3/6.
//  Copyright © 2017年 Meituan.com. All rights reserved.
//
#if TEST || DEBUG

#import "SAKBaseModel+PPHook.h"
#import "SAKPersistentConnectProtocol.h"
#import "SAKEnvironment.h"

@implementation SAKBaseModel (PPHook)

+ (void)load
{
    
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        
        Class selfClass = object_getClass([self class]);
        
        SEL oriSEL = @selector(sak_convertToMockRequest:);
        Method oriMethod = class_getInstanceMethod(selfClass, oriSEL);
        
        SEL cusSEL = @selector(pp_convertToMockRequest:);
        Method cusMethod = class_getInstanceMethod(selfClass, cusSEL);
        
        BOOL addSucc = class_addMethod(selfClass, oriSEL, method_getImplementation(cusMethod), method_getTypeEncoding(cusMethod));
        if (addSucc) {
            class_replaceMethod(selfClass, cusSEL, method_getImplementation(oriMethod), method_getTypeEncoding(oriMethod));
        }else {
            method_exchangeImplementations(oriMethod, cusMethod);
        }
        
    });
}

+ (NSURLRequest *)pp_convertToMockRequest:(NSURLRequest *)request
{
    if (!request.URL) {
        return request;
    }
    
    NSURL *url = request.URL;
    NSArray *blackURLSuffixs = @[@".jpg", @".png"];
    NSString *lastPathComponent = [[url lastPathComponent] lowercaseString];
    for (NSString *black in blackURLSuffixs) {
        if ([lastPathComponent hasSuffix:black]) {
            return request;
        }
    }
    
    
    NSMutableURLRequest * mutableRequest = [[SAKPersistentConnectProtocol protocolCanonicalRequestForRequest:request] mutableCopy];
    
    NSURLComponents *components = [NSURLComponents componentsWithURL:mutableRequest.URL resolvingAgainstBaseURL:YES];
    
    if (![request valueForHTTPHeaderField:@"MKOriginHost"]) {
        [mutableRequest setValue:components.host forHTTPHeaderField:@"MKOriginHost"];
    }
    
    if (![request valueForHTTPHeaderField:@"MKTunnelType"]) {
        [mutableRequest setValue:@"http" forHTTPHeaderField:@"MKTunnelType"];
    }
    
    if (![request valueForHTTPHeaderField:@"MKScheme"]) {
        [mutableRequest setValue:components.scheme forHTTPHeaderField:@"MKScheme"];
    }
    
    if (![request valueForHTTPHeaderField:@"MKScheme"]) {
        [mutableRequest setValue:components.scheme forHTTPHeaderField:@"MKScheme"];
    }
    
    if (![mutableRequest valueForHTTPHeaderField:@"MKUnionId"]) {
        NSString *uniondId = [[SAKEnvironment environment] UUID];
        if (uniondId.length) {
            [mutableRequest setValue:uniondId forHTTPHeaderField:@"MKUnionId"];
        }
    }
    
    // 原sak_configMockURLString不再生效，指定该域名
    components.host = @"appmock.sankuai.com";
    mutableRequest.URL = components.URL;
    
    return [mutableRequest copy];
}

@end

#endif
