//
//  PPMineViewModel.m
//  ipaymentapp
//
//  Created by zzMBP on 16/7/25.
//  Copyright © 2016年 Meituan.com. All rights reserved.
//

#import "PPMineViewModel.h"
#import "PPMineModel.h"
#import "PPWalletEntranceInfo.h"

@interface PPMineViewModel()

@property (nonatomic, strong) PPMineModel *model;

@end

@implementation PPMineViewModel

- (instancetype)init
{
    self = [super init];
    if (self) {
        _model = [[PPMineModel alloc] init];
    }
    return self;
}

- (RACCommand *)loadCommand
{
    if (!_loadCommand) {
        @weakify(self)
        _loadCommand = [[RACCommand alloc] initWithSignalBlock:^RACSignal *(id input) {
            @strongify(self)
            return [self refreshEntranceInfoSignal];
        }];
    }
    return _loadCommand;
}

- (RACSignal *)refreshEntranceInfoSignal
{
    @weakify(self)
    return [[self.model fetchMeituanPaymentEntranceInfoSignal] doNext:^(NSDictionary *entranceInfoDictionary) {
        @strongify(self)
        self.walletInfo = [PPWalletEntranceInfo domainWithJSONDictionary:entranceInfoDictionary[@"walletInfo"]];
        self.balanceInfo = [PPWalletEntranceInfo domainWithJSONDictionary:entranceInfoDictionary[@"balanceInfo"]];
        self.nopasspayInfo = [PPWalletEntranceInfo domainWithJSONDictionary:entranceInfoDictionary[@"nopasspayInfo"]];
    }];
}

@end
