//
//  PPAccountController.m
//  ipaymentapp
//
//  Created by wangfeng on 15/7/14.
//  Copyright (c) 2015年 Meituan.com. All rights reserved.
//

#import "PPAccountController.h"
#import "MTSignInViewController.h"
#import "MTNavigationController.h"

@interface PPAccountController ()

@end

@implementation PPAccountController

static PPAccountController *defaultPPAccountController;

+ (instancetype)defaultInstance
{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        defaultPPAccountController = [[PPAccountController alloc] init];
    });
    return defaultPPAccountController;
}

- (void)loginWith:(UIViewController *)viewController completedBlock:(void (^)())block
{
    MTSignInViewController *signInViewController = [[MTSignInViewController alloc] init];
    signInViewController.didSignInBlock = block;
    MTNavigationController *navigationController = [[MTNavigationController alloc] initWithRootViewController:signInViewController];
    [viewController presentViewController:navigationController animated:YES completion:nil];
}

@end
