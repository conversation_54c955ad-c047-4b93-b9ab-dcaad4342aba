//
//  PPProcessController.m
//  SAKCashier
//
//  Created by sunhl on 15/3/27.
//  Copyright (c) 2015年 meituan. All rights reserved.
//

#import "PPService.h"
#import "CIPError.h"
#import "PPModel.h"
#import "PPContentInfo.h"
#import "PPRefundedOrder.h"
#import "SPGObjectCacheManager.h"
#import "NSObject+SPKReturnSelf.h"

NSString * const kOfflinePartnerKey = @"oCr7Sd9FJ2rMCZMWulbhW6dg1M70aYvu";
NSString * const kOnlinePartnerKey = @"3Z3oY8BSObNfqNXzDAjgiBQuRm21LM8c";
NSString * const kOfflineMerchantNO = @"11000002029894";
NSString * const kOnlineMerchantNO = @"11000019125438";

NSString * const kOfflineReChargeTypePartnerKey = @"7MGaHg9AfBlPMoCfdScCJAoemQx8LXzI";
NSString * const kQaReChargeTypePartnerKey = @"7od[4vV/Yi1i8<D[D'%)(ko>Y%|t4+1r";
NSString * const kOnlineReChargeTypePartnerKey = @"GIzDsCc5YzIE7yz2tfy6ytesLjKOjiop";

NSString * const kOfflineReChargeTypeMerchantNO = @"11000011576141";
NSString * const kOnlineReChargeTypeMerchantNO = @"11000004396262";

// 仅小贷测试使用的商户号
NSString * const kOnlineCreditMerchantNO = @"11000003404791";
NSString * const kOnlineCreditPartnerKey = @"f0Hqh38ZzyVEzsvrAMD7tWtgWQ1I9m79";

// DCEP切换的商户号
NSString * const kOnlineDCEPMerchantNO = @"11000160456649";


@interface PPService ()

@property (nonatomic, strong) PPModel *model;

@end


@implementation PPService

static PPService *defaultPPService;

+ (id)defaultInstance
{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        defaultPPService = [[PPService alloc] init];
    });
    
    return defaultPPService;
}

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.model = [[PPModel alloc] init];
    }
    
    return self;
}

- (NSMutableArray *)getSubmitOrderContentArray
{
    NSMutableArray *contentArray = [NSMutableArray array];
    
    PPContentInfo *partnerIDInfo = ({
        PPContentInfo *info = [[PPContentInfo alloc] init];
        info.identifier = @"partner_id";
        info.title = @"业务ID";
        info.promptString = @"业务方的ID";
        info.defaultValue = @"2015010800000001";
        info;
    });
    [contentArray addObject:partnerIDInfo];
    
    PPContentInfo *partnerKeyInfo = ({
        PPContentInfo *info = [[PPContentInfo alloc] init];
        info.identifier = @"partner_key"; // 特殊
        info.title = @"业务KEY";
        info.promptString = @"业务方的Key";
        info.defaultValue = kOnlinePartnerKey;
        info;
    });
    [contentArray addObject:partnerKeyInfo];

    PPContentInfo *iphPayMerchantNoInfo = ({
        PPContentInfo *info = [[PPContentInfo alloc] init];
        info.identifier = @"iph_pay_merchant_no";
        info.title = @"商户号";
        info.promptString = @"iph_pay_merchant_no";
        info.defaultValue = kOnlineMerchantNO;
        info;
    });
    [contentArray addObject:iphPayMerchantNoInfo];
    
    PPContentInfo *notifyURLInfo = ({
        PPContentInfo *info = [[PPContentInfo alloc] init];
        info.identifier = @"notify_url";
        info.title = @"notify_url";
        info.promptString = @"notify URL";
        info.defaultValue = @"http://************:8080/cashier/paynotify";
        info;
    });
    [contentArray addObject:notifyURLInfo];
    
    PPContentInfo *outNumberInfo = ({
        NSInteger timestamp = [[NSDate date] timeIntervalSince1970];
        PPContentInfo *info = [[PPContentInfo alloc] init];
        info.defaultValue = [NSString stringWithFormat:@"%ld", (long)timestamp];
        info.identifier = @"out_no";
        info.title = @"out_no";
        info.promptString = @"out_no";
        info;
    });
    [contentArray addObject:outNumberInfo];

    PPContentInfo *subjectInfo = ({
        PPContentInfo *info = [[PPContentInfo alloc] init];
        info.identifier = @"subject";
        info.title = @"商品名称";
        info.promptString = @"subject";
        info.defaultValue = @"测试单";
        info;
    });
    [contentArray addObject:subjectInfo];
    
    PPContentInfo *bodyInfo = ({
        PPContentInfo *info = [[PPContentInfo alloc] init];
        info.identifier = @"body";
        info.title = @"商品描述";
        info.promptString = @"bodyInfo";
        info.defaultValue = @"这是测试单";
        info;
    });
    [contentArray addObject:bodyInfo];
    
    PPContentInfo *totalFeeInfo = ({
        PPContentInfo *info = [[PPContentInfo alloc] init];
        info.identifier = @"total_fee";
        info.title = @"订单金额";
        info.promptString = @"totalFee";
        info.defaultValue = @"0.01";
        info;
    });
    [contentArray addObject:totalFeeInfo];
    
    PPContentInfo *expireTimeInfo = ({
        PPContentInfo *info = [[PPContentInfo alloc] init];
        info.identifier = @"pay_expire";
        info.title = @"超时时间";
        info.promptString = @"expireTime";
        info.defaultValue = @"1000";
        info;
    });
    [contentArray addObject:expireTimeInfo];
    
    PPContentInfo *noLoginInfo = ({
        PPContentInfo *info = [[PPContentInfo alloc] init];
        info.identifier = @"noLogin";
        info.title = @"免密登陆参数";
        info.promptString = @"noLogin";
        info.defaultValue = @"0";
        info;
    });
    [contentArray addObject:noLoginInfo];
    
    PPContentInfo *cashierTypeInfo = ({
        PPContentInfo *info = [[PPContentInfo alloc] init];
        info.identifier = @"cashier_type";
        info.title = @"收银台类型";
        info.promptString = @"收银台类型例如pay-defer-sign";
        info;
    });
    [contentArray addObject:cashierTypeInfo];
    
    return contentArray;
}

- (void)generatePayOrderWithContentArray:(NSArray *)contentArray
                                finished:(void (^)(MTCPaymentRequest *paymentRequest, CIPError *error))finished
{
    
    NSMutableDictionary *paramDic = [NSMutableDictionary dictionary];
    
    for (PPContentInfo *contentInfo in contentArray) {
        if ([contentInfo.value length] && [contentInfo.identifier length]) {
            [paramDic setObject:contentInfo.value forKey:contentInfo.identifier];
        }
        
        if ([contentInfo.value isEqualToString:@"weekpay"]) {
            NSString *str = @"{\"bizCategoryCode\": \"waimai\"}";
            [paramDic setObject:str forKey:@"extraInfo"];
        }
    }
    
    [self.model generatePayOrderWithContentDictionary:[paramDic copy]
                                             finished:^(MTCPaymentRequest *paymentRequest, CIPError *error) {
                                                 @within_main_thread(finished, paymentRequest, error);
                                             }];
}

- (void)generateCombinePayOrderWithContentArray:(NSArray *)contentArray
                                       finished:(void (^)(MTCPaymentRequest *paymentRequest, CIPError *error))finished
{
    NSMutableDictionary *paramDic = [NSMutableDictionary dictionary];
    
    for (PPContentInfo *contentInfo in contentArray) {
        if ([contentInfo.value length] && [contentInfo.identifier length]) {
            [paramDic setObject:contentInfo.value forKey:contentInfo.identifier];
        }
    }
    
    [self.model generateCombinePayOrderWithContentDictionary:[paramDic copy]
                                                    finished:^(MTCPaymentRequest *paymentRequest, CIPError *error) {
                                                        @within_main_thread(finished, paymentRequest, error);
                                                    }];
}

- (void)refundOrderWithOrder:(PPOrder *)order
                 extraParams:(NSDictionary *)extraParams
                    Finished:(void(^)(CIPError *error))finished
{
    NSString *partnerID = order.partnerID;
    NSTimeInterval currentTime=[[NSDate dateWithTimeIntervalSinceNow:0] timeIntervalSince1970];
    NSString *timeString = [NSString stringWithFormat:@"%.0f", currentTime];
    NSMutableDictionary *param = [NSMutableDictionary dictionaryWithDictionary:@{
                                                                                 @"tradeNo" : order.orderID,
                                                                                 @"refundNo" : timeString,
                                                                                 @"sellerId" : partnerID,
                                                                                 @"riskParams": @"{}",
                                                                                 @"refundFeeCent": [NSString stringWithFormat:@"%ld", lround(order.money.doubleValue * 100)]
                                                                                 }];
    [param addEntriesFromDictionary:extraParams];
    
    [self.model refundOrderWithContentDictionary:[param copy]
                                      partnerKey:kOnlinePartnerKey
                                        Finished:^(CIPError *error) {
                                            @within_main_thread(finished, error);
                                        }];
}

- (void)getOrderListFinished:(void(^)(NSArray *orderArray, CIPError *error))finished
{
    [self.model fetchOrderListFinished:^(NSArray *orderArray, CIPError *error) {
//#define PP_TEST
#ifdef PP_TEST
        
        NSMutableArray *tmporderArray = [NSMutableArray array];
        PPOrder *order1 = ({
            PPOrder *order = [[PPOrder alloc] init];
            order.orderID = 12345;
            order.title = @"order one";
            order.payType = @"weixin";
            order.partnerID = 1234567;
            order.payTime = [NSDate date];
            order.orderTime = [NSDate date];
            order.status = @"NEED_PAY";
            order;
        });
        [tmporderArray addObject:order1];
        
        PPOrder *order2 = ({
            PPOrder *order = [[PPOrder alloc] init];
            order.orderID = 1234;
            order.title = @"order two";
            order.payType = @"weixin";
            order.partnerID = 1234568;
            order.payTime = [NSDate date];
            order.orderTime = [NSDate date];
            order.status = @"HAD_PAY";
            order;
        });
        [tmporderArray addObject:order2];
        
        @within_main_thread(finished, [tmporderArray copy], nil);
#else
        
        @within_main_thread(finished, orderArray, error);
#endif
        
    }];
}

- (void)refreshOrderWithOrder:(PPOrder *)order
                     finished:(void(^)(PPOrder *order, CIPError *error))finished
{
    NSMutableDictionary *param = [NSMutableDictionary dictionaryWithDictionary:@{
                                                                                 @"partner_id" : order.partnerID,
                                                                                 @"trade_no" : order.orderID
                                                                                 }];
    
    [self.model refreshOrderWithContentDictionary:[param copy]
                                      partnerKey:kOnlinePartnerKey
                                        finished:^(PPOrder *order, CIPError *error) {
                                            @within_main_thread(finished, order, error);
                                        }];

}

- (void)generateHelloPayWithContentArray:(NSArray *)contentArray finished:(void (^)(MTCPaymentRequest *, CIPError *))finished
{
    NSMutableDictionary *paramDic = [NSMutableDictionary dictionary];
    
    NSString *partnerKey;
    
    for (PPContentInfo *contentInfo in contentArray) {
        if ([contentInfo.value length] && [contentInfo.identifier length]) {
            if ([contentInfo.identifier isEqualToString:@"partner_key"]) {
                partnerKey = contentInfo.value;
            } else {
                [paramDic setObject:contentInfo.value forKey:contentInfo.identifier];
            }
        }
    }
    
    [self.model generateHelloPayOrderWithContentDictionary:[paramDic copy]
                                                partnerKey:partnerKey
                                                  finished:^(MTCPaymentRequest *paymentRequest, CIPError *error) {
                                                      @within_main_thread(finished, paymentRequest, error);
                                                  }];
}

- (void)getRefundOrderListWithParam:(NSDictionary *)param finished:(void(^)(NSArray<PPRefundedOrder *> *orderList, CIPError *error))finished
{
    // 退款列表接口 wiki: https://km.sankuai.com/page/478462030
    [self.model fetchRefundOrderListWithParamDictionary:param finished:^(NSDictionary * _Nullable responseDictionary, CIPError * _Nullable error) {
        if (error) {
            @within_main_thread(finished, nil, error);
        } else {
            CIPError *jsonError = nil;
            NSMutableArray<PPRefundedOrder *> *orderArray = [NSMutableArray new];
            for (NSDictionary *orderDictionary in responseDictionary[@"data"]) {
                CIPError *JSONError;
                PPRefundedOrder *order = [PPRefundedOrder domainWithJSONDictionary:orderDictionary error:&JSONError];
                if (JSONError) {
                    finished(nil, JSONError);
                    return;
                }
                [orderArray addObject:order];
            }
            @within_main_thread(finished, [orderArray copy], jsonError);
        }
    }];
}

- (void)barcodeDemoMockPay:(NSString *)payCodeToken
                  finished:(void(^)(CIPError *error))finished
{
    [self.model barcodeDemoMockPay:payCodeToken finished:finished];
}

- (NSMutableArray *)getVerifyPayPasswordContentArray
{
    NSMutableArray *contentArray = [NSMutableArray array];
    
    PPContentInfo *partnerIDInfo = ({
        PPContentInfo *info = [[PPContentInfo alloc] init];
        info.identifier = @"partner_id";
        info.title = @"业务ID:";
        info.promptString = @"业务方的ID";
        info.defaultValue = @"20150108";
        info;
    });
    [contentArray addObject:partnerIDInfo];
    
    PPContentInfo *partnerKeyInfo = ({
        PPContentInfo *info = [[PPContentInfo alloc] init];
        info.identifier = @"partner_key"; // 特殊
        info.title = @"业务Key:";
        info.promptString = @"业务方的Key";
        info.defaultValue = kOnlinePartnerKey;
        info;
    });
    [contentArray addObject:partnerKeyInfo];
    
    PPContentInfo *iphPayMerchantNoInfo = ({
        PPContentInfo *info = [[PPContentInfo alloc] init];
        info.identifier = @"merchant_no";
        info.title = @"merchantNo:";
        info.promptString = @"merchant_no";
        info.defaultValue = kOnlineMerchantNO;
        info;
    });
    [contentArray addObject:iphPayMerchantNoInfo];
    
    PPContentInfo *outNumberInfo = ({
        NSInteger timestamp = [[NSDate date] timeIntervalSince1970];
        PPContentInfo *info = [[PPContentInfo alloc] init];
        info.defaultValue = [NSString stringWithFormat:@"%ld", (long)timestamp];
        info.identifier = @"order_no";
        info.title = @"order_no:";
        info.promptString = @"order_no";
        info;
    });
    [contentArray addObject:outNumberInfo];
    
    return contentArray;
}

@end
