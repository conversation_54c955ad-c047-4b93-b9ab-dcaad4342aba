//
//  PPOrder.m
//  SAKCashier
//
//  Created by sunhl on 15/3/31.
//  Copyright (c) 2015年 meituan. All rights reserved.
//

#import "PPOrder.h"
#import <libextobjc/extobjc.h>

@implementation PPOrder

+ (NSDictionary *)predicateDictionary
{
    return @{ @keypath(PPOrder.new, orderID) : SAKDomainPredicate.isNotNull.JSONKey(@"transtradeno"),
              @keypath(PPOrder.new, partnerID) : SAKDomainPredicate.isString.JSON<PERSON>ey(@"partnerid"),
              @keypath(PPOrder.new, payType) : SAKDomainPredicate.wasOptional.JSONKey(@"paytype"),
              
              @keypath(PPOrder.new, status) : SAKDomainPredicate.wasNumber.JSONKey(@"status"),
               
              @keypath(PPOrder.new, orderTime): SAKDomainPredicate.wasOptional.JSON<PERSON>ey(@"ordertime"),
              
              @keypath(PPOrder.new, payTime): SAKDomainPredicate.wasOptional.JSON<PERSON>ey(@"paytime"),
              
              @keypath(PPOrder.new, title) : SAKDomainPredicate.isOptional.isString.JSONKey(@"subject"),
              @keypath(PPOrder.new, money) : SAKDomainPredicate.isString.JSONKey(@"money"),
              @keypath(PPOrder.new, body) : SAKDomainPredicate.isOptional.wasString.JSONKey(@"body"),
               @keypath(PPOrder.new, theID) : SAKDomainPredicate.wasOptional.wasNumber.isOptional.JSONKey(@"id"),
               @keypath(PPOrder.new, tradeNo) : SAKDomainPredicate.wasOptional.isOptional.isString.JSONKey(@"transtradeno"),
              };
}

+ (NSValueTransformer *)orderTimeJSONTransformer
{
    return [MTLValueTransformer transformerWithBlock:^id(NSNumber *time) {
        return [NSDate dateWithTimeIntervalSince1970:time.doubleValue];
    }];
}

+ (NSValueTransformer *)payTimeJSONTransformer
{
    return [MTLValueTransformer transformerWithBlock:^id(NSNumber *time) {
        return [NSDate dateWithTimeIntervalSince1970:time.doubleValue];
    }];
}

@end

