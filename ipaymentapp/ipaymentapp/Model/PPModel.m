//
//  PPModel.m
//  SAKCashier
//
//  Created by ho<PERSON><PERSON><PERSON><PERSON> on 3/26/15.
//  Copyright (c) 2015 meituan. All rights reserved.
//

#import "PPModel.h"
#import "SAKEnvironment.h"
#import "../../../Pods/Headers/Private/SAKCashier/MTCPaymentRequest.h"
#import "PPOrder.h"
#import "SAKBaseModel+CashierApp.h"
#import "SPKJSONObject.h"
#import "SAKHostSwitcherURLProtocol.h"
#import "SAKOneClickDataManager.h"
#import "NSDictionary+SPKShortcuts.h"
#import "SPKJSONObject.h"
#import "SPKUserSETManager.h"
#import <SAKCrypto/NSString+SAKDigest.h>
#import <QHOServiceManager/QHOServiceManager.h>
#import <CIPServiceRegistry/SAKFinPaymentAdapterProtocol.h>

NSString *PPModelErrorDomain = @"PPErrorDomain";

NSString *const kOuterAPIpayInVip = @"https://pay-in.vip.sankuai.com";
NSString *const kInternalAPIpayInVIP = @"https://pay-in.vip.sankuai.com";

NSString *const kOuterAPImpayVIP = @"https://mpay.vip.sankuai.com";
NSString *const kInternalAPImpayVIP = @"https://mpay.vip.sankuai.com";

@implementation PPModel

/*
 * 接口文档
 * https://km.sankuai.com/page/124556747
 */

- (void)generatePayOrderWithContentDictionary:(NSDictionary *)contentDictionay
                                     finished:(void(^)(MTCPaymentRequest *paymentRequest, CIPError *error))finished
{
    NSString *userID = [[SAKEnvironment environment].user.userID stringValue];
    NSString *token = [SAKEnvironment environment].user.token;
    NSAssert(userID.length > 0, @"No userID information");
    NSAssert(token.length > 0, @"No token information");
    
    // 追加通用参数
    NSMutableDictionary *paramDic = [NSMutableDictionary dictionaryWithDictionary:contentDictionay];
    [paramDic addEntriesFromDictionary:@{
                                         @"userid" : userID,
                                         @"token" : token,
                                         }];
    // 新版下单接口字段名与其他接口不一致，需要进行一次转换
    NSDictionary *resultParam = [self transferParamter:[paramDic copy]];
    
    NSString *URLString = nil;
    if ([self isInternalRequest]) {
        URLString = [kInternalAPImpayVIP stringByAppendingString: @"/demo/genorder"];
    } else {
        URLString = [kOuterAPImpayVIP stringByAppendingString:@"/demo/genorder"];
    }
    
    [self postToCashierPath:URLString
                  parameter:resultParam
                   finished:^(NSDictionary *responseDictionary, NSURLRequest *request, CIPError *error) {
                       error = [self checkoutResponseError:responseDictionary error:error];
                       if (finished) {
                           if (error) {
                               finished(nil, error);
                           } else {
                              MTCPaymentRequest *request = [[MTCPaymentRequest alloc]
                                                            initWithTradeNumber:responseDictionary[@"data"][@"tradeNo"]
                                                            payToken:responseDictionary[@"data"][@"payToken"] merchantNO:nil
                                                            cashierType:resultParam[@"cashier_type"] ?: nil
                                                            oneClickPayExtParams:nil
                                                            extraData:nil
                                                            outBusinessData:nil
                                                            cif:nil
                                                            extraStatics:nil
                                                            callbackURLString:@"http://www.baidu.com"];
                               finished(request, error);
                           }
                       }
                   }];
}

- (void)generateCombinePayOrderWithContentDictionary:(NSDictionary *)contentDictionay
                                            finished:(void(^)(MTCPaymentRequest *paymentRequest, CIPError *error))finished
{
    NSString *userID = [[SAKEnvironment environment].user.userID stringValue];
    NSString *token = [SAKEnvironment environment].user.token;
    NSAssert(userID.length > 0, @"No userID information");
    NSAssert(token.length > 0, @"No token information");
    
    NSMutableDictionary *paramDic = [NSMutableDictionary dictionaryWithDictionary:contentDictionay];
    [paramDic addEntriesFromDictionary:@{
                                         @"userid" : userID,
                                         @"token" : token,
                                         @"noLogin" : @(0),  // 是否免密登录，固定写死参数0
                                         @"mainProduct" : @"商品A+商品B"
                                         }];
    
    // 构造两笔订单合并
    NSMutableArray *combineOrderList = [[NSMutableArray alloc] init];
    NSInteger combineOrderCount = 2;
    for (int i = 0; i < combineOrderCount; i++) {
        NSMutableDictionary *combineOrderItem = [NSMutableDictionary dictionary];
        [combineOrderItem addEntriesFromDictionary:@{
                                                     @"productName" : [NSString stringWithFormat:@"商品%d", i],
                                                     @"productDesc" : [NSString stringWithFormat:@"商品%d描述", i],
                                                     @"payFeeCent" : @(1),
                                                     @"expireTime" : @(1000),
                                                     }];
        [combineOrderList addObject:combineOrderItem];
    }
    
    [paramDic setObject:[combineOrderList spk_JSONString] forKey:@"transList"];
    // 新版下单接口字段名与其他接口不一致，需要进行一次转换
    NSDictionary *resultParam = [self transferParamter:[paramDic copy]];
    
    NSString *URLString = nil;
    if ([self isInternalRequest]) {
        URLString = [kInternalAPImpayVIP stringByAppendingString: @"/demo/gencombineorder"];
    } else {
        URLString = [kOuterAPImpayVIP stringByAppendingString:@"/demo/gencombineorder"];
    }
    
    [self postToCashierPath:URLString
                  parameter:resultParam
                   finished:^(NSDictionary *responseDictionary, NSURLRequest *request, CIPError *error) {
                       error = [self checkoutResponseError:responseDictionary error:error];
                       if (finished) {
                           if (error) {
                               finished(nil, error);
                           } else {
                               MTCPaymentRequest *request = [[MTCPaymentRequest alloc] initWithTradeNumber:responseDictionary[@"data"][@"tradeNo"]
                                                                                                  payToken:responseDictionary[@"data"][@"payToken"]
                                                                                                merchantNO:nil
                                                                                               cashierType:nil
                                                                                       oneClickPayExtParams:nil
                                                                                                 extraData:nil
                                                                                           outBusinessData:nil
                                                                                                       cif:nil
                                                                                              extraStatics:nil
                                                                                         callbackURLString:@"http://www.baidu.com"];
                               finished(request, error);
                           }
                       }
                   }];
}

- (void)fetchOrderListFinished:(void(^)(NSArray *orderArray, CIPError *error))finished
{
    NSString *URLString = nil;
    if ([self isInternalRequest]) {
        URLString = [kInternalAPIpayInVIP stringByAppendingString: @"/demo/payorder/querypayorderbyuid"];
    } else {
        URLString = [kOuterAPIpayInVip stringByAppendingString: @"/demo/payorder/querypayorderbyuid"];
    }
    [self postToURL:URLString withURLParameters:nil bodyParameters:@{@"userid": kUserID,@"limit": @10, @"offset": @0} dataEncoding:SAKModelDataJSONEncoding
           finished:^(NSDictionary *responseDictionary, NSURLRequest *request, CIPError *error) {
               error = [self checkoutResponseError:responseDictionary error:error];
               if (finished) {
                   if (error) {
                       finished(nil, error);
                   } else {
                       NSMutableArray *orderArray = [NSMutableArray array];
                       for (NSDictionary *orderDictionary in responseDictionary[@"data"]) {
                           CIPError *JSONError;
                           PPOrder *order = [PPOrder domainWithJSONDictionary:orderDictionary error:&JSONError];
                           if (JSONError) {
                               finished(nil, JSONError);
                               return;
                           }
                           [orderArray addObject:order];
                       }
                       finished([orderArray copy], error);
                   }
               }
               
           }];
}

- (void)fetchRefundOrderListWithParamDictionary:(NSDictionary *)paramDic
                                       finished:(void(^)(NSDictionary * responseDictionary, CIPError * error ))finished
{
    // 退款列表接口 wiki: https://km.sankuai.com/page/478462030
    NSString *URLString;
    if ([self isInternalRequest]) {
        URLString = [kInternalAPImpayVIP stringByAppendingString: @"/demo/refund/list"];
    } else {
        URLString = [kOuterAPImpayVIP stringByAppendingString:@"/demo/refund/list"];
    }
    
    [self postToCashierPath:URLString
                  parameter:paramDic
                   finished:^(NSDictionary *responseDictionary, NSURLRequest *request, CIPError *error) {
                       error = [self checkoutResponseError:responseDictionary error:error];
        if (finished) {
            finished(responseDictionary, error);
        }
    }];
}

- (void)refundOrderWithContentDictionary:(NSDictionary *)contentDictionay
                              partnerKey:(NSString *)partnerKey
                                Finished:(void(^)(CIPError *error))finished
{
    NSMutableDictionary *paramDic = [NSMutableDictionary dictionaryWithDictionary:contentDictionay];
    
    [paramDic addEntriesFromDictionary:@{
                                         @"signType" : @"MD5",
                                         @"charset" : @"UTF-8"
                                         }];
    
    [paramDic setObject:[self genSignWithParams:paramDic partnerKey:partnerKey] forKey:@"sign"];
    
    NSString *URLString = nil;
    if ([self isInternalRequest]) {
        URLString = [kInternalAPImpayVIP stringByAppendingString: @"/demo/refund/apply"];
    } else {
        URLString = [kOuterAPImpayVIP stringByAppendingString: @"/demo/refund/apply"];
    }
    
    [self postToCashierPath:URLString
                  parameter:paramDic
                   finished:^(NSDictionary *responseDictionary, NSURLRequest *request, CIPError *error) {
                       error = [self checkoutResponseError:responseDictionary error:error];
                       if (finished) {
                           finished(error);
                       }
                   }];
}

- (void)refreshOrderWithContentDictionary:(NSDictionary *)contentDictionay
                               partnerKey:(NSString *)partnerKey
                                 finished:(void(^)(PPOrder *order, CIPError *error))finished
{
    NSMutableDictionary *paramDic = [NSMutableDictionary dictionaryWithDictionary:contentDictionay];
    
    [paramDic addEntriesFromDictionary:@{
                                         @"sign_type" : @"MD5",
                                         @"_input_charset" : @"UTF-8"
                                         }];
    [paramDic setObject:[self genSignWithParams:paramDic partnerKey:partnerKey] forKey:@"sign"];
    
    NSString *URLString = nil;
    if ([self isInternalRequest]) {
        URLString = [kInternalAPImpayVIP stringByAppendingString: @"/trade/querypay"];
    } else {
        URLString = [kOuterAPImpayVIP stringByAppendingString: @"/trade/querypay"];
    }
    
    [self postToCashierPath:URLString
                  parameter:paramDic
                   finished:^(NSDictionary *responseDictionary, NSURLRequest *request, CIPError *error) {
                       error = [self checkoutResponseError:responseDictionary error:error];
                       if (finished) {
                           if (error) {
                               finished(nil, error);
                           } else {
                               CIPError *JSONError;
                               PPOrder *order = [PPOrder domainWithJSONDictionary:responseDictionary error:&JSONError];
                               if (JSONError) {
                                   finished(nil, JSONError);
                                   return;
                               }
                               finished(order, error);
                           };
                       }
                   }];
}

- (void)generateHelloPayOrderWithContentDictionary:(NSDictionary *)contentDictionay
                                        partnerKey:(NSString *)partnerKey
                                          finished:(void (^)(MTCPaymentRequest *, CIPError *))finished
{
    NSMutableDictionary *paramDic = [NSMutableDictionary dictionaryWithDictionary:contentDictionay];
    
    NSString *userID = [[SAKEnvironment environment].user.userID stringValue];
    NSAssert(![userID isEqualToString:@""], @"No user information");
    // order_money 的值和 total_fee 相等
    if (paramDic[@"total_fee"]) {
        [paramDic addEntriesFromDictionary:@{@"order_money" : paramDic[@"total_fee"]}];
    }
    
    [paramDic addEntriesFromDictionary:@{
                                         @"sign_type" : @"MD5",
                                         @"_input_charset" : @"UTF-8",
                                         @"orderid" : @"12345", // 任意值
                                         @"userid" : userID,
                                         @"risk_param":@"",
                                         @"third_cashier_type" : @"qdb",
                                         @"order_ip" : @"3232269249",
                                         }];
    
    
    // 2016-05-25 由 /trade/genpayorder 改为 /cashier/gentransorder
    // 2018-05-11 由 /cashier/gentransorder 改为 /noncashier/getthirdcashierurl
    // 2018-05-24 添加钱袋宝接口: 充值收银台 /qdbapi/unifiedorder wiki: https://123.sankuai.com/km/page/15544464
    // 2020-06-28 下线 /noncashier/getthirdcashierurl
    // 2020-07-22 充值收银台接口变更为 /demo/mtcashier/genspecialorder https://km.sankuai.com/page/386035594
    NSString *URLString = nil;
    if ([self isInternalRequest]) {
        URLString = [kInternalAPImpayVIP stringByAppendingString:@"/demo/mtcashier/genspecialorder"];
    } else {
        URLString = [kOuterAPImpayVIP stringByAppendingString:@"/demo/mtcashier/genspecialorder"];
    }
    
    if (!paramDic[@"recharge_type"]) {
        [paramDic addEntriesFromDictionary:@{@"recharge_type" : @"1"}];
    }
    if (!paramDic[@"nb_source"]) {
        [paramDic addEntriesFromDictionary:@{@"nb_source" : @"special_recharge"}];
    }
    [paramDic addEntriesFromDictionary:@{@"order_subject" : paramDic[@"subject"] ?: @"测试单"}];
    [paramDic addEntriesFromDictionary:@{@"order_no" : [NSString stringWithFormat:@"%d", (int)[[NSDate date] timeIntervalSince1970]]}];
    
    [paramDic setObject:[self genSignWithParams:paramDic partnerKey:partnerKey] forKey:@"sign"];
    [paramDic spk_setStringParameter:[[[SAKEnvironment environment] user] token] forKey:@"token"];

    [self postToCashierPath:URLString
                  parameter:paramDic
                   finished:^(NSDictionary *responseDictionary, NSURLRequest *request, CIPError *error) {
                       error = [self checkoutResponseError:responseDictionary error:error];
                       if (finished) {
                           if (error) {
                               finished(nil, error);
                           } else {
                               NSString *payToken = responseDictionary[@"data"][@"payToken"];
                               if (payToken.length == 0) {
                                   payToken = responseDictionary[@"data"][@"pay_token"];
                               }
                               
                               NSString *tradeNumber = responseDictionary[@"data"][@"tradeNo"];
                               if (tradeNumber.length == 0) {
                                   tradeNumber = responseDictionary[@"data"][@"trans_id"];
                               }
                               MTCPaymentRequest *request = [[MTCPaymentRequest alloc] initWithTradeNumber:(tradeNumber ?: @"")
                                                                                                  payToken:(payToken ?: @"")
                                                                                                merchantNO:nil
                                                                                               cashierType:nil
                                                                                       oneClickPayExtParams:nil
                                                                                                 extraData:nil
                                                                                           outBusinessData:nil
                                                                                                       cif:nil
                                                                                              extraStatics:nil
                                                                                         callbackURLString:@"http://www.baidu.com"];
                               finished(request, error);
                           }
                       }
                   }];
}


- (void)barcodeDemoMockPay:(NSString *)payCodeToken
                  finished:(void(^)(CIPError *error))finished
{
    NSMutableDictionary *paramDic = [NSMutableDictionary dictionary];
    if (payCodeToken.length > 0) {
        [paramDic addEntriesFromDictionary:@{@"payCodeToken" : payCodeToken}];
    }
    
    NSString *URLString = nil;
    if ([self isInternalRequest]) {
        URLString = [kInternalAPImpayVIP stringByAppendingString: @"/demo/paycode"];
    } else {
        URLString = [kOuterAPImpayVIP stringByAppendingString: @"/demo/paycode"];
    }
    
    [self postToCashierPath:URLString
                  parameter:paramDic
                   finished:^(NSDictionary *responseDictionary, NSURLRequest *request, CIPError *error) {
                       error = [self checkoutResponseError:responseDictionary error:error];
                       if (finished) {
                           if (error) {
                               finished(error);
                           } else {
                               CIPError *JSONError;
                               if (JSONError) {
                                   finished(JSONError);
                                   return;
                               }
                               finished(error);
                           };
                       }
                   }];
}

# pragma mark - private

- (NSString *)genSignWithParams:(NSDictionary *)paramsDictionary partnerKey:(NSString *)partnerKey
{
    __block NSString *sign = @"";
    
    NSArray *sortKeyArr = [[paramsDictionary allKeys] sortedArrayUsingComparator:^NSComparisonResult(id obj1, id obj2){
        return [obj1 compare:obj2 options:NSNumericSearch];
    }];
    [sortKeyArr enumerateObjectsUsingBlock:^(id obj, NSUInteger idx, BOOL *stop) {
        if (![obj isEqualToString:@"signType"] && ![obj isEqualToString:@"sign_type"] && [paramsDictionary[obj] length] > 0) {
            sign = [sign stringByAppendingString:[NSString stringWithFormat:@"%@=%@%@", obj, paramsDictionary[obj], [obj isEqual:sortKeyArr.lastObject] ? @"" : @"&"]];
        }
    }];
    sign = [sign stringByAppendingString:partnerKey];
    sign = [sign sak_MD5String];
    return sign;
}

- (CIPError *)checkoutResponseError:(NSDictionary *)responseDictionary error:(CIPError *)error
{
    CIPError *actualError;
    if (error) {
        actualError = error;
    } else if (responseDictionary[@"error"]) {
        NSDictionary *errorDictionary = responseDictionary[@"error"];
        NSInteger code = [errorDictionary[@"code"] integerValue];
        NSString *message = errorDictionary[@"message"];
        
        actualError = [[CIPError alloc] initWithDomain:PPModelErrorDomain
                                                  code:code
                                             callstack:[NSThread callStackSymbols]
                                              userInfo:@{NSLocalizedDescriptionKey: message}];
    } else {
        actualError = nil;
    }
    return actualError;
}

- (void)getFromCashierPath:(NSString *)path
                  finished:(SAKModelRequestCallback)finishied
{
    [self getFromCashierAppBackendPath:path
                     withURLParameters:@{@"token" : kUserToken}
                              finished:finishied];
}

- (void)postToCashierPath:(NSString *)path
                parameter:(NSDictionary *)parameter
                 finished:(SAKModelRequestCallback)finishied
{
    NSMutableDictionary *parameterDictionary = [NSMutableDictionary dictionaryWithDictionary:parameter];
    
    // SET 化需求，在 query 中追加 member_id、zone_user_id 两个参数
    NSMutableDictionary *URLParametersDictionary = [NSMutableDictionary new];
    [URLParametersDictionary spk_setObjectOrNil:[[SPKUserSETManager sharedManager] getMemberIDSafely:path] forKey:@"member_id"];
    [URLParametersDictionary spk_setObjectOrNil:[QHO_OPTIONAL_SERVICE_FOR(SAKFinPaymentAdapterProtocol) getMTUserID] forKey:@"zone_user_id"];
    [URLParametersDictionary spk_setObjectOrNil:kUserToken forKey:@"token"];
    
    [self postToCashierAppBackendPath:path
                    withURLParameters:URLParametersDictionary
                       bodyParameters:[parameterDictionary copy]
                             finished:finishied];
}

// 公司内网请求
- (BOOL)isInternalRequest
{
    return ([[SAKOneClickDataManager sharedManager] defaultEnvironmentId] == 1000) ||
    ([[SAKOneClickDataManager sharedManager] defaultEnvironmentId] == 1001) ||
    ([[SAKOneClickDataManager sharedManager] defaultEnvironmentId] == 2271);
}

- (NSDictionary *)transferParamter:(NSDictionary *)paramDictionary
{
    NSMutableDictionary *resultArray = [paramDictionary mutableCopy];
    
    NSString *sellerId = paramDictionary[@"iph_pay_merchant_no"];
    if (sellerId.length > 0) {
        [resultArray setObject:sellerId forKey:@"sellerId"];
        [resultArray removeObjectForKey:@"iph_pay_merchant_no"];
    }
    
    NSString *productName = paramDictionary[@"subject"];
    if (productName.length > 0) {
        [resultArray setObject:productName forKey:@"productName"];
        [resultArray removeObjectForKey:@"subject"];
    }
    
    NSString *productDesc = paramDictionary[@"body"];
    if (productDesc.length > 0) {
        [resultArray setObject:productDesc forKey:@"productDesc"];
        [resultArray removeObjectForKey:@"body"];
    }
    
    // 新版接口金额单位变成了分
    NSDecimalNumber *payFeeCent = [[NSDecimalNumber alloc] initWithString:paramDictionary[@"total_fee"]];
    NSDecimalNumber *tempNumber = [[NSDecimalNumber alloc] initWithString:@"100"];
    payFeeCent = [payFeeCent decimalNumberByMultiplyingBy:tempNumber];
    [resultArray setObject:[NSString stringWithFormat:@"%lld", payFeeCent.longLongValue] forKey:@"payFeeCent"];
    [resultArray removeObjectForKey:@"total_fee"];
    
    NSString *expireTime = paramDictionary[@"pay_expire"];
    if (expireTime.length > 0) {
        [resultArray setObject:expireTime forKey:@"expireTime"];
        [resultArray removeObjectForKey:@"pay_expire"];
    }
    
    // 移除一些不需要的字段
    [resultArray removeObjectForKey:@"partner_id"];
    [resultArray removeObjectForKey:@"partner_key"];
    [resultArray removeObjectForKey:@"out_no"];
    [resultArray removeObjectForKey:@"notify_url"];
    
    return [resultArray copy];
}

@end
