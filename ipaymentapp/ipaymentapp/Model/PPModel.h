//
//  PPModel.h
//  SAKCashier
//
//  Created by hong<PERSON><PERSON><PERSON> on 3/26/15.
//  Copyright (c) 2015 meituan. All rights reserved.
//

#import "SAKBaseModel.h"

extern NSString * _Nonnull const kInternalAPIpayInVIP; // 支付内部地址（http://pay-in.vip.sankuai.com）
extern NSString * _Nonnull const kInternalAPImpayVIP; // 支付内部地址 (http://mpay.vip.sankuai.com)

@class MTCPaymentRequest, CIPError, PPOrder;
@interface PPModel : SAKBaseModel

/**
 下单请求

 @param contentDictionay 请求参数字典
 @param finished 请求结束回调
 */
- (void)generatePayOrderWithContentDictionary:(NSDictionary * _Nonnull)contentDictionay
                                     finished:(void(^_Nullable)(MTCPaymentRequest * _Nullable paymentRequest, CIPError * _Nullable error))finished;
/**
 合单请求
 
 @param contentDictionay 请求参数字典
 @param finished 请求结束回调
 */
- (void)generateCombinePayOrderWithContentDictionary:(NSDictionary * _Nonnull)contentDictionay
                                            finished:(void(^_Nullable)(MTCPaymentRequest * _Nullable paymentRequest, CIPError * _Nullable error))finished;

- (void)fetchOrderListFinished:(void(^_Nullable)(NSArray * _Nullable orderArray, CIPError * _Nullable error))finished;

- (void)fetchRefundOrderListWithParamDictionary:(NSDictionary * _Nonnull)paramDic
                                       finished:(void(^ _Nullable)(NSDictionary * _Nullable responseDictionary, CIPError * _Nullable error ))finished;

- (void)refundOrderWithContentDictionary:(NSDictionary * _Nonnull)contentDictionay
                              partnerKey:(NSString * _Nonnull)partnerKey
                                Finished:(void(^ _Nullable)(CIPError * _Nullable error))finished;

- (void)refreshOrderWithContentDictionary:(NSDictionary * _Nonnull)contentDictionay
                               partnerKey:(NSString * _Nonnull)partnerKey
                                 finished:(void(^ _Nullable)(PPOrder * _Nullable order, CIPError * _Nullable error))finished;

- (void)generateHelloPayOrderWithContentDictionary:(NSDictionary * _Nonnull)contentDictionay
                                        partnerKey:(NSString * _Nonnull)partnerKey
                                          finished:(void(^ _Nullable)(MTCPaymentRequest * _Nullable paymentRequest, CIPError * _Nullable error))finished;

// 用于模拟付款码扫码支付
- (void)barcodeDemoMockPay:(NSString * _Nonnull)payCodeToken
                  finished:(void(^ _Nullable)(CIPError * _Nullable error))finished;
@end
