//
//  MTBaseViewController+DarkModeNavBarStyle.m
//  ipaymentapp
//
//  Created by <PERSON><PERSON>oyangyu<PERSON><PERSON> on 2020/7/3.
//  Copyright © 2020 Meituan.com. All rights reserved.
//

#import "MTBaseViewController+DarkModeNavBarStyle.h"
#import "SWLSwizzle.h"
#import "UIImage+METUIKit.h"
#import "UIImage+SAKUI.h"

@implementation MTBaseViewController (DarkModeNavBarStyle)

// 替换掉 METUIKit 中 Swizzling 的方法以解决暗黑模式下 MTBaseViewController 导航栏变白问题，待 METUIKit 适配后移除
swl_swizzleInstanceMethod([MTBaseViewController class], @selector(met_defaultNavBarColorViewDidLoad), @selector(darkModeNavBarColorViewDidLoad))

- (void)darkModeNavBarColorViewDidLoad
{
    [self darkModeNavBarColorViewDidLoad];
    if ([self.navigationController.viewControllers containsObject:self]) {
        if (@available(iOS 13.0, *)) {
            if (UITraitCollection.currentTraitCollection.userInterfaceStyle == UIUserInterfaceStyleDark) {
                [self.navigationController.navigationBar setBackgroundImage:[[UIImage sakui_imageWithColor:HEXCOLOR(0x000000)] sakui_resizableImage] forBarMetrics:UIBarMetricsDefault];
            } else {
                [self.navigationController.navigationBar setBackgroundImage:[[UIImage sakui_imageWithColor:HEXCOLOR(0xffffff)] sakui_resizableImage] forBarMetrics:UIBarMetricsDefault];
            }
        }
    }
}

@end
