//
//  PPProcessController.h
//  SAKCashier
//
//  Created by sunhl on 15/3/27.
//  Copyright (c) 2015年 meituan. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "PPOrder.h"

extern NSString * const kOfflinePartnerKey;
extern NSString * const kOnlinePartnerKey;
extern NSString * const kOfflineMerchantNO;
extern NSString * const kOnlineMerchantNO;
extern NSString * const kOnlineCreditMerchantNO;
extern NSString * const kOnlineCreditPartnerKey;

extern NSString * const kOfflineReChargeTypePartnerKey;
extern NSString * const kOnlineReChargeTypePartnerKey;
extern NSString * const kQaReChargeTypePartnerKey;
extern NSString * const kOfflineReChargeTypeMerchantNO;
extern NSString * const kOnlineReChargeTypeMerchantNO;
extern NSString * const kOnlineDCEPMerchantNO;


@class MTCPaymentRequest, CIPError, PPRefundedOrder;
@interface PPService : NSObject

+ (id)defaultInstance;

- (NSMutableArray *)getSubmitOrderContentArray;

- (void)generatePayOrderWithContentArray:(NSArray *)contentArray
                                finished:(void (^)(MTCPaymentRequest *paymentRequest, CIPError *error))finished;

// 合单支付 将支持发起合单支付请求
- (void)generateCombinePayOrderWithContentArray:(NSArray *)contentArray
                                       finished:(void (^)(MTCPaymentRequest *paymentRequest, CIPError *error))finished;

- (void)getOrderListFinished:(void(^)(NSArray *orderArray, CIPError *error))finished;

- (void)refundOrderWithOrder:(PPOrder *)order
                 extraParams:(NSDictionary *)extraParams
                    Finished:(void(^)(CIPError *error))finished;

- (void)refreshOrderWithOrder:(PPOrder *)order
                     finished:(void(^)(PPOrder *order, CIPError *error))finished;

// 用于模拟付款码扫码支付
- (void)barcodeDemoMockPay:(NSString *)payCodeToken
                  finished:(void(^)(CIPError *error))finished;

/**
 海螺下单接口

 @param contentArray 创建订单接口所需参数
 @param finished 请求回调
 */
- (void)generateHelloPayWithContentArray:(NSArray *)contentArray
                                finished:(void (^)(MTCPaymentRequest *paymentRequest, CIPError *error))finished;


/**
 根据 param 和 userid 获得用户退款列表
 
 @param param 请求参数
 @param finished 请求回调
 */
- (void)getRefundOrderListWithParam:(NSDictionary *)param finished:(void(^)(NSArray<PPRefundedOrder *> *orderList, CIPError *error))finished;

#pragma mark - 独立验密使用

/**
 验证密码服务接口所需参数，参照 getSubmitOrderContentArray 的实现
 
 @return 验证密码服务接口所需参数
 */
- (NSMutableArray *)getVerifyPayPasswordContentArray;

@end
