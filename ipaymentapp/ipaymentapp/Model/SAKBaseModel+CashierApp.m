//
//  SAKBaseModel+CashierApp.m
//  SAKCashier
//
//  Created by wangfeng on 15/4/30.
//  Copyright (c) 2015年 meituan. All rights reserved.
//

#import "SAKBaseModel+CashierApp.h"
#import "SAKBaseModel.h"
#import "EXTScope.h"
#import "SAKBaseModel_Private.h"
#import "CIPStringAdditions.h"

static NSString * const kHTTPMethods[] = {@"GET", @"POST", @"PUT", @"DELETE"};

@implementation SAKBaseModel (CashierApp)

- (MTHTTPRequestOperation *)postToCashierAppBackendPath:(NSString *)path
                                      withURLParameters:(NSDictionary *)URLParameterDictionary
                                         bodyParameters:(NSDictionary *)bodyParameterDictionary
                                               finished:(SAKModelRequestCallback)finishedCallback
{
    @weakify(self);

    return [self postToURL:path
         withURLParameters:URLParameterDictionary
            bodyParameters:bodyParameterDictionary
              dataEncoding:SAKModelDataJSONEncoding
                  finished:^(id data, NSURLRequest *request, CIPError *error) {
                      @strongify(self);
                      [self meituanPayBackendCommonCallback:data
                                                    request:request
                                                      error:error
                                                   callback:finishedCallback];
                  }];
}

- (void)meituanPayBackendCommonCallback:(id)object
                                request:(NSURLRequest *)request
                                  error:(CIPError *)error
                               callback:(SAKModelRequestCallback)callback
{
    if (error) {
        callback(nil, request, error);
        return;
    }
    callback(object, request, nil);
}

- (MTHTTPRequestOperation *)getFromCashierAppBackendPath:(NSString *)path
                                       withURLParameters:(NSDictionary *)URLParameterDictionary
                                                finished:(SAKModelRequestCallback)finishedCallback
{
    @weakify(self);
    return [self getFromURL:path
          withURLParameters:URLParameterDictionary
               dataEncoding:SAKModelDataJSONEncoding
                   finished:^(id data, NSURLRequest *request, CIPError *error) {
                       @strongify(self);
                       [self meituanPayBackendCommonCallback:data
                                                     request:request
                                                       error:error
                                                    callback:finishedCallback];
                   }];
}

- (NSMutableURLRequest *)assembleRequestURL:(NSString *)URLString
                                  viaMethod:(SAKModelRequestMethod)method
                          withURLParameters:(NSDictionary *)URLParameterDictionary
                             bodyParameters:(NSDictionary *)bodyParameterDictionary
                           commonParameters:(NSDictionary *)commonParametersDictionary
                             uploadingFiles:(NSArray *)uploadingFileArray
                              configuration:(NSDictionary *)configurationDictionary
                                    timeout:(NSTimeInterval)interval
{
    NSMutableDictionary *URLParameters = [self processURLParameters:URLParameterDictionary].mutableCopy;
    NSMutableDictionary *bodyParameters = nil;
    
    if ([URLString containsString:@"/cashier/gentransorder"] ||
        [URLString containsString:@"/demo/refund/apply"] ||
        [URLString containsString:@"/trade/querypay"] ||
        [URLString containsString:@"/noncashier/getthirdcashierurl"] ||
        [URLString containsString:@"/qdbapi/unifiedorder"] ||
        [URLString containsString:@"/cashier/gencombinetransorder"]) {
        bodyParameters = [bodyParameterDictionary mutableCopy];
    } else {
        if (self.autowire) {
            NSDictionary *queryParams = [[NSURL URLWithString:URLString].query cipf_dictionaryByParseInURLParameterFormat];
            [commonParametersDictionary enumerateKeysAndObjectsUsingBlock:^(id  _Nonnull key, id  _Nonnull obj, BOOL * _Nonnull stop) {
                if (![URLParameters valueForKey:key] && ![queryParams valueForKey:key]) {
                    [URLParameters setValue:obj forKey:key];
                }
            }];
        }
        
        bodyParameters = [bodyParameterDictionary mutableCopy];
        BOOL stripCorpse = [configurationDictionary[kSAKStripCorpse] boolValue];
        if (!stripCorpse && method == SAKModelRequestMethodPOST) {
            if (self.corpse) {
                [bodyParameters setValuesForKeysWithDictionary:self.corpse];
            } else {
                // 没有获取到指纹的情况
                [bodyParameters setValue:@"Mephisto" forKey:@"fingerprint"];
            }
        } else {
            // Do Nothing
        }
    }
    
    // 上传图片parameterEncoding为MTURLRequestMultiPartParameterEncoding，详情见IPHONE-405
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[self processURL:URLString]
                                                             viaMethod:kHTTPMethods[method]
                                                          headerFields:nil
                                                         URLParameters:URLParameters
                                                        bodyParameters:[self processBodyParameters:bodyParameters]
                                                        uploadingFiles:uploadingFileArray
                                                        stringEncoding:NSUTF8StringEncoding
                                                     parameterEncoding:uploadingFileArray ? MTURLRequestMultiPartParameterEncoding : self.parameterEncoding];
    
    if (interval > 0) {
        request.timeoutInterval = interval;
    }
    
    return request;
}

@end

