//
//  AppDelegate.m
//  ipaymentapp
//
//  Created by wangfeng on 15/6/23.
//  Copyright (c) 2015年 Meituan.com. All rights reserved.
//

#import "AppDelegate.h"

#import "SAKBaseModel.h"
#import "SAKEnvironment.h"
#import "SAKPortal.h"

#import "MTNavigationController.h"
#import "ipaymentapp-Swift.h"
#import "PPService.h"
#import "MTWWallet.h"
#import "MPCPaymentChannel.h"
#import "SPGObjectCacheManager.h"
#import "SAKAppInfo.h"
#import "SAKEnvironment.h"
#import "PPAppSelectViewController.h"
#import "PSPushService.h"
#import "TTWebViewStyleManager.h"
#import "TTBridgeManager.h"
#import "SAKHostSwitcherURLProtocol.h"
#import "PPDebugWindow.h"
#import "SAKStatistics.h"
#import "NVMonitorCenter.h"
#import "NVNetworkConfigurator.h"
#import "NVDebugNetworkAgent.h"
#import "SAKBaseModel+AppMock.h"
#import "PPTitansAdapter.h"
#import "VANProxyProtocol.h"
#import "SAKInnerAddrInterceptProtocol.h"
#import "MRDLocationManager.h"
#import "PicassoSDK.h"
#import "NVMApiConfig.h"
#import "UIImage+SAKColor.h"
#import "NSString+DPHeaderParam.h"
#import "NVLogger.h"
#import "SAKPaymentCommonConfigure.h"
#import "PPCrashReporter.h"
#import "SAKPaymentKit.h"
#import "SAKHorn.h"
#import "SAKMemoryLeakMonitor.h"
#import "NSObject+MLeaksFinderSwitch.h"
#import "SAKMetrics.h"
#import "SAKMetricsDebugConfig.h"
#import "MeituanPaySDK.h"
#import "NVNetworkService.h"
#import "PPRiskErrorProcessController.h"
#import "PPHostSwitcher.h"
#import "SAKDebugInitialManager.h"
//#import "MRMConfigService.h" // 更新推荐位 SDK 的开关
//#import "MRMPaymentParameter.h"
#import "NSDictionary+SPKShortcuts.h"
//#import "PicassoClient.h"
#import "MRDLocationManager.h"
#import <MRDLocationManager/MRDLocationConfigService.h>
#import "CIPABTestCenter.h"
#import "SAKGuardCommon.h"
#import "MRNConfigCenter.h"
#import "SAKAccountServiceConfigure.h"
#import <SAKFinBusiness/SAKFinBusiness.h>
#import "VANStateManager.h"
#import <QHOServiceManager/QHOServiceManager.h>
#import <CIPServiceRegistry/SAKSkyEyeInitProtocol.h>
#import "SAKSignInService.h"
#import "SAKOneClickDataManager.h"
#import "NVLinkerConfigurator.h"
#import <wechat/WXApi.h>
#import <wechat/CIPWxApiDelegateProxy.h>
//#import <SAKTitansAdapter/SAKTitansAdapter.h>
#import <SAKAccount/MTSignInViewController.h>
#import <CIPFoundation/NSMutableDictionary+CIPSafe.h>
#import <TitansProtocol/TTWebViewConfig.h>
#import <SAKEnvironment/SAKEnvironment.h>
#import <TitansProtocol/TTAdapterProtocol.h>
#import <SWLSwizzle.h>
#import <CIPPrivacy/METPrivacyMonitor.h>
#import <SDWebImage/SDWebImage.h>

static NSString *const METSeguePush = @"com.meituan.segue.push";
static NSString *const kPPAppPushPassWord = @"ipayment";
static const NSInteger kIpaymentAppCATAppID = 92;
//extern NSString *const kMETPermissionApprovedKey; // 隐私浏览模式

#ifdef DEBUG
static const BOOL isDebug = YES;
#else
static const BOOL isDebug = NO;
#endif

@interface AppDelegate () <PSPushServiceDelegate, TTAdapterProtocol>

@property (nonatomic, strong) UITabBarController *tabbarController;
@property (nonatomic, strong) PSPushService *pushService;

@end

@interface AppDelegate (SAKPortal) <SAKPortalDelegate>

@end

@implementation AppDelegate (SAKPortal)

- (void)showViewController:(UIViewController *)destinationViewController fromViewController:(UIViewController *)sourceViewController viaSegue:(NSString *)segue
{
    if ([segue isEqualToString:METSeguePush]) {
        [sourceViewController.navigationController pushViewController:destinationViewController animated:YES];
    }
}

- (MTBaseViewController *)currentViewController
{
    UITabBarController *tabbarController = self.tabbarController;
    MTBaseViewController *topViewController = (MTBaseViewController *)[(MTNavigationController *)tabbarController.selectedViewController topViewController];
    if (topViewController.presentedViewController) {
        return (MTBaseViewController *)topViewController.presentedViewController;
    } else {
        return topViewController;
    }
}

@end

@implementation AppDelegate

+ (void)setup
{
    // TODO
}

- (BOOL)application:(UIApplication *)application willFinishLaunchingWithOptions:(NSDictionary *)launchOptions
{
    __block PPRiskErrorProcessController *riskErrorProcessController;
    SAKRiskControlManager *manager = SAKRiskControlManager.sharedManager;
    @weakify(self);
    [[[manager.riskErrorStateSignal filter:^BOOL(SAKRiskError *value) {
        return value.code != SAKRiskErrorTypeNone && value.code != SAKRiskErrorTypeRestartNotRequired;
    }] deliverOnMainThread]
     subscribeNext:^(SAKRiskError *error) {
         @strongify(self);
         riskErrorProcessController = [[PPRiskErrorProcessController alloc] init];
         [riskErrorProcessController handleRiskError:error
                                    inViewController:self.tabbarController.selectedViewController
                                      withCompletion:^(NSNumber *x) {
                                          [manager.riskErrorProcessedCommand execute:x];
                                      }];
     }];
    return YES;
}

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
    
    // 0.开启 swizzle
    [SWLSwizzle loadMethodSwizzles];
    
    // 0.1 加载 Mantle，以初始化 MTLValueTransformer
    [NSValueTransformer loadMantle];
    
    // 0.2 关闭隐私浏览模式
    [METPrivacyMonitor sharedInstance].privacyMode = NO;
//    [[NSUserDefaults standardUserDefaults] setBool:YES forKey:kMETPermissionApprovedKey];
    
    // 1. 跳过 SAKSkyEye 断言
    [QHO_OPTIONAL_SERVICE_FOR(SAKSkyEyeInitProtocol) initiateInSimpleModeWithType:@"skyeye_test"];
    
    // 2. Shark
    [self setupData];

    // 3. 设置 SAKPortal 的 delegate
    [SAKPortal setDelegate:self];

    // 4. 初始化 PushService
    self.pushService = [[PSPushService alloc] initWithPassword:kPPAppPushPassWord launchOptions:launchOptions delegate:self isDebug:isDebug];

    // 5. 注册远程通知
//    [self setupNotification];
    // 初始化 SDMetWebImageInit
    SDMetWebImageInit;

    // 6. 杂项
    [SAKInnerAddrInterceptProtocol sharedInstance].shouldShowAlertView = NO;

    // 7. 注册 SAKHostSwitcherURLProtocol
    // 多个NSURLProtocol拦截顺序为注册顺序的反序，即后注册的的NSURLProtocol先拦截
    [NSURLProtocol registerClass:[VANProxyProtocol class]];  // Vane/PortM
    // @yangfeiyu 如果要走 PAPI Mock，直接打开注释，并修改 user 字段为你的 PAPI 平台 token 即可
//    NSDictionary *param = @{
//                            @"VANHostNameKey" : @"a.sankuai.com",
//                            @"user" : @"2a1f25f9f825b7d3b4a96ef235576ec6"
//                            };
//    [[VANStateManager defaultManager] startWorkingWithParameter:param];

    // 8. 配置 Titans
    [self setupTitans];

    // 9. 初始化主页面
    [self setupMainView];

    // 内存泄漏监控配置
    [self setupMemoryLeakMonitor];
    
#if TEST || DEBUG
    // 10. 配置AppMock
    [SAKBaseModel sak_fetchDebugType:^BOOL{
        return ([NVDebugNetworkAgent instance].currentDebugType == SKNetworkDebugMock);
    }];
    
    [self autoUITestRegist];

#endif

    // 12. 配置灵犀
    [self setupSAKStatistics];

    // 17. 配置 Horn
    [self setupHorn];

    // 配置推荐位 SDK 更新开关
    [self setupRecommendation];

    // 13. Performance
    [self setupPerformance];

    // 16. crash report 配置
    [self setupCrashReport];

    // 17. 初始化 SAKGuard
    [SAKGuardCommon init];

    // 初始化覆盖率统计
    [self setupCoverage];
    // 配置 MRN
    [self setup];
    
    // 支付 SDK 配置
    [self setupPaymentKitConfig];
    [self setupFinSDK];
//    PicassoClient.sharedClient.betaUrl = PPGlobalConstant.kSPKPicassoHTTPEnvironment ? @"" : @"https://mapi.51ping.com/mapi/picasso/queryjs.bin";
    
    // 主题色设置
    [PPAppSelectViewController setupAppThemeStyle];
    
    [[CIPABTestCenter defaultCenter] startEngine];
    [[CIPABTestCenter defaultCenter] pullDataFromRemote];

    // 14. 请求定位权限
    [[MRDLocationManager defaultManager] requestWhenInUseAuthorization];
    // 必须要做的配置：UTM 和 AuthKey
    [MRDLocationConfigService setAuthKey:@"2e51374d7a7848bf79c8bb2efb5f8acc"];  // 设置定位授权 Key
    [[MRDLocationManager defaultManager] configUUID:[NSString stringWithFormat:@"uuid:%@", [SAKEnvironment environment].UUID]
                                             userId:[NSString stringWithFormat:@"meituan:%@", [[SAKEnvironment environment].user.userID stringValue] ? : @"-1"]];
    [MRDLocationConfigService setDeviceidType:@"uuid"
                                   useridType:[NSString stringWithFormat:@"uuid:%@", [SAKEnvironment environment].UUID]]; // 配置 UTM 参数

    
    [[MRDLocationManager defaultManager] configClientID:@"PayDemo"];
//    [[MRDLocationManager defaultManager] configUUID:[NSString stringWithFormat:@"uuid:%@", [SAKEnvironment environment].UUID]
//                                             userId:[NSString stringWithFormat:@"meituan:%@", [[SAKEnvironment environment].user.userID stringValue] ? : @"-1"]];
    
//    [[MRDLocationManager defaultManager] initLocationServiceWithUUID:[NSString stringWithFormat:@"uuid:%@", [SAKEnvironment environment].UUID]
//                                                              userID:[NSString stringWithFormat:@"meituan:%@", [[SAKEnvironment environment].user.userID stringValue] ? : @"-1"]
//                                                            clientID:[SAKEnvironment environment].appInfo.type
//                                                             authKey:@"2e51374d7a7848bf79c8bb2efb5f8acc"
//                                                   requestCityIDType:MRDLocationCityIDRequestTypeMeituan
//                                                 networkServiceBlock:^(MRDLocationTask *locationTask) {
//                                                     SAKBaseModel *locationModel = [[SAKBaseModel alloc] init];
//                                                     if ([locationTask.method isEqualToString: @"GET"]) {
//                                                         [locationModel getFromURL:locationTask.urlString
//                                                                 withURLParameters:locationTask.urlParameters
//                                                                      dataEncoding:SAKModelDataJSONEncoding
//                                                                          finished:^(id data, NSURLRequest *request, CIPError *error) {
//                                                                              locationTask.finishBlock(data, request, error);
//                                                                          }];
//                                                     } else if ([locationTask.method isEqualToString:@"POST"]) {
//                                                         locationModel.parameterEncoding = MTURLRequestJSONParameterEncoding;
//                                                         [locationModel postToURL:locationTask.urlString
//                                                                withURLParameters:locationTask.urlParameters
//                                                                   bodyParameters:locationTask.bodyDictionary
//                                                                     dataEncoding:SAKModelDataJSONEncoding
//                                                                         finished:^(id data, NSURLRequest *request, CIPError *error) {
//                                                                             locationTask.finishBlock(data, request, error);
//                                                                         }];
//                                                     }
//                                                 }];


    [[MRDLocationManager defaultManager] startUpdatingLocation];
    [[[NSNotificationCenter defaultCenter] rac_addObserverForName:kMRDLocationUpdatedNotification object:nil] subscribeNext:^(NSNotification *notif) {
        MRDLocationManager *manager = (MRDLocationManager *)notif.object;
        [[MRDLocationManager defaultManager] reverseGeoLocation:manager.lastLocation timeout:15 finished:^(MTPlaceMark *placemark, NSError *error) {
            if (placemark) {
                [SAKEnvironment environment].city.cityID = [placemark.cityID intValue];
            }
        }];
    }];

    // SAKAccount 必须参数 https://km.sankuai.com/page/*********
    /// 5.87.2 及之后版本， shareConfigure 被废弃，统一使用 initConfig: 初始化
    SAKAccountServiceConfigure *config = [SAKAccountServiceConfigure new];
    config.joinKey = @"100151_1204293641";
    // SAKAccountServiceConfigure 其他配置项设置 ...
    [SAKAccountServiceConfigure initConfig:config];
    
    
    [self setupAccountAdapter];
    
    return YES;
}

- (BOOL)application:(UIApplication *)application openURL:(NSURL *)url sourceApplication:(NSString *)sourceApplication annotation:(id)annotation
{
    if (url) {
        BOOL isProcessed = NO;

        if ([MeituanPaySDK isFromMeituanPaySDKWithURL:url]) {
            isProcessed = [MeituanPaySDK meituanPaySDKHanldeApplication:application openURL:url];
        }

        if ([[MTWWallet defaultWallet] isPaymentCallBackURL:url]) {
            isProcessed = [[MTWWallet defaultWallet] processPaymentResultWithURL:url];
        }

        if (!isProcessed) {
            // do nothing
        }
    }
    return YES;
}

// iOS9及以上 使用下面的方法
- (BOOL)application:(UIApplication *)app openURL:(NSURL *)url options:(NSDictionary<UIApplicationOpenURLOptionsKey,id> *)options
{
    if (url) {
        BOOL isProcessed = NO;

        if ([MeituanPaySDK isFromMeituanPaySDKWithURL:url]) {
            isProcessed = [MeituanPaySDK meituanPaySDKHanldeApplication:app openURL:url];
        }
        
        if ([SAKFinBusiness isWalletPaymentCallBackURL:url]) {
            isProcessed = [SAKFinBusiness processPaymentResultWithWalletURL:url];
        }
//        if ([[MTWWallet defaultWallet] isPaymentCallBackURL:url]) {
//            isProcessed = [[MTWWallet defaultWallet] processPaymentResultWithURL:url];
//        }

        if (!isProcessed) {
            // url 跳转
            NSString *schemeString = [[url scheme] lowercaseString];
            
            if ([schemeString isEqualToString:@"meituanpayment"]) {
                [SAKPortal transferFromViewController:nil toURL:url completion:nil];
            }
        }
    }
    return YES;
}

- (void)application:(UIApplication *)application didRegisterForRemoteNotificationsWithDeviceToken:(NSData *)deviceToken
{
    NSMutableString *tokenString = [NSMutableString string];
    for (NSInteger i = 0, n = [deviceToken length]; i < n; i++) {
        unsigned int b = ((char *)[deviceToken bytes])[i];
        [tokenString appendFormat:@"%02x", (0xFF & b)];
    }
    if (tokenString.length == 0) {
        return;
    }

    [self.pushService setupAPNsToken:tokenString];
}

- (void)application:(UIApplication *)application didFailToRegisterForRemoteNotificationsWithError:(NSError *)error
{
    // TODO log error
}

- (void)application:(UIApplication *)application didReceiveRemoteNotification:(NSDictionary *)userInfo
{
    [self.pushService pushSDKHandleRemoteNotification:userInfo];
}

- (BOOL)application:(UIApplication *)application continueUserActivity:(NSUserActivity *)userActivity restorationHandler:(void (^)(NSArray<id<UIUserActivityRestoring>> * _Nullable))restorationHandler {
    if ([userActivity.activityType isEqualToString:NSUserActivityTypeBrowsingWeb]
        && userActivity.webpageURL
        && [userActivity.webpageURL.absoluteString hasPrefix:@"https://i.meituan.com/wechat-callback/"]) {
        return [WXApi handleOpenUniversalLink:userActivity delegate:[[CIPWxApiDelegateProxy alloc] init]];
    } else {
        // Others
        
    }
    return YES;
}

#pragma mark - SAKPortalDelegate

- (void)onRegistedResWithPushToken:(NSString *)pushToken
{
    if ([pushToken length]) {
        [SAKEnvironment environment].pushToken = pushToken;
    }
}

- (void)onRecvMessage:(PSPushMessage *)message
{
    // TODO use message
}

#pragma mark - Data

- (void)setupData
{
    [NVLogger installWithAppID:[NSString stringWithFormat:@"%@",@(kIpaymentAppCATAppID)] LoggerParams:^NSDictionary *{
        return @{@"unionId" : [[SAKEnvironment environment] UUID] ? : @""};
    }];
    [[SAKEnvironment environment] doStartupConfigurationIfNeeded];
    [SAKBaseModel setModelDelegate:[SAKEnvironment environment]];
    // 开启长连通道
    [SAKBaseModel setPersistentConnectStrategy:^BOOL{
        return YES;
    }];

    // 初始化频道
    id nb_appName = [[SPGObjectCacheManager sharedCacheManager] objectForKey:kPPAppSelectedKey];
    if (nb_appName && [nb_appName isKindOfClass:[NSString class]]) {
        [SAKEnvironment environment].appInfo.type = nb_appName;
    } else {
        [[SPGObjectCacheManager sharedCacheManager] saveObject:[SAKEnvironment environment].appInfo.type forKey:kPPAppSelectedKey];
    }

    // 添加 appid & unionid
    [[NVMonitorCenter defaultCenter] setAppID:kIpaymentAppCATAppID];
    [[NVMonitorCenter defaultCenter] setUnionIdBlock:^NSString *{
        return @"ipaymentapp_unionid";
    }];

    [NVLinkerConfigurator configurator].unionIDBlock = ^NSString * _Nullable{
        return [[SAKEnvironment environment] UUID];
    };
    
    [NVLinkerConfigurator configurator].appID = kIpaymentAppCATAppID;
    
    // Shark
    [NVNetworkConfigurator configurator].appId = kIpaymentAppCATAppID;

    [[NVNetworkConfigurator configurator] setupUnionid:^NSString *{
        return [[SAKEnvironment environment] UUID];
    }];
    
#if DEBUG || TEST
    [NVNetworkConfigurator configurator].networkAgent = [NVDebugNetworkAgent instance];
#endif
    [[NVNetworkService service] startWithConfig:[NVNetworkConfigurator configurator]];

    [[SAKEnvironment environment] setUnionIDObtainBlock:^NSString * _Nonnull{
        return [[SAKEnvironment environment] UUID];
    }];
    
    // 触发获取用户登录状态
    BOOL isUserAvailable = [SAKUserService sharedUserService].isUserAvailable;
}

#pragma mark - Notification
- (void)setupNotification
{
#if __IPHONE_OS_VERSION_MAX_ALLOWED >= 80000
#pragma deploymate push "ignored-api-availability"
    if ([[UIApplication sharedApplication] respondsToSelector:@selector(registerUserNotificationSettings:)]) {
        UIUserNotificationType types = UIUserNotificationTypeBadge |
        UIUserNotificationTypeSound | UIUserNotificationTypeAlert;
        UIUserNotificationSettings *mySettings =[UIUserNotificationSettings settingsForTypes:types categories:nil];
        [[UIApplication sharedApplication] registerUserNotificationSettings:mySettings];
        [[UIApplication sharedApplication] registerForRemoteNotifications];
    } else {
        [[UIApplication sharedApplication] registerForRemoteNotificationTypes:(UIRemoteNotificationTypeBadge | UIRemoteNotificationTypeSound | UIRemoteNotificationTypeAlert)];
    }
#pragma deploymate
#else
    [[UIApplication sharedApplication] registerForRemoteNotificationTypes: (UIRemoteNotificationTypeBadge | UIRemoteNotificationTypeSound | UIRemoteNotificationTypeAlert)];
#endif
}

#pragma mark - Titans

- (void)setupTitans
{
    // 简单配置 Titans， 详见：http://wiki.sankuai.com/pages/viewpage.action?pageId=664087623
    [PPTitansAdapter setup];
}

#pragma mark - UI

- (void)setupMainView
{
    self.tabbarController = [[UITabBarController alloc] init];

    PPSubmitOrderViewController *submitOrderViewController = [[PPSubmitOrderViewController alloc] init];
    submitOrderViewController.contentArray = [[PPService defaultInstance] getSubmitOrderContentArray];
    PPMineViewController *myviewController = [[PPMineViewController alloc] init];
    PPConfigViewController *configViewController = [[PPConfigViewController alloc] init];
    [[PPHostSwitcher sharedHostSwitcher] initDataWithContentInfo:submitOrderViewController.contentArray];
    MTNavigationController *homeNavController = [[MTNavigationController alloc] initWithRootViewController:submitOrderViewController];
    MTNavigationController *myNavController = [[MTNavigationController alloc] initWithRootViewController:myviewController];
    MTNavigationController *configNavController = [[MTNavigationController alloc] initWithRootViewController:configViewController];
    self.tabbarController.viewControllers = [NSArray arrayWithObjects:homeNavController, myNavController, configNavController, nil];

    UITabBar *tabBar = self.tabbarController.tabBar;
    UITabBarItem *tabItem1 = [tabBar.items objectAtIndex:0];
    UITabBarItem *tabItem2 = [tabBar.items objectAtIndex:1];
    UITabBarItem *tabItem3 = [tabBar.items objectAtIndex:2];

    tabItem1.title = @"下单";
    tabItem2.title = @"我的";
    tabItem3.title = @"设置";

    tabItem1.image = [UIImage imageNamed:@"icon_tabbar_homepage"];
    tabItem2.image = [UIImage imageNamed:@"icon_tabbar_mine"];
    tabItem3.image = [UIImage imageNamed:@"icon_tabbar_misc"];

    tabItem1.selectedImage = [[UIImage imageNamed:@"icon_tabbar_homepage_selected"] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
    tabItem2.selectedImage = [[UIImage imageNamed:@"icon_tabbar_mine_selected"] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
    tabItem3.selectedImage = [[UIImage imageNamed:@"icon_tabbar_misc_selected"] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];

    [[UITabBarItem appearance] setTitleTextAttributes:[NSDictionary dictionaryWithObjectsAndKeys:[UIColor colorWithRed:102/255.0 green:122/255.0 blue:143/255.0 alpha:1.0f], NSForegroundColorAttributeName, [UIFont systemFontOfSize:12], NSFontAttributeName, nil] forState:UIControlStateNormal];


    self.window = [[UIWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];

#if DEBUG || TEST
    // 9. 摇一摇 debug 弹窗
    [self setDebugWindow];
    [SAKDebugInitialManager sak_DebugInitialOperation];
#endif
    if (@available(iOS 13.0, *)) {
        tabBar.backgroundColor = [UIColor systemGray6Color];
        tabBar.translucent = NO;
    } else {
        tabBar.backgroundColor = [UIColor whiteColor];
    }
    self.window.rootViewController = self.tabbarController;
    [self.window makeKeyAndVisible];
}

#pragma mark - METDebugWindow

#if DEBUG || TEST

- (void)setDebugWindow
{
    PPDebugWindow *window = [[PPDebugWindow alloc] initWithFrame:[[UIScreen mainScreen] bounds]];
    window.detectsShake = YES;
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(deviceDidShake)
                                                 name:PPDebugWindowDidReceiveShakeNotification
                                               object:nil];
    self.window = window;
}

- (void)deviceDidShake
{
    UIViewController *presentingViewController = self.window.rootViewController;

    if (!presentingViewController.presentedViewController) {
        PPConfigViewController *configViewController = [[PPConfigViewController alloc] init];

        UINavigationController *naviVC = [[UINavigationController alloc] initWithRootViewController:configViewController];
        [presentingViewController presentViewController:naviVC
                                               animated:YES
                                             completion:nil];
    }
}

#endif

#pragma mark - 灵犀

- (void)setupSAKStatistics
{
//    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(registerParam:) name:SAKStatisticsRegisterCommonConfigNotification object:nil];
    [SAKStatistics initSAKStatistics:^(SAKStatisticsCommonConfig * _Nonnull commonConfig) {
        [self.class registerCommonConfig:commonConfig];
    }];
}

+ (void)registerCommonConfig:(SAKStatisticsCommonConfig *)commonConfig
{
    [commonConfig setGetAppType:^NSString * __nonnull{
        return @"pay-demo";
    }];
    [commonConfig setGetAppChannel:^NSString * __nonnull{
        return [SAKEnvironment environment].appInfo.channel;
    }];
    [commonConfig setGetCityID:^NSString * __nonnull{
        return [@([SAKEnvironment environment].city.cityID) stringValue];
    }];
    [commonConfig setGetUserID:^NSString * __nonnull{
        return [[SAKEnvironment environment].user.userID stringValue];
    }];
    [commonConfig setGetLoginType:^NSString * __nonnull{
        return [@([SAKEnvironment environment].user.loginType) stringValue];
    }];
//    [commonConfig setGetUUID:^NSString * _Nonnull{
//        return [SAKEnvironment environment].UUID;
//    }];
    [commonConfig setGetLocatedCityID:^NSNumber * _Nullable{
        return @([SAKEnvironment environment].city.cityID);
    }];
}

#pragma mark - Performance

- (void)setupPerformance
{
    [[SAKMetrics sharedInstance] startWithToken:@"593e8033c956203794a9bd97"];
    [SAKMetricsDebugConfig sharedConfig].showDashboard = NO;
    [SAKMetricsDebugConfig sharedConfig].compressRequestData = NO;
}

#pragma mark - init SAKPayment

- (void)setupPaymentKitConfig
{
    [SAKPaymentKit initSAKPaymentKit:^(SAKPaymentCommonConfigure *commonConfig) {
        [commonConfig configWithAppID:@(kIpaymentAppCATAppID) mapiID:@"payscope"];
//        [commonConfig setGetRecommendationView:^UIView *(NSDictionary *params){
//            NSString *transID = [params spk_stringForKey:@"trans_id"];
//            NSString *tradeno = [params spk_stringForKey:@"tradeno"];
//            NSString *finalMoney = [params spk_stringForKey:@"finalMoney"];
//            NSString *mode = [params spk_stringForKey:@"mode"];
//            NSString *requestID = [params spk_stringForKey:@"requestId"];
//            NSString *ext = [params spk_stringForKey:@"ext"];
//            NVAssert(transID.length > 0 || finalMoney.length > 0 , @"MRMPaymentParameter 参数为空,trans_id = %@,finalMoney = %@", transID, finalMoney);
//            MRMPaymentParameter *parameter = [[MRMPaymentParameter alloc] init];
//            parameter.positionKey = kMRMPayPopupPositionKey; // 支付页 - 支付成功弹窗
//            parameter.tradeno = tradeno;
//            parameter.trans_id = transID;
//            parameter.finalMoney = finalMoney;
//            parameter.mode = mode;
//            parameter.request_id = requestID;
//            parameter.ext = ext;
//            UIView *view = [MRMManager recommendationViewWithBusinessParameter:parameter completion:nil];
//            return view;
//        }];
        [commonConfig configUniversalLink:@"https://i.meituan.com/wechat-callback/"];
    }];
//    NSNumber *isPicassoSharkOn = [[SPGObjectCacheManager sharedCacheManager] objectForKey:PPGlobalConstant.kSPKPicassoSharkTrunOffState];
//    [SAKPaymentKit resetPicassoChannel:![isPicassoSharkOn boolValue]];
}

- (void)setupFinSDK
{
    SAKFinBusinessConfiguration *config = [[SAKFinBusinessConfiguration alloc] init];
    [config configWithAppID:@(kIpaymentAppCATAppID) mapiID:@"payscope"];
//    [config setGetRecommendationView:^UIView *(NSDictionary *params){
//        NSString *transID = [params spk_stringForKey:@"trans_id"];
//        NSString *tradeno = [params spk_stringForKey:@"tradeno"];
//        NSString *finalMoney = [params spk_stringForKey:@"finalMoney"];
//        NSString *mode = [params spk_stringForKey:@"mode"];
//        NSString *requestID = [params spk_stringForKey:@"requestId"];
//        NSString *ext = [params spk_stringForKey:@"ext"];
//        NVAssert(transID.length > 0 || finalMoney.length > 0 , @"MRMPaymentParameter 参数为空,trans_id = %@,finalMoney = %@", transID, finalMoney);
//        MRMPaymentParameter *parameter = [[MRMPaymentParameter alloc] init];
//        parameter.positionKey = kMRMPayPopupPositionKey; // 支付页 - 支付成功弹窗
//        parameter.tradeno = tradeno;
//        parameter.trans_id = transID;
//        parameter.finalMoney = finalMoney;
//        parameter.mode = mode;
//        parameter.request_id = requestID;
//        parameter.ext = ext;
//        UIView *view = [MRMManager recommendationViewWithBusinessParameter:parameter completion:nil];
//        return view;
//    }];
    
    [SAKFinBusiness setupSDKWithConfig:config];
//    NSNumber *isPicassoSharkOn = [[SPGObjectCacheManager sharedCacheManager] objectForKey:PPGlobalConstant.kSPKPicassoSharkTrunOffState];
//    [SAKFinBusiness configDownloadChannel: [isPicassoSharkOn boolValue] ? SFBDownloadChannelHTTP : SFBDownloadChannelShark];
}


// 配置推荐位 SDK
- (void)setupRecommendation
{
//    //线上方法
//    [[MRMConfigService defaultService] updateRecommendationRules];// 更新开关
//    if ([[SPGObjectCacheManager sharedCacheManager] objectForKey:PPGlobalConstant.kSPKShowRecommendationViewForced]) {
//        # if DEBUG || TEST
//        [[MRMConfigService defaultService] updateRecommendationRulesWithRule:METRecommendationRule_b]; // 强制返回参数的UI样式，METRecommendationRule_b标示平台样式，METRecommendationRule_a标示非平台推荐
//        # endif
//    }
}

//配置覆盖率统计
- (void)setupCoverage
{
    [[SPGObjectCacheManager sharedCacheManager] saveObject:@(YES) forKey:PPGlobalConstant.kSPKAutoReportCoverageOn];
    NSNumber *coverage = [[SPGObjectCacheManager sharedCacheManager] objectForKey:PPGlobalConstant.kSPKAutoReportCoverageOn];
    if(coverage){
        [[COVDataManager sharedManager] startOrStopToUpload: coverage.boolValue];
    }
}
#pragma mark - init Horn

- (void)setupHorn
{
    // 初始化 Horn
    SAKHornConfiguration *hornConfig = [[SAKHornConfiguration alloc] init];
    [hornConfig setGetUUIDBlock:^NSString * _Nonnull{
        return [SAKEnvironment environment].UUID;
    }];
    hornConfig.hornToken = @"593e8033c956203794a9bd97";
    [SAKHorn initHorn:hornConfig];

    [SAKHorn setEnableLog:NO];
    [SAKHorn setEnableMock:NO];
//    [SAKHorn setEnableDebug:YES];
    
    [SAKHorn registerType:@"pay_demo_config" callback:^(BOOL enable, NSString * _Nullable result) {
        NSLog(@"%@", result);
    }];
    
}

#pragma mark - crash

- (void)setupCrashReport
{
    [PPCrashReporter setupCrashReport];
}

#pragma mark - memory leaks monitor

- (void)setupMemoryLeakMonitor
{
    NSNumber *memoryLeakMonitor = [[SPGObjectCacheManager sharedCacheManager] objectForKey:[PPGlobalConstant kSPKMemoryLeakMonitorTrunOffState]];
    if (memoryLeakMonitor) {
        [SAKMemoryLeakMonitor turnOffMemoryLeakMonitor:memoryLeakMonitor.boolValue];
        MLeaksFinderSwitchTurnoff = memoryLeakMonitor.boolValue;
    }
}

#pragma mark - MRN setup

- (void)setup
{
    // 定义URL跳转的前缀，在imeituan中使用imeituan://www.meituan.com/
    [MRNConfigCenter defaultCenter].portalPrefix = ^NSString * {
        return @"meituanpayment://www.meituan.com/";
    };

    // 定义接收到URL跳转的处理方式，在imeituan中使用SAKPortal处理跳转URL
    [[MRNConfigCenter defaultCenter] setOpenURL:^(NSURL *URL, UIViewController *srcViewController) {
        [SAKPortal transferFromViewController:srcViewController
                                        toURL:URL
                                   completion:nil];
    }];

    [MRNConfigCenter defaultCenter].openURLWithCallback = ^(NSURL *URL, UIViewController *srcViewController, MRNTransferCompletionHandler completion, MRNOpenURLResult resultBlock) {
        [SAKPortal transferFromViewController:srcViewController
                                        toURL:URL
                           transferCompletion:completion
               resultCallbackAfterDisappeared:^(UIViewController *targetVC, SAKPortalResultCode code, id data) {
                   NVAssert([data isKindOfClass:NSString.class], @"setResult's data support string only");
                   CIPF_safe_block(resultBlock, code, data);
               }];
    };

    [MRNConfigCenter defaultCenter].EVAProjectName = @"meituan_payment";
    [[MRNConfigCenter defaultCenter] start];
}


- (void)autoUITestRegist
{
    if ([NSProcessInfo processInfo].environment[@"appmock"]) {
        NSString *uid = [NSProcessInfo processInfo].environment[@"uid"];
        NSInteger envid = [NSProcessInfo processInfo].environment[@"envid"].integerValue;
        
        if (envid > 0) {
            SAKOneClickDataManager *manager = [SAKOneClickDataManager sharedManager];
            [manager setDefaultEnvironmentId:envid]; // 2270 支付 QA
        }
        
        [[NVDebugNetworkAgent instance] handleMockDomain:[NSString stringWithFormat:@"https://appmock.sankuai.com/mw/register?_=0__0&uid=%@", uid]];
        [[NVDebugNetworkAgent instance] setupMockDomain:@"https://appmock.sankuai.com"];
        [[NVDebugNetworkAgent instance] switch2Domain:SKNetworkDebugMock];
    }
         
    if ([NSProcessInfo processInfo].environment[@"signin"]) {
        if(![[SAKUserService sharedUserService] isUserAvailable]) {
            NSString *account = [NSProcessInfo processInfo].environment[@"account"];
            NSString *password = [NSProcessInfo processInfo].environment[@"password"];
            NSString *countryCode = [NSProcessInfo processInfo].environment[@"countryCode"];
            SAKSignInService *service = [[SAKSignInService alloc] init];
            [service signInWithAccount:account password:password requestCode:nil responseCode:nil countryCode:countryCode userTicket:nil finished:^(SAKUser *user, CIPError *error) {
                if (error) {
                    [[SPKToastCenter defaultCenter] toastWithError:error];
                }
            }];

        }
    }
    
    if ([NSProcessInfo processInfo].environment[@"memLeakOff"]) {
        [[SPGObjectCacheManager sharedCacheManager] saveObject:@(YES) forKey:[PPGlobalConstant kSPKMemoryLeakMonitorTrunOffState]];
        [SAKMemoryLeakMonitor turnOffMemoryLeakMonitor:YES];
        MLeaksFinderSwitchTurnoff = YES;
    }
    
    if ([NSProcessInfo processInfo].environment[@"encyptParamOff"]) {
        [[SPGObjectCacheManager sharedCacheManager] saveObject:@"YES" forKey:[PPGlobalConstant kSPKSensitiveDataEncryptOff]];
    }
}

// 登录页适配层初始化
- (void)setupAccountAdapter
{
    // 用以更新Token
    [TTWebViewConfig shared].getTokenSignal = ^RACSignal *() {
        return [RACObserve([SAKEnvironment environment], user.token) deliverOnMainThread];
    };
    
    [[TTBridgeManager shared] setAccountLoginActionBlock:^(UIViewController *viewController, TTJSBridgeCallbackBlock callback, BOOL forceJump) {
        if (!forceJump && [SAKUserService sharedUserService].user && [SAKUserService sharedUserService].userAvailable) {
            [self loginSuccessWithCallback:callback];
            return;
        }
        
        MTSignInViewController *signInViewController = [[MTSignInViewController alloc] init];
        signInViewController.didSignInBlock = ^() {
            [self loginSuccessWithCallback:callback];
        };
        
        signInViewController.didClickDismissButtonBlock = ^() {
            if (callback) {
                callback(NO, nil, nil);
            }
        };
        
        MTNavigationController *navC = [[MTNavigationController alloc] initWithRootViewController:signInViewController];
        [viewController presentViewController:navC animated:YES completion:nil];
    }];
    
    [[TTBridgeManager shared] setAccountLogoutActionBlock:^(TTJSBridgeCallbackBlock callback, NSInteger reason, NSString *url) {
        [SAKUserService sharedUserService].logoutType = reason;
        [[SAKUserService sharedUserService] userWantLogout];
        if (callback) {
            callback(YES, nil, nil);
        }
    }];
}

- (void)loginSuccessWithCallback:(TTJSBridgeCallbackBlock)callback
{
    SAKUser *user = [SAKEnvironment environment].user;
    NSMutableDictionary *dict = [[NSMutableDictionary alloc] initWithCapacity:3];
    [dict cipf_safeSetObject:user.userID forKey:@"userId"];
    [dict cipf_safeSetObject:user.userName forKey:@"userName"];
    [dict cipf_safeSetObject:user.token forKey:@"userToken"];
    if (callback) {
        callback(YES, nil, dict);
    }
}

@end
