# 
# 自动切换并更新pod
# <AUTHOR> <EMAIL>
#

# app名称
app_name="ipaymentapp"

# 需要操作的pod名
pod_name_array=(
    "xxxxx占位xxxxx"
    "SAKPaymentKit" 
    "SAKMeituanPay"
    "SAKPaymentChannel"
    "SAKBarcodeCashier"
    "SAKCashier"
    "SAKWallet"
    "SAKWalletService"
    "SAKPaymentAccount"
)

# pod对应的文件夹名称
pod_folder_array=(
    "xxxxx占位xxxxx"
    "sakpaymentkit"
    "sakmeituanpay"
    "sakpaymentchannel"
    "sakbarcodecashier"
    "sakcashier"
    "sakwallet"
    "sakwalletservice"
    "sakpaymentaccount"
)

# pod对应的仓库地址
pod_git_array=(
    "xxxxx占位xxxxx"
    "ssh://*******************/ios/sakpaymentkit.git"
    "ssh://*******************/ios/sakmeituanpay.git"
    "ssh://*******************/ios/sakpaymentchannel.git"
    "ssh://*******************/ios/sakbarcodecashier.git"
    "ssh://*******************/ios/sakcashier.git"
    "ssh://*******************/ios/sakwallet.git"
    "ssh://*******************/payios/sakwalletservice.git"
    "ssh://*******************/payios/sakpaymentaccount.git"
)

# 功能实现

function show_switch_to()
{
    echo "执行的操作："
    echo "  1: 切本地"
    echo "  2: 切git分支"
    echo "  3: 切版本号"
    echo "  4: 更新指定pod"
    echo "  5: 更新Podfile版本号"
}

function show_pod_names()
{
    echo "\n输入要切换或更新的pod（多个空格隔开）："
    echo "  0: 下面全部pod"

    for ((i=1; i < ${#pod_name_array[*]}; ++i)); do
        echo "  $i: ${pod_name_array[$i]}"
    done
}

show_switch_to
read index_switch

# 更新本地Podfile版本号
if [ $index_switch == "5" ]; then
    echo "\n输入要同步的目标Podfile路径："
    read src_pod_path

    while read -r line; do
        if  echo $line | grep -q "pod \'"; then
            pod_name=${line#*pod \'}
            pod_name=${pod_name%%\'\, *}
            pod_name=${pod_name//\//\\/}

            pod_line=${line#*pod \'}
            pod_line=${pod_line%%, :configurations*}
            pod_line=${pod_line//\//\\/}

            sed -i '' "s/^.*pod \'$pod_name\'.*$/  pod \'$pod_line/" .Podfile.patch

            echo "update: $pod_name"
        fi
    done < $src_pod_path

    exit
fi

# 其它pod操作
show_pod_names
read -a index_array

if [ ${index_array[0]} == "0" ]; then
    for ((i=1; i < ${#pod_name_array[*]}; ++i)); do
        index_array[$i-1]=$i
    done
fi

if [ $index_switch == "1" ]; then
    for index in ${index_array[*]}; do
        pod_name=${pod_name_array[$index]}
        pod_folder=${pod_folder_array[$index]}

        if [ -d ../$pod_folder ]; then
            echo "pod '$pod_name', :path => '../$pod_folder/$pod_name.podspec'"
            sed -i '' "s/^.*pod \'$pod_name\'.*$/  pod '$pod_name', :path => '..\/$pod_folder\/$pod_name.podspec'/" .Podfile.patch
        else
            echo "找不到 $pod_name 的目录，将 $pod_name 和ipaymentapp放在同一个目录下"
            exit
        fi
    done
elif [ $index_switch == "2" ]; then
    echo "\n输入对应的分支名（多个空格隔开，如果相同输入一个）："
    read -a branch_name_array
    for ((i=0; i < ${#index_array[*]}; ++i)); do
        index=${index_array[$i]}
        if [ ${#branch_name_array[*]} == 1 ]; then
            branch_name=${branch_name_array[0]}
        else
            branch_name=${branch_name_array[$i]}
        fi
        pod_name=${pod_name_array[$index]}
        pod_git=${pod_git_array[$index]}

        echo "pod '$pod_name', :git => '$pod_git', :branch => '$branch_name'"
        tmp_branch_name=${branch_name//\//\\/}
        tmp_pod_git=${pod_git//\//\\/}
        sed -i '' "s/^.*pod \'$pod_name\'.*$/  pod '$pod_name', :git => '$tmp_pod_git', :branch => '$tmp_branch_name'/" .Podfile.patch
    done
elif [ $index_switch == "3" ]; then
    echo "\n输入对应各版本号（多个空格隔开，如果相同输入一个）："
    read -a version_array
    for ((i=0; i < ${#index_array[*]}; ++i)); do
        index=${index_array[$i]}
        if [ ${#version_array[*]} == 1 ]; then
            version_name=${version_array[0]}
        else
            version_name=${version_array[$i]}
        fi
        pod_name=${pod_name_array[$index]}
        pod_git=${pod_git_array[$index]}

        echo "pod '$pod_name', '$version_name'"
        sed -i '' "s/^.*pod \'$pod_name\'.*$/  pod '$pod_name', '$version_name'/" .Podfile.patch
    done
fi

for ((i=0; i < ${#index_array[*]}; ++i)); do
    index=${index_array[$i]}
    index_array[$i]=${pod_name_array[$index]}
done

echo "\npod update ${index_array[*]}\n"
pod update ${index_array[*]}

echo "\nReopen Xcode..."

killall Xcode
sleep 1
open $app_name.xcworkspace 
