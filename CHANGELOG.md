# Upcoming

## Breaking Change

## Feature

SAKCashier
=== 5.2.0 2019-01-03

* Feature:
* 去除旧版收银台代码
* 图片资源压缩
* 去除对SAKABTest的引用
* 修改部分bug
* 完善trace监控埋点

SAKPaymentAccount
=== 5.2.0 2019-01-03

* Feature:
* 一键绑卡D模式功能开发
* 优化支付设置页功能模块划分
* 删除无用图片资源，压缩图片资源
* 修复部分bug，优化页面展示用户体验
* 完善trace监控埋点

SAKPaymentChannel
=== 5.2.0 2019-01-03

* Feature:
* 修复微信支付线上Crash问题
* 去除银联支付
* 完善trace监控埋点

SAKMeituanPay
=== 5.2.0 2019-01-03

* Feature:
* 请求carbin的卡号位数由服务器下发
* 压缩图片资源
* 迁移CIPStorage
* 完善trace监控埋点
	
SAKPaymentKit
=== 5.2.0 2019-01-03

* Feature:
* 图片资源压缩
* 身份证样图不对齐问题修复
* Picasso告警处理
* 完善trace监控埋点
	
SAKPaymentGuard
=== 1.1.4 2019-01-03

* Feature:
* 迁移CIPStorage
	
SAKWallet
=== 5.2.0 2019-01-03

* Feature:
* 压缩图片资源
* 完善trace监控埋点
* 迁移CIPStorage

SAKWalletService
=== 2.1.2 2019-01-03

* Feature:
* 压缩图片资源

SAKBarCodeCashier
=== 5.2.0 2019-01-03

* Feature:
* 付款码收银台商户号维度配置服务化，增加透传字段
* 修复内存泄漏问题
* 压缩图片资源
* 完善trace监控埋点
* 迁移CIPStorage