#!/bin/bash

echo "正在处理.mcopilotignore文件..."

# 保存前二行内容
FIRST_THREE_LINES=$(head -n 2 .mcopilotignore)

# 直接重写.mcopilotignore文件，保留前二行
echo "$FIRST_THREE_LINES" > .mcopilotignore

echo "正在分析.Podfile.patch文件并更新.mcopilotignore..."

# 处理文件
cat .Podfile.patch | while read line; do
  # 跳过注释行（以#开头的行）
  if [[ $line =~ ^[[:space:]]*# ]]; then
    continue
  fi
  
  # 跳过空行
  if [[ -z $line ]]; then
    continue
  fi
  
  # 检查是否为pod行
  if [[ $line =~ ^[[:space:]]*pod[[:space:]] ]]; then
    # 提取pod名称
    POD_NAME=$(echo $line | awk -F"'" '{print $2}' | awk -F"," '{print $1}')
    
    # 检查是否为本地依赖（包含:path =>）
    if [[ $line == *":path =>"* ]]; then
      # 将本地依赖组件名转换为小写
      LOCAL_NAME=$(echo "$POD_NAME" | tr '[:upper:]' '[:lower:]')
      # 追加到.mcopilotignore文件
      echo "!../$LOCAL_NAME" >> .mcopilotignore
    elif [[ $line == *":git =>"* ]]; then
      # 追加远程依赖到.mcopilotignore文件
      echo "!./Pods/$POD_NAME" >> .mcopilotignore
    fi
  fi
done

echo "更新完成！"
echo "已将依赖组件追加到.mcopilotignore文件中"
