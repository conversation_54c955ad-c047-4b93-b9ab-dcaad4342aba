#!/bin/bash

# 设置输出文件
REMOTE_DEPS="remote_dependencies.txt"
LOCAL_DEPS="local_dependencies.txt"

# 清空输出文件（如果存在）
> $REMOTE_DEPS
> $LOCAL_DEPS

echo "正在分析.Podfile.patch文件..."

# 处理文件
cat .Podfile.patch | while read line; do
  # 跳过注释行（以#开头的行）
  if [[ $line =~ ^[[:space:]]*# ]]; then
    continue
  fi
  
  # 跳过空行
  if [[ -z $line ]]; then
    continue
  fi
  
  # 检查是否为pod行
  if [[ $line =~ ^[[:space:]]*pod[[:space:]] ]]; then
    # 提取pod名称
    POD_NAME=$(echo $line | awk -F"'" '{print $2}' | awk -F"," '{print $1}')
    
    # 检查是否为本地依赖（包含:path =>）
    if [[ $line == *":path =>"* ]]; then
      # 将本地依赖组件名转换为小写并保存
      echo "$POD_NAME" | tr '[:upper:]' '[:lower:]' >> $LOCAL_DEPS
    elif [[ $line == *":git =>"* ]]; then
      # 只保存组件名
      echo "$POD_NAME" >> $REMOTE_DEPS
    fi
  fi
done

# 统计数量
REMOTE_COUNT=$(wc -l < $REMOTE_DEPS)
LOCAL_COUNT=$(wc -l < $LOCAL_DEPS)

echo "分析完成！"
echo "发现 $REMOTE_COUNT 个远程依赖组件，已保存到 $REMOTE_DEPS"
echo "发现 $LOCAL_COUNT 个本地依赖组件（已转为小写），已保存到 $LOCAL_DEPS"
