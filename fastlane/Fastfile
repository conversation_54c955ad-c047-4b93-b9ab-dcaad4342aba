import_from_git(
  url: 'ssh://*******************/ios/hyperlane.git',
  path: 'fastlane/Fastfile_base',
  branch: 'master'
)

default_platform :ios

platform :ios do
  before_all do
    ENV['FASTLANE_SKIP_UPDATE_CHECK'] = 'YES'
    skip_docs
    opt_out_usage
    sh "git config core.ignorecase true"
  end
  
  desc "Build for 'ReleaseInhouse' configuration and/or upload to 抢鲜"
  override_lane :release_inhouse_build do |options|
    branch = (options[:branch] or 'master')
    app_group_value = (options[:app_group] or '')
    configuration = (options[:configuration] or "ReleaseInhouse")
    force_commit = (options[:force_commit] or true)
    export_opt = (options[:export_options] or {})
    metadata = workspace_metadata(workspace: options[:workspace])
    workspace = metadata['workspace']
    workspace_file = metadata['workspace_file']
    scheme = metadata['schemes'].first
    project = metadata['project']
    project_file = metadata['project_file']
    target = metadata['targets'].first
    entitlements_file = metadata['entitlements_file']
    badge_gravity = (options[:badge_gravity] or "North")
    perf_project = (options[:perf_project] or nil)
    
    qiangxian_url = 'https://apptest.sankuai.com'
    
    ENV['FASTLANE_XCODEBUILD_SETTINGS_TIMEOUT'] = '100'
    ENV['FASTLANE_XCODE_LIST_TIMEOUT'] = '100'
    if options[:xcproj] == true
      xcproj(
        xcodeproj: project_file
      )
    end
    version_number = get_version_number(
      xcodeproj: project_file
    )
    build_number = get_build_number(
      xcodeproj: project_file
    )
    unless options[:update_build_number] == false
      #ensure_git_status_clean
      ensure_git_branch(
        branch: branch
      )
      build_number = build_number.to_i + 1
      increment_build_number(
        build_number: build_number,
        xcodeproj: project_file
      )
      commit_version_bump(
        message: "MRE-20 Automatically bump build number to #{build_number}",
        xcodeproj: project_file,
        force: force_commit
      )
      push_to_git_remote(
        local_branch: branch,
        remote_branch: branch,
        tags: false
      )
    end
    if options[:remove_apple_pay] == true
      remove_apple_pay_settings(
        xcodeproj_path: project_file,
        entitlements_path: entitlements_file
      )
    end
    unless options[:badge] == false
      sakbadge(
        shield: "#{version_number}-#{build_number}-orange",
        no_badge: true,
        shield_gravity: badge_gravity
      )
    end
    sync_mobileprovisions(
      configuration: configuration
    )
    gym(
      workspace: workspace_file,
      scheme: scheme,
      configuration: configuration,
      output_directory: sandbox_path,
      output_name: "#{workspace}-#{version_number}-#{build_number}.ipa",
      archive_path: "#{sandbox_path}/#{workspace}-#{version_number}-#{build_number}.xcarchive",
      build_path: sandbox_path,
      buildlog_path: sandbox_path,
      derived_data_path: "#{sandbox_path}/DerivedData",
      export_method: 'enterprise',
      export_options: export_opt
    )

    
    app_group = 'ipaymentapp-stable-releaseinhouse'
   

    if(app_group_value != '') 
      app_group = app_group_value
    end

    unless options[:apptest] == false
      upload_apptest(
        app_display_name: get_ipa_info_plist_value(ipa: "#{sandbox_path}/#{workspace}-#{version_number}-#{build_number}.ipa", key: 'CFBundleDisplayName'),
        bundle_identifier: CredentialsManager::AppfileConfig.try_fetch_value(:app_identifier),
        bundle_version: version_number.to_s,
        build_number_string: build_number.to_s,
        ipa_path: "#{sandbox_path}/#{workspace}-#{version_number}-#{build_number}.ipa",
        app_group: app_group,
        base_url: qiangxian_url
      )
    end
    backup_xcarchive(
      xcarchive: "#{sandbox_path}/#{workspace}-#{version_number}-#{build_number}.xcarchive",
      destination: sandbox_path,
      versioned: false
    )
    url = upload_to_mss(
      bucket_name: "#{workspace}-release-inhouse-build",
      files_path: ["#{sandbox_path}/#{workspace}-#{version_number}-#{build_number}.xcarchive.zip"]
    )
    UI.success "You can download xcarchive from : #{url.first}"
    if perf_project
      upload_dsyms_to_perf(
        perf_project: perf_project,
        bundle_version: version_number,
        build_version: build_number.to_s,
        bundle_id: CredentialsManager::AppfileConfig.try_fetch_value(:app_identifier),
        xcarchive_path: "#{sandbox_path}/#{workspace}-#{version_number}-#{build_number}.xcarchive",
      )
      upload_linkmap_to_perf(
        perf_project: perf_project,
        bundle_version: version_number,
        derived_data_path: "#{sandbox_path}/DerivedData"
      )
    end
  end
end
